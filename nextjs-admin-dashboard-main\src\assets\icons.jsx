export function ChevronUpIcon(props) {
  return (
    <svg
      width={22}
      height={22}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.5 13.75L11 8.25L5.5 13.75"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function ChevronDownIcon(props) {
  return (
    <svg
      width={22}
      height={22}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5.5 8.25L11 13.75L16.5 8.25"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function HomeIcon(props) {
  return (
    <svg
      width={22}
      height={22}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.15722 19.7714V16.7047C8.1572 15.9246 8.79312 15.2908 9.58101 15.2856H12.4671C13.2587 15.2856 13.9005 15.9209 13.9005 16.7047V16.7047V19.7809C13.9003 20.4432 14.4343 20.9845 15.103 21H17.0271C18.9451 21 20.5 19.4607 20.5 17.5618V17.5618V8.83784C20.4898 8.09083 20.1355 7.38935 19.538 6.93303L12.9577 1.6853C11.8049 0.771566 10.1662 0.771566 9.01342 1.6853L2.46203 6.94256C1.86226 7.39702 1.50739 8.09967 1.5 8.84736V17.5618C1.5 19.4607 3.05488 21 4.97291 21H6.89696C7.58235 21 8.13797 20.4499 8.13797 19.7714V19.7714"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function UserIcon(props) {
  return (
    <svg
      width={22}
      height={22}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11 11C13.7614 11 16 8.76142 16 6C16 3.23858 13.7614 1 11 1C8.23858 1 6 3.23858 6 6C6 8.76142 8.23858 11 11 11Z"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21 21C21 17.134 16.5563 14 11 14C5.44365 14 1 17.134 1 21"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function CalendarIcon(props) {
  return (
    <svg
      width={22}
      height={22}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M3.09277 7.40729H18.9167"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.442 10.8677H15.4512"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.0045 10.8677H11.0137"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.55818 10.8677H6.56744"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.442 14.3188H15.4512"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.0045 14.3188H11.0137"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.55818 14.3188H6.56744"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.0433 1V4.29078"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.96515 1V4.29078"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.2383 3.57919H6.77096C4.83427 3.57919 3.5 4.94523 3.5 6.85941V15.5171C3.5 17.4313 4.83427 18.7974 6.77096 18.7974H15.2383C17.175 18.7974 18.5 17.4313 18.5 15.5171V6.85941C18.5 4.94523 17.175 3.57919 15.2383 3.57919Z"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function SettingsIcon(props) {
  return (
    <svg
      width={22}
      height={22}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.8067 7.62361L20.1842 6.54352C19.6577 5.62966 18.4907 5.31426 17.5755 5.83866V5.83866C17.1399 6.09528 16.6201 6.16821 16.1307 6.04106C15.6413 5.91391 15.2226 5.59746 14.9668 5.16131C14.8023 4.88409 14.7139 4.56833 14.7105 4.24598V4.24598C14.7254 3.72916 14.5304 3.22834 14.17 2.85761C13.8096 2.48688 13.3145 2.2778 12.7975 2.27802H11.5435C11.0369 2.27802 10.5513 2.47618 10.194 2.8335C9.83666 3.19082 9.63849 3.67641 9.63849 4.18295V4.18295C9.62405 5.23693 8.77438 6.07681 7.72084 6.0767C7.39849 6.07329 7.08273 5.98488 6.80551 5.82035V5.82035C5.89001 5.29467 4.72129 5.60907 4.19561 6.52457L3.53025 7.62361C3.00458 8.53911 3.31898 9.70783 4.23448 10.2335V10.2335C4.82214 10.574 5.18114 11.2056 5.18114 11.889C5.18114 12.5725 4.82214 13.2041 4.23448 13.5446V13.5446C3.31898 14.0703 3.00458 15.239 3.53025 16.1545L4.16870 17.2346C4.41258 17.6798 4.82035 18.0083 5.31692 18.1474C5.81348 18.2866 6.34525 18.2249 6.79978 17.9758V17.9758C7.23513 17.7211 7.75083 17.6482 8.23606 17.7754C8.72129 17.9025 9.13721 18.2190 9.39213 18.6551C9.55666 18.9323 9.64507 19.2481 9.64848 19.5704V19.5704C9.64848 20.6235 10.4904 21.4655 11.5435 21.4655H12.7975C13.8506 21.4655 14.6925 20.6235 14.6925 19.5704V19.5704C14.6969 19.2481 14.7853 18.9323 14.9498 18.6551C15.2047 18.2190 15.6206 17.9025 16.1059 17.7754C16.5911 17.6482 17.1068 17.7211 17.5422 17.9758V17.9758C18.4577 18.5015 19.6264 18.1871 20.1521 17.2716L20.8067 16.1545C21.0618 15.7188 21.1318 15.1986 21.0312 14.7055C20.9305 14.2125 20.6676 13.7707 20.2934 13.4596V13.4596C19.9192 13.1484 19.6563 12.7066 19.5556 12.2136C19.455 11.7205 19.525 11.2003 19.7801 10.7646C19.9446 10.4874 20.1851 10.2652 20.4731 10.1244C20.7611 9.98358 21.0831 9.92993 21.4035 9.97175V9.97175C21.9203 9.98619 22.4211 9.79103 22.7918 9.43076C23.1625 9.0705 23.3716 8.57541 23.3714 8.05859V6.80466C23.3714 6.29813 23.1732 5.81254 22.8159 5.45522C22.4586 5.09789 21.973 4.89973 21.4665 4.89973H21.4665C21.1441 4.90314 20.8284 4.81473 20.5511 4.65020C20.2739 4.48567 20.0517 4.24518 19.9109 3.95718C19.8101 3.46411 19.8801 2.94391 20.1352 2.50816C20.3903 2.0724 20.8132 1.75067 21.3063 1.65002V1.65002C21.8231 1.63558 22.3239 1.44042 22.6946 1.08015C23.0653 0.719878 23.2744 0.224791 23.2742 -0.292025V-1.54595"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle
        cx="12.1747"
        cy="11.8891"
        r="3.63616"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
