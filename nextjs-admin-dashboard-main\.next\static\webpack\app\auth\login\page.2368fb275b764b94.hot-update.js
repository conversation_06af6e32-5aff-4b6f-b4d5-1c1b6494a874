"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/components/Auth/AuthModal.jsx":
/*!*******************************************!*\
  !*** ./src/components/Auth/AuthModal.jsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.jsx\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/toast */ \"(app-pages-browser)/./src/utils/toast.js\");\n/* harmony import */ var _components_UI_Modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/UI/Modal */ \"(app-pages-browser)/./src/components/UI/Modal.jsx\");\n/* harmony import */ var _components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/FormElements/InputGroup */ \"(app-pages-browser)/./src/components/FormElements/InputGroup/index.tsx\");\n/* harmony import */ var _components_FormElements_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/FormElements/select */ \"(app-pages-browser)/./src/components/FormElements/select.tsx\");\n/* harmony import */ var _components_FormElements_InputGroup_text_area__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/FormElements/InputGroup/text-area */ \"(app-pages-browser)/./src/components/FormElements/InputGroup/text-area.tsx\");\n/* harmony import */ var _components_FormElements_Button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FormElements/Button */ \"(app-pages-browser)/./src/components/FormElements/Button.jsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst AuthModal = (param)=>{\n    let { isOpen, onClose } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login, setUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [isLogin, setIsLogin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [verificationSent, setVerificationSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        phoneNumber: \"\",\n        firstName: \"\",\n        lastName: \"\",\n        role: \"user\",\n        otp: \"\",\n        businessName: \"\",\n        businessCategory: \"\",\n        businessDescription: \"\",\n        businessAddress: \"\",\n        city: \"\",\n        state: \"\",\n        zipCode: \"\",\n        country: \"\"\n    });\n    const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || \"http://localhost:3000\";\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n        setError(\"\");\n    };\n    const resetForm = ()=>{\n        setFormData({\n            email: \"\",\n            password: \"\",\n            phoneNumber: \"\",\n            firstName: \"\",\n            lastName: \"\",\n            role: \"user\",\n            otp: \"\",\n            businessName: \"\",\n            businessCategory: \"\",\n            businessDescription: \"\",\n            businessAddress: \"\",\n            city: \"\",\n            state: \"\",\n            zipCode: \"\",\n            country: \"\"\n        });\n        setError(\"\");\n        setVerificationSent(false);\n        setIsLogin(true);\n    };\n    const handleClose = ()=>{\n        resetForm();\n        onClose();\n    };\n    // const handleSubmit = async (e) => {\n    //   e.preventDefault();\n    //   setError(\"\");\n    //   setLoading(true);\n    //   try {\n    //     if (verificationSent) {\n    //       // OTP Verification\n    //       const endpoint =\n    //         formData.role === \"business_owner\"\n    //           ? \"/api/auth/verifyAndCreateBusinessOwner\"\n    //           : \"/api/auth/verifyAndCreateUser\";\n    //       console.log(\"Sending OTP for verification:\", formData.otp);\n    //       const response = await fetch(`${BACKEND_URL}${endpoint}`, {\n    //         method: \"POST\",\n    //         headers: { \"Content-Type\": \"application/json\" },\n    //         credentials: \"include\",\n    //         body: JSON.stringify({ otp: formData.otp }),\n    //       });\n    //       const data = await response.json();\n    //       console.log(\"OTP Verification Response:\", data);\n    //       if (!response.ok)\n    //         throw new Error(data.message || \"OTP verification failed\");\n    //       const token = data.data?.token || data.token;\n    //       if (!token) throw new Error(\"No token received from server\");\n    //       const userData =\n    //         data.data?.businessOwner || data.data?.user || data.user;\n    //       setUser(userData);\n    //       localStorage.setItem(\"token\", token);\n    //       showSuccessToast(\"Account verified successfully!\");\n    //       // Check if the user is a business owner\n    //       if (userData.role === \"business_owner\") {\n    //         console.log(\"Navigating to business profile for role:\", userData.role);\n    //         router.push(\"/business/dashboard\");\n    //       } else if (userData.role === \"admin\") {\n    //         console.log(\"Navigating to admin dashboard for role:\", userData.role);\n    //         router.push(\"/admin/dashboard\");\n    //       } else {\n    //         console.log(\"Navigating to home for role:\", userData.role);\n    //         router.push(\"/\");\n    //       }\n    //       handleClose();\n    //     } else if (isLogin) {\n    //       // Login\n    //       const endpoint =\n    //         formData.role === \"business_owner\"\n    //           ? \"/api/auth/businessOwnerLogin\"\n    //           : \"/api/auth/login\";\n    //       const payload = { email: formData.email, password: formData.password };\n    //       console.log(\"Login Endpoint:\", endpoint);\n    //       console.log(\"Login Payload:\", payload);\n    //       console.log(\"Selected Role:\", formData.role);\n    //       const response = await fetch(`${BACKEND_URL}${endpoint}`, {\n    //         method: \"POST\",\n    //         headers: { \"Content-Type\": \"application/json\" },\n    //         credentials: \"include\",\n    //         body: JSON.stringify(payload),\n    //       });\n    //       const data = await response.json();\n    //       console.log(\"Login Response:\", data);\n    //       if (!response.ok) throw new Error(data.message || \"Login failed\");\n    //       const token = data.data?.token || data.token;\n    //       if (!token) throw new Error(\"No token received from server\");\n    //       const userData =\n    //         data.data?.businessOwner || data.data?.user || data.user;\n    //       setUser(userData);\n    //       localStorage.setItem(\"token\", token);\n    //       showSuccessToast(`Welcome back, ${userData.firstName || userData.ownerFirstName || 'User'}!`);\n    //       // Check if the user is a business owner\n    //       if (userData.role === \"business_owner\") {\n    //         console.log(\"Navigating to business profile for role:\", userData.role);\n    //         router.push(\"/business/dashboard\");\n    //       } else if (userData.role === \"admin\") {\n    //         console.log(\"Navigating to admin dashboard for role:\", userData.role);\n    //         router.push(\"/admin/dashboard\");\n    //       } else {\n    //         console.log(\"Navigating to home for role:\", userData.role);\n    //         router.push(\"/\");\n    //       }\n    //       handleClose();\n    //     } else {\n    //       // Registration\n    //       const endpoint =\n    //         formData.role === \"business_owner\"\n    //           ? \"/api/auth/registerBusinessOwner\"\n    //           : \"/api/auth/register\";\n    //       const payload =\n    //         formData.role === \"business_owner\"\n    //           ? {\n    //               email: formData.email,\n    //               password: formData.password,\n    //               phoneNumber: formData.phoneNumber,\n    //               ownerFirstName: formData.firstName,\n    //               ownerLastName: formData.lastName,\n    //               businessName: formData.businessName,\n    //               businessCategory: formData.businessCategory,\n    //               businessDescription: formData.businessDescription,\n    //               businessAddress: formData.businessAddress,\n    //               city: formData.city,\n    //               state: formData.state,\n    //               zipCode: formData.zipCode,\n    //               country: formData.country,\n    //             }\n    //           : {\n    //               email: formData.email,\n    //               password: formData.password,\n    //               phoneNumber: formData.phoneNumber,\n    //               firstName: formData.firstName,\n    //               lastName: formData.lastName,\n    //             };\n    //       const response = await fetch(`${BACKEND_URL}${endpoint}`, {\n    //         method: \"POST\",\n    //         headers: { \"Content-Type\": \"application/json\" },\n    //         credentials: \"include\",\n    //         body: JSON.stringify(payload),\n    //       });\n    //       const data = await response.json();\n    //       console.log(\"Registration Response:\", data);\n    //       if (!response.ok)\n    //         throw new Error(data.message || \"Registration failed\");\n    //       showSuccessToast(\"Registration successful! Please verify your email with the OTP sent to your email address.\");\n    //       setVerificationSent(true);\n    //     }\n    //   } catch (err) {\n    //     setError(err.message);\n    //     showErrorToast(err.message || \"An error occurred\");\n    //     console.error(\"Auth Error:\", err);\n    //   } finally {\n    //     setLoading(false);\n    //   }\n    // };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setLoading(true);\n        try {\n            if (verificationSent) {\n                var _data_data, _data_data1, _data_data2;\n                // OTP Verification\n                const endpoint = formData.role === \"business_owner\" ? \"/api/auth/verifyAndCreateBusinessOwner\" : \"/api/auth/verifyAndCreateUser\";\n                console.log(\"Verification Endpoint:\", endpoint, \"OTP:\", formData.otp, \"Role:\", formData.role);\n                const response = await fetch(\"\".concat(BACKEND_URL).concat(endpoint), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify({\n                        otp: formData.otp\n                    })\n                });\n                const data = await response.json();\n                console.log(\"OTP Verification Response:\", data);\n                if (!response.ok) throw new Error(data.message || \"OTP verification failed\");\n                const token = ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.token) || data.token;\n                if (!token) throw new Error(\"No token received from server\");\n                const userData = ((_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.businessOwner) || ((_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.user) || data.user;\n                setUser(userData);\n                localStorage.setItem(\"token\", token);\n                (0,_utils_toast__WEBPACK_IMPORTED_MODULE_4__.showSuccessToast)(\"Account verified successfully!\");\n                if (userData.role === \"business_owner\") {\n                    console.log(\"Navigating to business profile for role:\", userData.role);\n                    router.push(\"/business/dashboard\");\n                } else if (userData.role === \"admin\") {\n                    console.log(\"Navigating to admin dashboard for role:\", userData.role);\n                    router.push(\"/admin/dashboard\");\n                } else {\n                    console.log(\"Navigating to home for role:\", userData.role);\n                    router.push(\"/\");\n                }\n                handleClose();\n            } else if (isLogin) {\n                var _data_data3, _data_data4, _data_data5;\n                // Login\n                const endpoint = formData.role === \"business_owner\" ? \"/api/auth/businessOwnerLogin\" : \"/api/auth/login\";\n                const payload = {\n                    email: formData.email,\n                    password: formData.password\n                };\n                console.log(\"Login Endpoint:\", endpoint, \"Payload:\", payload, \"Role:\", formData.role);\n                const response = await fetch(\"\".concat(BACKEND_URL).concat(endpoint), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(payload)\n                });\n                const data = await response.json();\n                console.log(\"Login Response:\", data);\n                if (!response.ok) throw new Error(data.message || \"Login failed\");\n                const token = ((_data_data3 = data.data) === null || _data_data3 === void 0 ? void 0 : _data_data3.token) || data.token;\n                if (!token) throw new Error(\"No token received from server\");\n                const userData = ((_data_data4 = data.data) === null || _data_data4 === void 0 ? void 0 : _data_data4.businessOwner) || ((_data_data5 = data.data) === null || _data_data5 === void 0 ? void 0 : _data_data5.user) || data.user;\n                setUser(userData);\n                localStorage.setItem(\"token\", token);\n                (0,_utils_toast__WEBPACK_IMPORTED_MODULE_4__.showSuccessToast)(\"Welcome back, \".concat(userData.firstName || userData.ownerFirstName || 'User', \"!\"));\n                if (userData.role === \"business_owner\") {\n                    console.log(\"Navigating to business profile for role:\", userData.role);\n                    router.push(\"/business/dashboard\");\n                } else if (userData.role === \"admin\") {\n                    console.log(\"Navigating to admin dashboard for role:\", userData.role);\n                    router.push(\"/admin/dashboard\");\n                } else {\n                    console.log(\"Navigating to home for role:\", userData.role);\n                    router.push(\"/\");\n                }\n                handleClose();\n            } else {\n                // Registration\n                const endpoint = formData.role === \"business_owner\" ? \"/api/auth/registerBusinessOwner\" : \"/api/auth/register\";\n                const payload = formData.role === \"business_owner\" ? {\n                    email: formData.email,\n                    password: formData.password,\n                    phoneNumber: formData.phoneNumber,\n                    ownerFirstName: formData.firstName,\n                    ownerLastName: formData.lastName,\n                    businessName: formData.businessName,\n                    businessCategory: formData.businessCategory,\n                    businessDescription: formData.businessDescription,\n                    businessAddress: formData.businessAddress,\n                    city: formData.city,\n                    state: formData.state,\n                    zipCode: formData.zipCode,\n                    country: formData.country\n                } : {\n                    email: formData.email,\n                    password: formData.password,\n                    phoneNumber: formData.phoneNumber,\n                    firstName: formData.firstName,\n                    lastName: formData.lastName\n                };\n                console.log(\"Registration Endpoint:\", endpoint, \"Payload:\", payload, \"Role:\", formData.role);\n                const response = await fetch(\"\".concat(BACKEND_URL).concat(endpoint), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(payload)\n                });\n                const data = await response.json();\n                console.log(\"Registration Response:\", data);\n                if (!response.ok) throw new Error(data.message || \"Registration failed\");\n                (0,_utils_toast__WEBPACK_IMPORTED_MODULE_4__.showSuccessToast)(\"Registration successful! Please verify your email with the OTP sent to your email address.\");\n                setVerificationSent(true);\n            }\n        } catch (err) {\n            setError(err.message);\n            (0,_utils_toast__WEBPACK_IMPORTED_MODULE_4__.showErrorToast)(err.message || \"An error occurred\");\n            console.error(\"Auth Error:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const roleOptions = [\n        {\n            value: \"user\",\n            label: \"User\"\n        },\n        {\n            value: \"business_owner\",\n            label: \"Business Owner\"\n        }\n    ];\n    const businessCategories = [\n        {\n            value: \"Cleaning\",\n            label: \"Cleaning\"\n        },\n        {\n            value: \"Repair & Maintenance\",\n            label: \"Repair & Maintenance\"\n        },\n        {\n            value: \"Home & Garden\",\n            label: \"Home & Garden\"\n        },\n        {\n            value: \"Health & Wellness\",\n            label: \"Health & Wellness\"\n        },\n        {\n            value: \"Technology\",\n            label: \"Technology\"\n        },\n        {\n            value: \"Other\",\n            label: \"Other\"\n        }\n    ];\n    const getModalTitle = ()=>{\n        if (verificationSent) return \"Verify OTP\";\n        return isLogin ? \"Login\" : \"Register\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Modal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        isOpen: isOpen,\n        onClose: handleClose,\n        title: getModalTitle(),\n        size: \"lg\",\n        className: \"max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-lg bg-red-50 p-4 text-red-700 dark:bg-red-900/20 dark:text-red-400\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                    lineNumber: 402,\n                    columnNumber: 11\n                }, undefined),\n                !verificationSent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                    label: \"Role\",\n                    items: roleOptions,\n                    value: formData.role,\n                    onChange: handleChange,\n                    name: \"role\",\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                    lineNumber: 408,\n                    columnNumber: 11\n                }, undefined),\n                verificationSent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    label: \"Enter OTP\",\n                    type: \"text\",\n                    name: \"otp\",\n                    placeholder: \"Enter OTP\",\n                    value: formData.otp,\n                    handleChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                    lineNumber: 419,\n                    columnNumber: 11\n                }, undefined) : !isLogin ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-6 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            label: \"First Name\",\n                            type: \"text\",\n                            name: \"firstName\",\n                            placeholder: \"First Name\",\n                            value: formData.firstName,\n                            handleChange: handleChange,\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            label: \"Last Name\",\n                            type: \"text\",\n                            name: \"lastName\",\n                            placeholder: \"Last Name\",\n                            value: formData.lastName,\n                            handleChange: handleChange,\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                            lineNumber: 439,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                label: \"Phone Number\",\n                                type: \"tel\",\n                                name: \"phoneNumber\",\n                                placeholder: \"Phone Number\",\n                                value: formData.phoneNumber,\n                                handleChange: handleChange,\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                lineNumber: 449,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                            lineNumber: 448,\n                            columnNumber: 13\n                        }, undefined),\n                        formData.role === \"business_owner\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        label: \"Business Name\",\n                                        type: \"text\",\n                                        name: \"businessName\",\n                                        placeholder: \"Business Name\",\n                                        value: formData.businessName,\n                                        handleChange: handleChange,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    label: \"Business Category\",\n                                    items: businessCategories,\n                                    value: formData.businessCategory,\n                                    onChange: handleChange,\n                                    name: \"businessCategory\",\n                                    placeholder: \"Select Category\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    label: \"Business Address\",\n                                    type: \"text\",\n                                    name: \"businessAddress\",\n                                    placeholder: \"Business Address\",\n                                    value: formData.businessAddress,\n                                    handleChange: handleChange,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    label: \"City\",\n                                    type: \"text\",\n                                    name: \"city\",\n                                    placeholder: \"City\",\n                                    value: formData.city,\n                                    handleChange: handleChange,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    label: \"State\",\n                                    type: \"text\",\n                                    name: \"state\",\n                                    placeholder: \"State\",\n                                    value: formData.state,\n                                    handleChange: handleChange,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    label: \"Zip Code\",\n                                    type: \"text\",\n                                    name: \"zipCode\",\n                                    placeholder: \"Zip Code\",\n                                    value: formData.zipCode,\n                                    handleChange: handleChange,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    label: \"Country\",\n                                    type: \"text\",\n                                    name: \"country\",\n                                    placeholder: \"Country\",\n                                    value: formData.country,\n                                    handleChange: handleChange,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup_text_area__WEBPACK_IMPORTED_MODULE_8__.TextAreaGroup, {\n                                        label: \"Business Description\",\n                                        placeholder: \"Describe your business and services...\",\n                                        value: formData.businessDescription,\n                                        onChange: handleChange,\n                                        name: \"businessDescription\",\n                                        rows: 4\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                    lineNumber: 429,\n                    columnNumber: 11\n                }, undefined) : null,\n                !verificationSent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            label: \"Email\",\n                            type: \"email\",\n                            name: \"email\",\n                            placeholder: \"Email\",\n                            value: formData.email,\n                            handleChange: handleChange,\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                            lineNumber: 544,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_InputGroup__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            label: \"Password\",\n                            type: \"password\",\n                            name: \"password\",\n                            placeholder: \"Password\",\n                            value: formData.password,\n                            handleChange: handleChange,\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                            lineNumber: 553,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_Button__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    type: \"submit\",\n                    loading: loading,\n                    className: \"w-full\",\n                    size: \"lg\",\n                    children: verificationSent ? \"Verify OTP\" : isLogin ? \"Login\" : \"Register\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                    lineNumber: 565,\n                    columnNumber: 9\n                }, undefined),\n                !verificationSent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full border-t border-gray-300 dark:border-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex justify-center text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-white px-2 text-gray-500 dark:bg-gray-dark dark:text-gray-400\",\n                                        children: \"Or continue with\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                            lineNumber: 576,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_Button__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        window.open(\"\".concat(BACKEND_URL, \"/api/auth/google\"), \"_self\");\n                                    },\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mr-2 h-5 w-5 text-red-500\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Google\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormElements_Button__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        window.open(\"\".concat(BACKEND_URL, \"/api/auth/facebook\"), \"_self\");\n                                    },\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mr-2 h-5 w-5 text-blue-600\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Facebook\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                            lineNumber: 587,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-gray-600 dark:text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    console.log('Switching isLogin to:', !isLogin);\n                                    setIsLogin(!isLogin);\n                                    setVerificationSent(false);\n                                    setError(\"\");\n                                },\n                                className: \"text-primary hover:underline\",\n                                children: isLogin ? \"Need an account? Register\" : \"Have an account? Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n            lineNumber: 400,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\components\\\\Auth\\\\AuthModal.jsx\",\n        lineNumber: 393,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthModal, \"sAWn7cmK5to8tjUk0dH4vDSvFoc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AuthModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthModal);\nvar _c;\n$RefreshReg$(_c, \"AuthModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Auth/AuthModal.jsx\n"));

/***/ })

});