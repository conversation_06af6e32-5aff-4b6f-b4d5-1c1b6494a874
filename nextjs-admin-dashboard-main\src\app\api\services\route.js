import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Service from '@/models/Service';
import { apiResponse } from '@/lib/apiResponse';

export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const skip = (page - 1) * limit;

    // Build query
    let query = { isActive: true };
    
    if (category) {
      query.category = category;
    }
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Get services with business owner details
    const services = await Service.find(query)
      .populate('businessOwner', 'businessName ownerFirstName ownerLastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Service.countDocuments(query);

    return apiResponse.success({
      services,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }, 'Services fetched successfully');

  } catch (error) {
    console.error('Error fetching services:', error);
    return apiResponse.error('Failed to fetch services', 500);
  }
}

export async function POST(request) {
  try {
    await connectDB();
    
    const serviceData = await request.json();
    
    // Validate required fields
    const requiredFields = ['title', 'description', 'category', 'price', 'businessOwner'];
    for (const field of requiredFields) {
      if (!serviceData[field]) {
        return apiResponse.error(`${field} is required`, 400);
      }
    }

    // Create new service
    const service = new Service(serviceData);
    await service.save();

    // Populate business owner details
    await service.populate('businessOwner', 'businessName ownerFirstName ownerLastName email');

    return apiResponse.success(service, 'Service created successfully', 201);

  } catch (error) {
    console.error('Error creating service:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return apiResponse.error(`Validation error: ${errors.join(', ')}`, 400);
    }
    
    return apiResponse.error('Failed to create service', 500);
  }
}
