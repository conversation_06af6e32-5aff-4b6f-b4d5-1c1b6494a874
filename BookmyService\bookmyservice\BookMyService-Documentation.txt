# BookMyService - Project Documentation

## Table of Contents
1. Introduction
2. System Architecture
3. User Roles and Authentication
4. Core Functionalities
5. Frontend Components
6. Backend Structure
7. API Endpoints
8. Database Schema
9. Payment Processing
10. Email Notifications
11. Booking Workflow
12. Service Management
13. User Management
14. Business Owner Management
15. Admin Features
16. Error Handling and Notifications

## 1. Introduction

BookMyService is a comprehensive service booking platform that connects service providers (business owners) with customers (users). The platform allows business owners to list their services, and users to browse, book, and pay for these services. The application features a robust authentication system, real-time booking management, payment processing via Stripe, and email notifications.

### Key Features
- User, business owner, and admin roles with separate dashboards
- Service listing and discovery
- Booking management with status tracking
- Secure payment processing
- Email notifications for booking events
- Revenue statistics for business owners
- Profile management for all user types

## 2. System Architecture

BookMyService follows a client-server architecture with a clear separation between frontend and backend:

### Frontend
- Built with React.js
- Uses React Router for navigation
- Context API for state management
- Tailwind CSS for styling
- Responsive design for mobile and desktop

### Backend
- Node.js with Express.js
- MongoDB for database
- JWT for authentication
- Nodemailer for email services
- Stripe for payment processing
- Cloudinary for image storage

### Communication Flow
1. Frontend makes API requests to the backend
2. Backend processes requests, interacts with the database, and returns responses
3. Frontend renders data based on responses

## 3. User Roles and Authentication

The application supports three user roles, each with different permissions and interfaces:

### User (Customer)
- Can browse services
- Can book services
- Can manage their bookings
- Can update their profile

### Business Owner
- Can create and manage services
- Can accept or reject booking requests
- Can view revenue statistics
- Can manage their business profile

### Admin
- Can manage all users and business owners
- Can view and manage all services
- Can handle disputes and refunds

### Authentication Flow
1. **Registration**:
   - User/Business Owner fills registration form
   - OTP is sent to email for verification
   - User verifies email with OTP
   - Account is created

2. **Login**:
   - User enters email
   - OTP is sent to email
   - User verifies with OTP
   - JWT token is generated and stored in localStorage
   - User is redirected to appropriate dashboard

3. **Authorization**:
   - JWT token is sent with each API request
   - Backend validates token and checks user role
   - Access is granted or denied based on role and permissions

## 4. Core Functionalities

### Service Listing and Discovery
- Business owners can create services with details, images, pricing
- Users can browse services by category, location, or search terms
- Service cards display key information (name, price, rating, image)

### Booking Process
1. User selects a service
2. User chooses date and time
3. User confirms booking details
4. User makes payment via Stripe
5. Booking request is sent to business owner
6. Business owner accepts or rejects booking
7. User receives confirmation or rejection notification
8. Service is delivered
9. User marks service as completed using OTP

### Payment Processing
- Secure payment via Stripe
- Payment is held until service is completed or cancelled
- Refunds are processed for rejected or cancelled bookings

### Notifications
- Email notifications for booking events
- In-app notifications for booking status changes
- Toast notifications for user actions

## 5. Frontend Components

### Common Components
- **Navbar**: Navigation bar with links to different sections
- **Footer**: Contains links to important pages and contact information
- **ServiceCard**: Displays service information in a card format
- **BookingForm**: Form for creating a new booking
- **Toast**: Notification component for success/error messages

### Page Components
- **HomePage**: Landing page with featured services and categories
- **ServiceListingPage**: Displays services with filtering options
- **ServiceDetailPage**: Shows detailed information about a service
- **LoginPage**: Handles user authentication
- **RegisterPage**: Handles user registration

### User Profile Components
- **UserProfile**: Main component for user profile management
- **MyBookings**: Displays user's bookings with status and actions
- **EditProfile**: Form for updating user profile information

### Business Owner Components
- **BusinessProfile**: Main component for business owner profile
- **AddService**: Form for creating a new service
- **MyServices**: Displays services offered by the business owner
- **BookingRequests**: Shows pending booking requests
- **RevenueStatistics**: Displays revenue data with charts

## 6. Backend Structure

### Controllers
- **AuthController**: Handles authentication (login, register, verify)
- **UserController**: Manages user-related operations
- **BusinessOwnerController**: Manages business owner operations
- **ServiceController**: Handles service CRUD operations
- **BookingController**: Manages booking operations
- **PaymentController**: Processes payments and refunds
- **RevenueController**: Calculates and returns revenue statistics

### Middleware
- **AuthMiddleware**: Validates JWT tokens and user roles
- **ErrorMiddleware**: Handles and formats error responses
- **FileUploadMiddleware**: Processes file uploads

### Services
- **EmailService**: Sends email notifications
- **StripeService**: Interfaces with Stripe API
- **CloudinaryService**: Manages image uploads and storage

### Models
- **User**: Represents customer data
- **BusinessOwner**: Represents service provider data
- **Service**: Represents service offerings
- **Booking**: Represents booking data
- **Payment**: Represents payment transactions

## 7. API Endpoints

### Authentication
- `POST /api/auth/register`: Register a new user
- `POST /api/auth/login`: Login with email
- `POST /api/auth/verify-otp`: Verify OTP for authentication
- `GET /api/auth/me`: Get current user information

### User Management
- `GET /api/user/:id`: Get user profile
- `PUT /api/user/update-profile/:id`: Update user profile
- `GET /api/user/bookings`: Get user's bookings

### Business Owner Management
- `GET /api/business-owner/:id`: Get business owner profile
- `PUT /api/business-owner/:id`: Update business owner profile
- `GET /api/business-owner/services`: Get services offered by business owner

### Service Management
- `POST /api/service/create`: Create a new service
- `GET /api/service/:id`: Get service details
- `PUT /api/service/:id`: Update service
- `DELETE /api/service/:id`: Delete service
- `GET /api/service/all`: Get all services
- `GET /api/service/category/:category`: Get services by category

### Booking Management
- `POST /api/booking/create`: Create a new booking
- `GET /api/booking/:id`: Get booking details
- `PUT /api/booking/confirm/:id`: Confirm booking (business owner)
- `PUT /api/booking/cancel/:id`: Cancel booking (user)
- `PUT /api/booking/cancel-by-provider/:id`: Cancel booking (business owner)
- `PUT /api/booking/complete/:id`: Mark booking as completed
- `GET /api/booking/business-owner`: Get bookings for business owner

### Payment Processing
- `POST /api/payment/process`: Process payment for booking
- `POST /api/payment/refund`: Process refund for cancelled booking

### Revenue Statistics
- `GET /api/booking/statistics/revenue`: Get revenue statistics for business owner

## 8. Database Schema

### User Schema
- `_id`: ObjectId (Primary Key)
- `firstName`: String
- `lastName`: String
- `email`: String (unique)
- `phoneNumber`: String
- `bookedServiceIds`: Array of Service IDs
- `createdAt`: Date
- `updatedAt`: Date

### BusinessOwner Schema
- `_id`: ObjectId (Primary Key)
- `ownerFirstName`: String
- `ownerLastName`: String
- `email`: String (unique)
- `phoneNumber`: String
- `businessName`: String
- `businessCategory`: String
- `businessAddress`: String
- `city`: String
- `state`: String
- `zipCode`: String
- `country`: String
- `businessLogo`: String (URL)
- `createdAt`: Date
- `updatedAt`: Date

### Service Schema
- `_id`: ObjectId (Primary Key)
- `name`: String
- `category`: String
- `description`: String
- `price`: Number
- `availability`: String
- `location`: Array of Strings
- `duration`: Number
- `images`: Array of Strings (URLs)
- `booking_type`: Array of Strings
- `payment_options`: Array of Strings
- `businessOwner`: ObjectId (Reference to BusinessOwner)
- `bookedBy`: Array of User IDs
- `status`: String (enum: AVAILABLE, BOOKED, CANCELLED)
- `createdAt`: Date
- `updatedAt`: Date

### Booking Schema
- `_id`: ObjectId (Primary Key)
- `service`: ObjectId (Reference to Service)
- `user`: ObjectId (Reference to User)
- `businessOwner`: ObjectId (Reference to BusinessOwner)
- `date`: Date
- `timeSlot`: String
- `status`: String (enum: PENDING, CONFIRMED, COMPLETED, CANCELLED, CANCELLED_BY_PROVIDER)
- `paymentIntentId`: String
- `refundId`: String
- `amount`: Number
- `testPayment`: Boolean
- `otp`: String
- `createdAt`: Date
- `updatedAt`: Date

## 9. Payment Processing

### Payment Flow
1. **Initiation**:
   - User selects a service and proceeds to checkout
   - Frontend creates a payment intent via the backend
   - Stripe returns a client secret

2. **Processing**:
   - User enters payment details
   - Frontend confirms payment with Stripe using client secret
   - Stripe processes the payment

3. **Confirmation**:
   - Backend receives payment confirmation from Stripe
   - Booking is created with PENDING status
   - User receives confirmation email with booking details

4. **Refund Process**:
   - If booking is cancelled or rejected, refund is initiated
   - Backend creates refund request to Stripe
   - Stripe processes refund
   - User receives refund confirmation email

### Implementation Details
- Stripe Elements for secure payment form
- Payment Intent API for creating and managing payments
- Webhook handling for payment events
- Error handling for failed payments

## 10. Email Notifications

### Types of Notifications
- **Registration**: OTP for email verification
- **Login**: OTP for authentication
- **Booking Confirmation**: Details of confirmed booking
- **Booking Rejection**: Notification of rejected booking with reason
- **Service Completion**: Confirmation of completed service
- **Booking Cancellation**: Notification of cancelled booking
- **Refund Confirmation**: Details of processed refund

### Email Templates
- HTML templates with responsive design
- Personalized with user and service information
- Includes relevant action buttons and links

### Implementation
- Nodemailer for sending emails
- Gmail SMTP for delivery
- Async processing to prevent blocking

## 11. Booking Workflow

### Booking States
1. **PENDING**: Initial state after payment, awaiting business owner approval
2. **CONFIRMED**: Booking approved by business owner
3. **COMPLETED**: Service delivered and marked as completed by user
4. **CANCELLED**: Booking cancelled by user
5. **CANCELLED_BY_PROVIDER**: Booking rejected or cancelled by business owner

### Booking Creation Flow
1. User selects service and enters booking details
2. User makes payment
3. System creates booking with PENDING status
4. Business owner receives notification
5. Business owner accepts or rejects booking
6. User receives confirmation or rejection notification

### Booking Completion Flow
1. Service is delivered
2. Business owner provides OTP to user
3. User enters OTP to mark service as completed
4. System updates booking status to COMPLETED
5. Revenue is recorded for business owner

### Booking Cancellation Flow
1. User or business owner initiates cancellation
2. System updates booking status to CANCELLED or CANCELLED_BY_PROVIDER
3. Refund is processed if applicable
4. Both parties receive cancellation notification
5. Booking is removed from active bookings list

## 12. Service Management

### Service Creation
1. Business owner fills service details form
2. Business owner uploads service images
3. System validates and stores service information
4. Service becomes available for booking

### Service Editing
1. Business owner selects service to edit
2. Form is pre-filled with existing service data
3. Business owner makes changes
4. System validates and updates service information

### Service Deletion
1. Business owner selects service to delete
2. System checks if service has active bookings
3. If no active bookings, service is deleted
4. If active bookings exist, deletion is prevented

### Service Visibility
- Services can be filtered by category, price range, location
- Search functionality allows finding services by keywords
- Featured services can be highlighted on the homepage

## 13. User Management

### User Registration
1. User fills registration form with personal details
2. OTP is sent to user's email
3. User verifies email with OTP
4. User account is created

### Profile Management
1. User can view their profile information
2. User can update personal details
3. User can view booking history
4. User can manage active bookings

### Booking Management
1. User can view all their bookings
2. User can filter bookings by status
3. User can cancel pending or confirmed bookings
4. User can mark services as completed with OTP
5. User can view booking details including payment information

## 14. Business Owner Management

### Registration
1. Business owner fills registration form with business details
2. OTP is sent to business owner's email
3. Business owner verifies email with OTP
4. Business owner account is created

### Profile Management
1. Business owner can view their profile information
2. Business owner can update business details
3. Business owner can upload business logo
4. Business owner can manage address information

### Service Management
1. Business owner can create new services
2. Business owner can edit existing services
3. Business owner can delete services
4. Business owner can view all their services

### Booking Management
1. Business owner can view booking requests
2. Business owner can accept or reject bookings
3. Business owner can view booking history
4. Business owner can cancel confirmed bookings if necessary

### Revenue Tracking
1. Business owner can view total revenue
2. Business owner can view revenue by service
3. Business owner can view monthly revenue trends
4. Business owner can track completed vs cancelled bookings

## 15. Admin Features

### User Management
1. Admin can view all users
2. Admin can edit user information
3. Admin can disable user accounts
4. Admin can view user booking history

### Business Owner Management
1. Admin can view all business owners
2. Admin can edit business owner information
3. Admin can disable business owner accounts
4. Admin can view business owner services and revenue

### Service Management
1. Admin can view all services
2. Admin can edit service information
3. Admin can disable services
4. Admin can feature services on homepage

### Booking Management
1. Admin can view all bookings
2. Admin can change booking status
3. Admin can process refunds
4. Admin can resolve disputes

## 16. Error Handling and Notifications

### Error Types
- **Validation Errors**: Form input validation failures
- **Authentication Errors**: Login/access permission issues
- **Payment Errors**: Issues with payment processing
- **Server Errors**: Backend processing failures
- **Network Errors**: Communication issues between client and server

### Error Handling Strategy
1. Frontend validation to prevent invalid submissions
2. Backend validation for data integrity
3. Try-catch blocks for handling exceptions
4. Standardized error response format
5. User-friendly error messages

### Notification System
1. **Toast Notifications**: Temporary messages for user actions
2. **In-App Notifications**: Persistent messages for important events
3. **Email Notifications**: External communication for critical events
4. **Status Indicators**: Visual cues for process status

### Implementation
- React-toast for toast notifications
- Context API for managing notification state
- Standardized notification format for consistency
- Automatic dismissal for non-critical notifications
