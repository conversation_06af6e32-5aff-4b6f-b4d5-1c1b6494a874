import{Fragment as t,options as e,h as n}from"preact";if("function"!=typeof Symbol){var r=0;Symbol=function(t){return"@@"+t+ ++r},Symbol.for=function(t){return"@@"+t}}var o=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,i=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,a=/[\s\n\\/='"\0<>]/,l=/^xlink:?./,u=/["&<]/;function c(t){if(!1===u.test(t+=""))return t;for(var e=0,n=0,r="",o="";n<t.length;n++){switch(t.charCodeAt(n)){case 34:o="&quot;";break;case 38:o="&amp;";break;case 60:o="&lt;";break;default:continue}n!==e&&(r+=t.slice(e,n)),r+=o,e=n+1}return n!==e&&(r+=t.slice(e,n)),r}var s=function(t,e){return String(t).replace(/(\n+)/g,"$1"+(e||"\t"))},f=function(t,e,n){return String(t).length>(e||40)||!n&&-1!==String(t).indexOf("\n")||-1!==String(t).indexOf("<")},p={},d=/([A-Z])/g;function _(t){var e="";for(var n in t){var r=t[n];null!=r&&""!==r&&(e&&(e+=" "),e+="-"==n[0]?n:p[n]||(p[n]=n.replace(d,"-$1").toLowerCase()),e="number"==typeof r&&!1===o.test(n)?e+": "+r+"px;":e+": "+r+";")}return e||void 0}function y(t,e){return Array.isArray(e)?e.reduce(y,t):null!=e&&!1!==e&&t.push(e),t}function v(){this.__d=!0}function g(t,e){return{__v:t,context:e,props:t.props,setState:v,forceUpdate:v,__d:!0,__h:[]}}function b(t,e){var n=t.contextType,r=n&&e[n.__c];return null!=n?r?r.props.value:n.__:e}var m=[];function h(n,r,o,u,p,d){if(null==n||"boolean"==typeof n)return"";if("object"!=typeof n)return"function"==typeof n?"":c(n);var v=o.pretty,j=v&&"string"==typeof v?v:"\t";if(Array.isArray(n)){for(var x="",S=0;S<n.length;S++)v&&S>0&&(x+="\n"),x+=h(n[S],r,o,u,p,d);return x}if(void 0!==n.constructor)return"";var k,A=n.type,O=n.props,w=!1;if("function"==typeof A){if(w=!0,!o.shallow||!u&&!1!==o.renderRootComponent){if(A===t){var F=[];return y(F,n.props.children),h(F,r,o,!1!==o.shallowHighOrder,p,d)}var C,E=n.__c=g(n,r);e.__b&&e.__b(n);var M=e.__r;if(A.prototype&&"function"==typeof A.prototype.render){var H=b(A,r);(E=n.__c=new A(O,H)).__v=n,E._dirty=E.__d=!0,E.props=O,null==E.state&&(E.state={}),null==E._nextState&&null==E.__s&&(E._nextState=E.__s=E.state),E.context=H,A.getDerivedStateFromProps?E.state=Object.assign({},E.state,A.getDerivedStateFromProps(E.props,E.state)):E.componentWillMount&&(E.componentWillMount(),E.state=E._nextState!==E.state?E._nextState:E.__s!==E.state?E.__s:E.state),M&&M(n),C=E.render(E.props,E.state,E.context)}else for(var N=b(A,r),D=0;E.__d&&D++<25;)E.__d=!1,M&&M(n),C=A.call(n.__c,O,N);return E.getChildContext&&(r=Object.assign({},r,E.getChildContext())),e.diffed&&e.diffed(n),h(C,r,o,!1!==o.shallowHighOrder,p,d)}A=(k=A).displayName||k!==Function&&k.name||function(t){var e=(Function.prototype.toString.call(t).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!e){for(var n=-1,r=m.length;r--;)if(m[r]===t){n=r;break}n<0&&(n=m.push(t)-1),e="UnnamedComponent"+n}return e}(k)}var I,L,W="<"+A;if(O){var $=Object.keys(O);o&&!0===o.sortAttributes&&$.sort();for(var P=0;P<$.length;P++){var T=$[P],U=O[T];if("children"!==T){if(!a.test(T)&&(o&&o.allAttributes||"key"!==T&&"ref"!==T&&"__self"!==T&&"__source"!==T)){if("defaultValue"===T)T="value";else if("defaultChecked"===T)T="checked";else if("defaultSelected"===T)T="selected";else if("className"===T){if(void 0!==O.class)continue;T="class"}else p&&l.test(T)&&(T=T.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===T){if(O.for)continue;T="for"}"style"===T&&U&&"object"==typeof U&&(U=_(U)),"a"===T[0]&&"r"===T[1]&&"boolean"==typeof U&&(U=String(U));var R=o.attributeHook&&o.attributeHook(T,U,r,o,w);if(R||""===R)W+=R;else if("dangerouslySetInnerHTML"===T)L=U&&U.__html;else if("textarea"===A&&"value"===T)I=U;else if((U||0===U||""===U)&&"function"!=typeof U){if(!(!0!==U&&""!==U||(U=T,o&&o.xml))){W=W+" "+T;continue}if("value"===T){if("select"===A){d=U;continue}"option"===A&&d==U&&void 0===O.selected&&(W+=" selected")}W=W+" "+T+'="'+c(U)+'"'}}}else I=U}}if(v){var J=W.replace(/\n\s*/," ");J===W||~J.indexOf("\n")?v&&~W.indexOf("\n")&&(W+="\n"):W=J}if(W+=">",a.test(A))throw new Error(A+" is not a valid HTML tag name in "+W);var V,q=i.test(A)||o.voidElements&&o.voidElements.test(A),z=[];if(L)v&&f(L)&&(L="\n"+j+s(L,j)),W+=L;else if(null!=I&&y(V=[],I).length){for(var B=v&&~W.indexOf("\n"),G=!1,Z=0;Z<V.length;Z++){var K=V[Z];if(null!=K&&!1!==K){var Q=h(K,r,o,!0,"svg"===A||"foreignObject"!==A&&p,d);if(v&&!B&&f(Q)&&(B=!0),Q)if(v){var X=Q.length>0&&"<"!=Q[0];G&&X?z[z.length-1]+=Q:z.push(Q),G=X}else z.push(Q)}}if(v&&B)for(var Y=z.length;Y--;)z[Y]="\n"+j+s(z[Y],j)}if(z.length||L)W+=z.join("");else if(o&&o.xml)return W.substring(0,W.length-1)+" />";return!q||V||L?(v&&~W.indexOf("\n")&&(W+="\n"),W=W+"</"+A+">"):W=W.replace(/>$/," />"),W}var j={shallow:!0};S.render=S;var x=[];function S(r,o,i){o=o||{};var a=e.__s;e.__s=!0;var l,u=n(t,null);return u.__k=[r],l=i&&(i.pretty||i.voidElements||i.sortAttributes||i.shallow||i.allAttributes||i.xml||i.attributeHook)?h(r,o,i):C(r,o,!1,void 0,u),e.__c&&e.__c(r,x),e.__s=a,x.length=0,l}function k(t){return null==t||"boolean"==typeof t?null:"string"==typeof t||"number"==typeof t||"bigint"==typeof t?n(null,null,t):t}function A(t,e){return"className"===t?"class":"htmlFor"===t?"for":"defaultValue"===t?"value":"defaultChecked"===t?"checked":"defaultSelected"===t?"selected":e&&l.test(t)?t.toLowerCase().replace(/^xlink:?/,"xlink:"):t}function O(t,e){return"style"===t&&null!=e&&"object"==typeof e?_(e):"a"===t[0]&&"r"===t[1]&&"boolean"==typeof e?String(e):e}var w=Array.isArray,F=Object.assign;function C(n,r,o,l,u){if(null==n||!0===n||!1===n||""===n)return"";if("object"!=typeof n)return"function"==typeof n?"":c(n);if(w(n)){var s="";u.__k=n;for(var f=0;f<n.length;f++)s+=C(n[f],r,o,l,u),n[f]=k(n[f]);return s}if(void 0!==n.constructor)return"";n.__=u,e.__b&&e.__b(n);var p=n.type,d=n.props;if("function"==typeof p){var _;if(p===t)_=d.children;else{_=p.prototype&&"function"==typeof p.prototype.render?function(t,n){var r=t.type,o=b(r,n),i=new r(t.props,o);t.__c=i,i.__v=t,i.__d=!0,i.props=t.props,null==i.state&&(i.state={}),null==i.__s&&(i.__s=i.state),i.context=o,r.getDerivedStateFromProps?i.state=F({},i.state,r.getDerivedStateFromProps(i.props,i.state)):i.componentWillMount&&(i.componentWillMount(),i.state=i.__s!==i.state?i.__s:i.state);var a=e.__r;return a&&a(t),i.render(i.props,i.state,i.context)}(n,r):function(t,n){var r,o=g(t,n),i=b(t.type,n);t.__c=o;for(var a=e.__r,l=0;o.__d&&l++<25;)o.__d=!1,a&&a(t),r=t.type.call(o,t.props,i);return r}(n,r);var y=n.__c;y.getChildContext&&(r=F({},r,y.getChildContext()))}var v=C(_=null!=_&&_.type===t&&null==_.key?_.props.children:_,r,o,l,n);return e.diffed&&e.diffed(n),n.__=void 0,e.unmount&&e.unmount(n),v}var m,h,j="<";if(j+=p,d)for(var x in m=d.children,d){var S=d[x];if(!("key"===x||"ref"===x||"__self"===x||"__source"===x||"children"===x||"className"===x&&"class"in d||"htmlFor"===x&&"for"in d||a.test(x)))if(S=O(x=A(x,o),S),"dangerouslySetInnerHTML"===x)h=S&&S.__html;else if("textarea"===p&&"value"===x)m=S;else if((S||0===S||""===S)&&"function"!=typeof S){if(!0===S||""===S){S=x,j=j+" "+x;continue}if("value"===x){if("select"===p){l=S;continue}"option"!==p||l!=S||"selected"in d||(j+=" selected")}j=j+" "+x+'="'+c(S)+'"'}}var E=j;if(j+=">",a.test(p))throw new Error(p+" is not a valid HTML tag name in "+j);var M="",H=!1;if(h)M+=h,H=!0;else if("string"==typeof m)M+=c(m),H=!0;else if(w(m)){n.__k=m;for(var N=0;N<m.length;N++){var D=m[N];if(m[N]=k(D),null!=D&&!1!==D){var I=C(D,r,"svg"===p||"foreignObject"!==p&&o,l,n);I&&(M+=I,H=!0)}}}else if(null!=m&&!1!==m&&!0!==m){n.__k=[k(m)];var L=C(m,r,"svg"===p||"foreignObject"!==p&&o,l,n);L&&(M+=L,H=!0)}if(e.diffed&&e.diffed(n),n.__=void 0,e.unmount&&e.unmount(n),H)j+=M;else if(i.test(p))return E+" />";return j+"</"+p+">"}S.shallowRender=function(t,e){return S(t,e,j)};const E=/(\\|\"|\')/g,M=Object.prototype.toString,H=Date.prototype.toISOString,N=Error.prototype.toString,D=RegExp.prototype.toString,I=Symbol.prototype.toString,L=/^Symbol\((.*)\)(.*)$/,W=/\n/gi,$=Object.getOwnPropertySymbols||(t=>[]);function P(t){return"[object Array]"===t||"[object ArrayBuffer]"===t||"[object DataView]"===t||"[object Float32Array]"===t||"[object Float64Array]"===t||"[object Int8Array]"===t||"[object Int16Array]"===t||"[object Int32Array]"===t||"[object Uint8Array]"===t||"[object Uint8ClampedArray]"===t||"[object Uint16Array]"===t||"[object Uint32Array]"===t}function T(t){return""===t.name?"[Function anonymous]":"[Function "+t.name+"]"}function U(t){return I.call(t).replace(L,"Symbol($1)")}function R(t){return"["+N.call(t)+"]"}function J(t){if(!0===t||!1===t)return""+t;if(void 0===t)return"undefined";if(null===t)return"null";const e=typeof t;if("number"===e)return function(t){return t!=+t?"NaN":0===t&&1/t<0?"-0":""+t}(t);if("string"===e)return'"'+function(t){return t.replace(E,"\\$1")}(t)+'"';if("function"===e)return T(t);if("symbol"===e)return U(t);const n=M.call(t);return"[object WeakMap]"===n?"WeakMap {}":"[object WeakSet]"===n?"WeakSet {}":"[object Function]"===n||"[object GeneratorFunction]"===n?T(t,min):"[object Symbol]"===n?U(t):"[object Date]"===n?H.call(t):"[object Error]"===n?R(t):"[object RegExp]"===n?D.call(t):"[object Arguments]"===n&&0===t.length?"Arguments []":P(n)&&0===t.length?t.constructor.name+" []":t instanceof Error&&R(t)}function V(t,e,n,r,o,i,a,l,u,c){let s="";if(t.length){s+=o;const f=n+e;for(let n=0;n<t.length;n++)s+=f+B(t[n],e,f,r,o,i,a,l,u,c),n<t.length-1&&(s+=","+r);s+=o+n}return"["+s+"]"}function q(t,e,n,r,o,i,a,l,u,c){if((i=i.slice()).indexOf(t)>-1)return"[Circular]";i.push(t);const s=++l>a;if(!s&&t.toJSON&&"function"==typeof t.toJSON)return B(t.toJSON(),e,n,r,o,i,a,l,u,c);const f=M.call(t);return"[object Arguments]"===f?s?"[Arguments]":function(t,e,n,r,o,i,a,l,u,c){return(c?"":"Arguments ")+V(t,e,n,r,o,i,a,l,u,c)}(t,e,n,r,o,i,a,l,u,c):P(f)?s?"[Array]":function(t,e,n,r,o,i,a,l,u,c){return(c?"":t.constructor.name+" ")+V(t,e,n,r,o,i,a,l,u,c)}(t,e,n,r,o,i,a,l,u,c):"[object Map]"===f?s?"[Map]":function(t,e,n,r,o,i,a,l,u,c){let s="Map {";const f=t.entries();let p=f.next();if(!p.done){s+=o;const t=n+e;for(;!p.done;)s+=t+B(p.value[0],e,t,r,o,i,a,l,u,c)+" => "+B(p.value[1],e,t,r,o,i,a,l,u,c),p=f.next(),p.done||(s+=","+r);s+=o+n}return s+"}"}(t,e,n,r,o,i,a,l,u,c):"[object Set]"===f?s?"[Set]":function(t,e,n,r,o,i,a,l,u,c){let s="Set {";const f=t.entries();let p=f.next();if(!p.done){s+=o;const t=n+e;for(;!p.done;)s+=t+B(p.value[1],e,t,r,o,i,a,l,u,c),p=f.next(),p.done||(s+=","+r);s+=o+n}return s+"}"}(t,e,n,r,o,i,a,l,u,c):"object"==typeof t?s?"[Object]":function(t,e,n,r,o,i,a,l,u,c){let s=(c?"":t.constructor?t.constructor.name+" ":"Object ")+"{",f=Object.keys(t).sort();const p=$(t);if(p.length&&(f=f.filter(t=>!("symbol"==typeof t||"[object Symbol]"===M.call(t))).concat(p)),f.length){s+=o;const p=n+e;for(let n=0;n<f.length;n++){const d=f[n];s+=p+B(d,e,p,r,o,i,a,l,u,c)+": "+B(t[d],e,p,r,o,i,a,l,u,c),n<f.length-1&&(s+=","+r)}s+=o+n}return s+"}"}(t,e,n,r,o,i,a,l,u,c):void 0}function z(t,e,n,r,o,i,a,l,u,c){let s,f=!1;for(let e=0;e<u.length;e++)if(s=u[e],s.test(t)){f=!0;break}return!!f&&s.print(t,function(t){return B(t,e,n,r,o,i,a,l,u,c)},function(t){const r=n+e;return r+t.replace(W,"\n"+r)},{edgeSpacing:o,spacing:r})}function B(t,e,n,r,o,i,a,l,u,c){return J(t)||z(t,e,n,r,o,i,a,l,u,c)||q(t,e,n,r,o,i,a,l,u,c)}const G={indent:2,min:!1,maxDepth:Infinity,plugins:[]};function Z(t){return new Array(t+1).join(" ")}var K={test:function(t){return t&&"object"==typeof t&&"type"in t&&"props"in t&&"key"in t},print:function(t,e,n){return S(t,K.context,K.opts)}},Q={plugins:[K]},X={attributeHook:function(t,e,n,r,o){var i=typeof e;if("dangerouslySetInnerHTML"===t)return!1;if(null==e||"function"===i&&!r.functions)return"";if(r.skipFalseAttributes&&!o&&(!1===e||("class"===t||"style"===t)&&""===e))return"";var a="string"==typeof r.pretty?r.pretty:"\t";return"string"!==i?("function"!==i||r.functionNames?(K.context=n,K.opts=r,~(e=function(t,e){let n,r;e?(function(t){if(Object.keys(t).forEach(t=>{if(!G.hasOwnProperty(t))throw new Error("prettyFormat: Invalid option: "+t)}),t.min&&void 0!==t.indent&&0!==t.indent)throw new Error("prettyFormat: Cannot run with min option and indent")}(e),e=function(t){const e={};return Object.keys(G).forEach(n=>e[n]=t.hasOwnProperty(n)?t[n]:G[n]),e.min&&(e.indent=0),e}(e)):e=G;const o=e.min?" ":"\n",i=e.min?"":"\n";if(e&&e.plugins.length){n=Z(e.indent),r=[];var a=z(t,n,"",o,i,r,e.maxDepth,0,e.plugins,e.min);if(a)return a}return J(t)||(n||(n=Z(e.indent)),r||(r=[]),q(t,n,"",o,i,r,e.maxDepth,0,e.plugins,e.min))}(e,Q)).indexOf("\n")&&(e=s("\n"+e,a)+"\n")):e="Function",s("\n"+t+"={"+e+"}",a)):"\n"+a+t+'="'+c(e)+'"'},jsx:!0,xml:!1,functions:!0,functionNames:!0,skipFalseAttributes:!0,pretty:"  "};function Y(t,e,n,r){return S(t,e,n=Object.assign({},X,n||{}))}export default Y;export{Y as render};
//# sourceMappingURL=jsx.module.js.map
