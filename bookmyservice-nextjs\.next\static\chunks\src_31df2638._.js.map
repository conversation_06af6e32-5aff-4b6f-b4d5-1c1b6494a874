{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Book%20my%20Service%20new/bookmyservice-nextjs/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date) {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\nexport function formatCurrency(amount) {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD'\n  }).format(amount);\n}\n\nexport function formatTime(date) {\n  const d = new Date(date);\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function generateOTP() {\n  return Math.floor(100000 + Math.random() * 900000).toString();\n}\n\nexport function validateEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhone(phone) {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;\n  return phoneRegex.test(phone);\n}\n\nexport function truncateText(text, maxLength) {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function capitalizeFirst(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function getInitials(name) {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2);\n}\n\nexport function debounce(func, wait) {\n  let timeout;\n  return (...args) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function getStatusColor(status) {\n  switch (status.toLowerCase()) {\n    case 'pending':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'confirmed':\n      return 'text-blue-600 bg-blue-100';\n    case 'completed':\n      return 'text-green-600 bg-green-100';\n    case 'cancelled':\n    case 'cancelled_by_provider':\n      return 'text-red-600 bg-red-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function getBookingStatusText(status) {\n  switch (status.toLowerCase()) {\n    case 'pending':\n      return 'Pending Approval';\n    case 'confirmed':\n      return 'Confirmed';\n    case 'completed':\n      return 'Completed';\n    case 'cancelled':\n      return 'Cancelled by User';\n    case 'cancelled_by_provider':\n      return 'Cancelled by Provider';\n    default:\n      return status;\n  }\n}\n\nexport const API_BASE_URL = process.env.NODE_ENV === 'production' \n  ? 'https://bookmyservice.onrender.com/api'\n  : '/api';\n\nexport function getAuthHeaders() {\n  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;\n  return {\n    'Content-Type': 'application/json',\n    ...(token && { Authorization: `Bearer ${token}` })\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAwG4B;AAxG5B;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAI;IAC7B,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAM;IACnC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAI;IAC7B,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS;IACd,OAAO,KAAK,KAAK,CAAC,SAAS,KAAK,MAAM,KAAK,QAAQ,QAAQ;AAC7D;AAEO,SAAS,cAAc,KAAK;IACjC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAK;IACjC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,IAAI,EAAE,SAAS;IAC1C,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,gBAAgB,GAAG;IACjC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,YAAY,IAAI;IAC9B,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,eAAe,MAAM;IACnC,OAAQ,OAAO,WAAW;QACxB,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,qBAAqB,MAAM;IACzC,OAAQ,OAAO,WAAW;QACxB,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,eAAe,6EAExB;AAEG,SAAS;IACd,MAAM,QAAQ,uCAAgC,aAAa,OAAO,CAAC;IACnE,OAAO;QACL,gBAAgB;QAChB,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Book%20my%20Service%20new/bookmyservice-nextjs/src/app/auth/login/page.jsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { toast } from 'react-toastify';\nimport { validateEmail } from '@/lib/utils';\n\nexport default function LoginPage() {\n  const [step, setStep] = useState('email'); // 'email' or 'otp'\n  const [email, setEmail] = useState('');\n  const [otp, setOtp] = useState('');\n  const [loading, setLoading] = useState(false);\n  const router = useRouter();\n  const { login } = useAuth();\n\n  const handleEmailSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!email) {\n      toast.error('Please enter your email address');\n      return;\n    }\n    \n    if (!validateEmail(email)) {\n      toast.error('Please enter a valid email address');\n      return;\n    }\n    \n    setLoading(true);\n    \n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email }),\n      });\n      \n      const data = await response.json();\n      \n      if (data.success) {\n        toast.success(data.message);\n        setStep('otp');\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      toast.error('An error occurred. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleOtpSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!otp) {\n      toast.error('Please enter the OTP');\n      return;\n    }\n    \n    if (otp.length !== 6) {\n      toast.error('OTP must be 6 digits');\n      return;\n    }\n    \n    setLoading(true);\n    \n    try {\n      const response = await fetch('/api/auth/verify-otp', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, otp }),\n      });\n      \n      const data = await response.json();\n      \n      if (data.success) {\n        login(data.data.user, data.data.token);\n        toast.success('Login successful!');\n        \n        // Redirect based on user role\n        if (data.data.user.role === 'admin') {\n          router.push('/admin/dashboard');\n        } else if (data.data.user.role === 'business_owner') {\n          router.push('/business/dashboard');\n        } else {\n          router.push('/dashboard');\n        }\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error) {\n      console.error('OTP verification error:', error);\n      toast.error('An error occurred. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleResendOtp = async () => {\n    setLoading(true);\n    \n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email }),\n      });\n      \n      const data = await response.json();\n      \n      if (data.success) {\n        toast.success('New OTP sent to your email');\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error) {\n      console.error('Resend OTP error:', error);\n      toast.error('Failed to resend OTP. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <Link href=\"/\" className=\"flex justify-center\">\n            <h1 className=\"text-3xl font-bold text-primary\">BookMyService</h1>\n          </Link>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            {step === 'email' ? 'Sign in to your account' : 'Verify your email'}\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            {step === 'email' ? (\n              <>\n                Or{' '}\n                <Link href=\"/auth/register\" className=\"font-medium text-primary hover:text-blue-dark\">\n                  create a new account\n                </Link>\n              </>\n            ) : (\n              `We've sent a 6-digit code to ${email}`\n            )}\n          </p>\n        </div>\n\n        {step === 'email' ? (\n          <form className=\"mt-8 space-y-6\" onSubmit={handleEmailSubmit}>\n            <div>\n              <label htmlFor=\"email\" className=\"sr-only\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                className=\"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm\"\n                placeholder=\"Email address\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                disabled={loading}\n              />\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Sending OTP...\n                  </div>\n                ) : (\n                  'Send OTP'\n                )}\n              </button>\n            </div>\n          </form>\n        ) : (\n          <form className=\"mt-8 space-y-6\" onSubmit={handleOtpSubmit}>\n            <div>\n              <label htmlFor=\"otp\" className=\"sr-only\">\n                OTP Code\n              </label>\n              <input\n                id=\"otp\"\n                name=\"otp\"\n                type=\"text\"\n                maxLength=\"6\"\n                required\n                className=\"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm text-center text-lg tracking-widest\"\n                placeholder=\"000000\"\n                value={otp}\n                onChange={(e) => setOtp(e.target.value.replace(/\\D/g, ''))}\n                disabled={loading}\n              />\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Verifying...\n                  </div>\n                ) : (\n                  'Verify & Sign In'\n                )}\n              </button>\n            </div>\n\n            <div className=\"text-center\">\n              <button\n                type=\"button\"\n                onClick={handleResendOtp}\n                disabled={loading}\n                className=\"text-sm text-primary hover:text-blue-dark disabled:opacity-50\"\n              >\n                Didn't receive the code? Resend OTP\n              </button>\n            </div>\n\n            <div className=\"text-center\">\n              <button\n                type=\"button\"\n                onClick={() => {\n                  setStep('email');\n                  setOtp('');\n                }}\n                className=\"text-sm text-gray-600 hover:text-gray-900\"\n              >\n                ← Back to email\n              </button>\n            </div>\n          </form>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,mBAAmB;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAExB,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI,CAAC,OAAO;YACV,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YACzB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO;gBAC1B,QAAQ;YACV,OAAO;gBACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,EAAE,cAAc;QAEhB,IAAI,CAAC,KAAK;YACR,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,IAAI,MAAM,KAAK,GAAG;YACpB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAI;YACpC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,KAAK;gBACrC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,8BAA8B;gBAC9B,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS;oBACnC,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,kBAAkB;oBACnD,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC;gCAAG,WAAU;0CAAkC;;;;;;;;;;;sCAElD,6LAAC;4BAAG,WAAU;sCACX,SAAS,UAAU,4BAA4B;;;;;;sCAElD,6LAAC;4BAAE,WAAU;sCACV,SAAS,wBACR;;oCAAE;oCACG;kDACH,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAiB,WAAU;kDAAgD;;;;;;;+CAKxF,CAAC,6BAA6B,EAAE,OAAO;;;;;;;;;;;;gBAK5C,SAAS,wBACR,6LAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAAU;;;;;;8CAG3C,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,cAAa;oCACb,QAAQ;oCACR,WAAU;oCACV,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,UAAU;;;;;;;;;;;;sCAId,6LAAC;sCACC,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,wBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;wCAAuE;;;;;;2CAIxF;;;;;;;;;;;;;;;;yCAMR,6LAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAM,WAAU;8CAAU;;;;;;8CAGzC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,WAAU;oCACV,QAAQ;oCACR,WAAU;oCACV,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;oCACtD,UAAU;;;;;;;;;;;;sCAId,6LAAC;sCACC,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,wBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;wCAAuE;;;;;;2CAIxF;;;;;;;;;;;sCAKN,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;sCAKH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS;oCACP,QAAQ;oCACR,OAAO;gCACT;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA1PwB;;QAKP,qIAAA,CAAA,YAAS;QACN,kIAAA,CAAA,UAAO;;;KANH", "debugId": null}}]}