import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Booking from '@/models/Booking';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';
import { sendEmail } from '@/lib/email';

export async function PATCH(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    const bookingId = params.id;
    const businessOwnerId = decoded.userId;
    const { status } = await request.json();
    
    // Validate status
    const validStatuses = ['pending', 'confirmed', 'rejected', 'in_progress', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return apiResponse.error('Invalid status', 400);
    }
    
    // Find the booking
    const booking = await Booking.findById(bookingId)
      .populate('user', 'firstName lastName email')
      .populate('service', 'title description price')
      .populate('businessOwner', 'businessName email ownerFirstName ownerLastName');
    
    if (!booking) {
      return apiResponse.error('Booking not found', 404);
    }
    
    // Check if this booking belongs to the business owner
    if (booking.businessOwner._id.toString() !== businessOwnerId) {
      return apiResponse.error('Unauthorized to update this booking', 403);
    }
    
    // Check if status transition is valid
    const currentStatus = booking.status;
    const validTransitions = {
      pending: ['confirmed', 'rejected'],
      confirmed: ['in_progress', 'cancelled'],
      in_progress: ['completed', 'cancelled'],
      completed: [], // Cannot change from completed
      cancelled: [], // Cannot change from cancelled
      rejected: [] // Cannot change from rejected
    };
    
    if (!validTransitions[currentStatus].includes(status)) {
      return apiResponse.error(`Cannot change status from ${currentStatus} to ${status}`, 400);
    }
    
    // Update booking status
    booking.status = status;
    booking.updatedAt = new Date();
    
    // Add timestamps for specific statuses
    if (status === 'confirmed') {
      booking.confirmedAt = new Date();
    } else if (status === 'in_progress') {
      booking.startedAt = new Date();
    } else if (status === 'completed') {
      booking.completedAt = new Date();
    } else if (status === 'rejected') {
      booking.rejectedAt = new Date();
    }
    
    await booking.save();
    
    // Send email notification to customer
    const statusMessages = {
      confirmed: 'Your booking has been confirmed!',
      rejected: 'Your booking request has been declined.',
      in_progress: 'Your service is now in progress.',
      completed: 'Your service has been completed.',
      cancelled: 'Your booking has been cancelled.'
    };
    
    try {
      await sendEmail({
        to: booking.user.email,
        subject: `Booking Update - ${booking.service.title}`,
        html: `
          <h2>Booking Status Update</h2>
          <p>Dear ${booking.user.firstName},</p>
          <p>${statusMessages[status]}</p>
          
          <h3>Booking Details:</h3>
          <p><strong>Service:</strong> ${booking.service.title}</p>
          <p><strong>Provider:</strong> ${booking.businessOwner.businessName}</p>
          <p><strong>Date:</strong> ${new Date(booking.preferredDate).toLocaleDateString()}</p>
          ${booking.preferredTime ? `<p><strong>Time:</strong> ${booking.preferredTime}</p>` : ''}
          <p><strong>Status:</strong> ${status.replace('_', ' ').toUpperCase()}</p>
          
          ${status === 'confirmed' ? `
            <p>The service provider will contact you soon to finalize the details.</p>
          ` : ''}
          
          ${status === 'completed' ? `
            <p>We hope you're satisfied with the service! Please consider leaving a review.</p>
          ` : ''}
          
          ${status === 'rejected' ? `
            <p>We apologize for any inconvenience. You can browse other service providers or try booking at a different time.</p>
          ` : ''}
          
          <p>You can view your booking details in your dashboard.</p>
          
          <p>Best regards,<br>The BookMyService Team</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send status update email:', emailError);
      // Don't fail the request if email fails
    }
    
    return apiResponse.success(booking, `Booking status updated to ${status} successfully`);
    
  } catch (error) {
    console.error('Error updating booking status:', error);
    return apiResponse.error('Failed to update booking status', 500);
  }
}
