/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/business/dashboard/recent-bookings/route";
exports.ids = ["app/api/business/dashboard/recent-bookings/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute&page=%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute&page=%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Bhushan_patil_OneDrive_Desktop_Book_my_Service_new_nextjs_admin_dashboard_main_src_app_api_business_dashboard_recent_bookings_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/business/dashboard/recent-bookings/route.js */ \"(rsc)/./src/app/api/business/dashboard/recent-bookings/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/business/dashboard/recent-bookings/route\",\n        pathname: \"/api/business/dashboard/recent-bookings\",\n        filename: \"route\",\n        bundlePath: \"app/api/business/dashboard/recent-bookings/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\api\\\\business\\\\dashboard\\\\recent-bookings\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Bhushan_patil_OneDrive_Desktop_Book_my_Service_new_nextjs_admin_dashboard_main_src_app_api_business_dashboard_recent_bookings_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute&page=%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/business/dashboard/recent-bookings/route.js":
/*!*****************************************************************!*\
  !*** ./src/app/api/business/dashboard/recent-bookings/route.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.js\");\n/* harmony import */ var _models_Booking__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/Booking */ \"(rsc)/./src/models/Booking.js\");\n/* harmony import */ var _lib_apiResponse__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/apiResponse */ \"(rsc)/./src/lib/apiResponse.js\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-utils */ \"(rsc)/./src/lib/auth-utils.js\");\n\n\n\n\n\nasync function GET(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Verify authentication\n        const token = request.headers.get('authorization')?.replace('Bearer ', '');\n        if (!token) {\n            return _lib_apiResponse__WEBPACK_IMPORTED_MODULE_3__.apiResponse.error('Authentication required', 401);\n        }\n        const decoded = (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token);\n        if (!decoded || decoded.role !== 'business_owner') {\n            return _lib_apiResponse__WEBPACK_IMPORTED_MODULE_3__.apiResponse.error('Unauthorized access', 403);\n        }\n        const businessOwnerId = decoded.userId;\n        const { searchParams } = new URL(request.url);\n        const limit = parseInt(searchParams.get('limit')) || 5;\n        // Get recent bookings for this business owner\n        const recentBookings = await _models_Booking__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            businessOwner: businessOwnerId\n        }).populate('user', 'firstName lastName email').populate('service', 'title price').sort({\n            createdAt: -1\n        }).limit(limit);\n        // Format the data for the dashboard\n        const formattedBookings = recentBookings.map((booking)=>({\n                id: booking._id,\n                service: booking.service?.title || 'Unknown Service',\n                customer: `${booking.user?.firstName || ''} ${booking.user?.lastName || ''}`.trim() || 'Unknown Customer',\n                customerEmail: booking.user?.email || '',\n                date: booking.preferredDate,\n                createdAt: booking.createdAt,\n                status: booking.status,\n                amount: booking.service?.price || 0,\n                message: booking.message,\n                preferredTime: booking.preferredTime\n            }));\n        return _lib_apiResponse__WEBPACK_IMPORTED_MODULE_3__.apiResponse.success(formattedBookings, 'Recent bookings fetched successfully');\n    } catch (error) {\n        console.error('Error fetching recent bookings:', error);\n        return _lib_apiResponse__WEBPACK_IMPORTED_MODULE_3__.apiResponse.error('Failed to fetch recent bookings', 500);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/business/dashboard/recent-bookings/route.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/apiResponse.js":
/*!********************************!*\
  !*** ./src/lib/apiResponse.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiResponse: () => (/* binding */ apiResponse),\n/* harmony export */   validateRequest: () => (/* binding */ validateRequest),\n/* harmony export */   withErrorHandler: () => (/* binding */ withErrorHandler)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n/**\n * Standardized API response utility\n * Provides consistent response format across all API endpoints\n */ const apiResponse = {\n    /**\n   * Success response\n   * @param {any} data - Response data\n   * @param {string} message - Success message\n   * @param {number} statusCode - HTTP status code (default: 200)\n   * @returns {NextResponse} Formatted success response\n   */ success: (data = null, message = 'Success', statusCode = 200)=>{\n        const response = {\n            success: true,\n            message,\n            data,\n            timestamp: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: statusCode\n        });\n    },\n    /**\n   * Error response\n   * @param {string} message - Error message\n   * @param {number} statusCode - HTTP status code (default: 400)\n   * @param {any} errors - Additional error details\n   * @returns {NextResponse} Formatted error response\n   */ error: (message = 'An error occurred', statusCode = 400, errors = null)=>{\n        const response = {\n            success: false,\n            message,\n            errors,\n            timestamp: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: statusCode\n        });\n    },\n    /**\n   * Validation error response\n   * @param {Array|Object} validationErrors - Validation error details\n   * @param {string} message - Error message\n   * @returns {NextResponse} Formatted validation error response\n   */ validationError: (validationErrors, message = 'Validation failed')=>{\n        const response = {\n            success: false,\n            message,\n            errors: validationErrors,\n            timestamp: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 422\n        });\n    },\n    /**\n   * Unauthorized response\n   * @param {string} message - Error message\n   * @returns {NextResponse} Formatted unauthorized response\n   */ unauthorized: (message = 'Unauthorized access')=>{\n        const response = {\n            success: false,\n            message,\n            timestamp: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 401\n        });\n    },\n    /**\n   * Forbidden response\n   * @param {string} message - Error message\n   * @returns {NextResponse} Formatted forbidden response\n   */ forbidden: (message = 'Access forbidden')=>{\n        const response = {\n            success: false,\n            message,\n            timestamp: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 403\n        });\n    },\n    /**\n   * Not found response\n   * @param {string} message - Error message\n   * @returns {NextResponse} Formatted not found response\n   */ notFound: (message = 'Resource not found')=>{\n        const response = {\n            success: false,\n            message,\n            timestamp: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 404\n        });\n    },\n    /**\n   * Internal server error response\n   * @param {string} message - Error message\n   * @param {any} error - Error details (only in development)\n   * @returns {NextResponse} Formatted server error response\n   */ serverError: (message = 'Internal server error', error = null)=>{\n        const response = {\n            success: false,\n            message,\n            timestamp: new Date().toISOString()\n        };\n        // Include error details only in development\n        if ( true && error) {\n            response.error = {\n                message: error.message,\n                stack: error.stack\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 500\n        });\n    },\n    /**\n   * Rate limit exceeded response\n   * @param {string} message - Error message\n   * @param {number} retryAfter - Seconds to wait before retry\n   * @returns {NextResponse} Formatted rate limit response\n   */ rateLimitExceeded: (message = 'Rate limit exceeded', retryAfter = 60)=>{\n        const response = {\n            success: false,\n            message,\n            retryAfter,\n            timestamp: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 429,\n            headers: {\n                'Retry-After': retryAfter.toString()\n            }\n        });\n    },\n    /**\n   * Paginated response\n   * @param {Array} data - Array of items\n   * @param {Object} pagination - Pagination info\n   * @param {string} message - Success message\n   * @returns {NextResponse} Formatted paginated response\n   */ paginated: (data, pagination, message = 'Data retrieved successfully')=>{\n        const response = {\n            success: true,\n            message,\n            data,\n            pagination: {\n                page: pagination.page || 1,\n                limit: pagination.limit || 10,\n                total: pagination.total || 0,\n                pages: pagination.pages || 0,\n                hasNext: pagination.hasNext || false,\n                hasPrev: pagination.hasPrev || false\n            },\n            timestamp: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 200\n        });\n    },\n    /**\n   * Created response\n   * @param {any} data - Created resource data\n   * @param {string} message - Success message\n   * @returns {NextResponse} Formatted created response\n   */ created: (data, message = 'Resource created successfully')=>{\n        return apiResponse.success(data, message, 201);\n    },\n    /**\n   * Updated response\n   * @param {any} data - Updated resource data\n   * @param {string} message - Success message\n   * @returns {NextResponse} Formatted updated response\n   */ updated: (data, message = 'Resource updated successfully')=>{\n        return apiResponse.success(data, message, 200);\n    },\n    /**\n   * Deleted response\n   * @param {string} message - Success message\n   * @returns {NextResponse} Formatted deleted response\n   */ deleted: (message = 'Resource deleted successfully')=>{\n        return apiResponse.success(null, message, 200);\n    },\n    /**\n   * No content response\n   * @returns {NextResponse} Formatted no content response\n   */ noContent: ()=>{\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n            status: 204\n        });\n    }\n};\n/**\n * Error handler middleware for API routes\n * @param {Function} handler - API route handler\n * @returns {Function} Wrapped handler with error handling\n */ const withErrorHandler = (handler)=>{\n    return async (request, context)=>{\n        try {\n            return await handler(request, context);\n        } catch (error) {\n            console.error('API Error:', error);\n            // Handle specific error types\n            if (error.name === 'ValidationError') {\n                const validationErrors = Object.values(error.errors).map((err)=>({\n                        field: err.path,\n                        message: err.message\n                    }));\n                return apiResponse.validationError(validationErrors);\n            }\n            if (error.name === 'CastError') {\n                return apiResponse.error('Invalid ID format', 400);\n            }\n            if (error.code === 11000) {\n                const field = Object.keys(error.keyPattern)[0];\n                return apiResponse.error(`${field} already exists`, 409);\n            }\n            if (error.name === 'JsonWebTokenError') {\n                return apiResponse.unauthorized('Invalid token');\n            }\n            if (error.name === 'TokenExpiredError') {\n                return apiResponse.unauthorized('Token expired');\n            }\n            // Default server error\n            return apiResponse.serverError('An unexpected error occurred', error);\n        }\n    };\n};\n/**\n * Validation helper\n * @param {Object} data - Data to validate\n * @param {Object} rules - Validation rules\n * @returns {Object} Validation result\n */ const validateRequest = (data, rules)=>{\n    const errors = [];\n    for (const [field, rule] of Object.entries(rules)){\n        const value = data[field];\n        if (rule.required && (!value || value.toString().trim() === '')) {\n            errors.push({\n                field,\n                message: `${field} is required`\n            });\n            continue;\n        }\n        if (value && rule.type) {\n            if (rule.type === 'email' && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)) {\n                errors.push({\n                    field,\n                    message: `${field} must be a valid email`\n                });\n            }\n            if (rule.type === 'number' && isNaN(Number(value))) {\n                errors.push({\n                    field,\n                    message: `${field} must be a number`\n                });\n            }\n            if (rule.type === 'string' && typeof value !== 'string') {\n                errors.push({\n                    field,\n                    message: `${field} must be a string`\n                });\n            }\n        }\n        if (value && rule.minLength && value.toString().length < rule.minLength) {\n            errors.push({\n                field,\n                message: `${field} must be at least ${rule.minLength} characters`\n            });\n        }\n        if (value && rule.maxLength && value.toString().length > rule.maxLength) {\n            errors.push({\n                field,\n                message: `${field} cannot exceed ${rule.maxLength} characters`\n            });\n        }\n        if (value && rule.min && Number(value) < rule.min) {\n            errors.push({\n                field,\n                message: `${field} must be at least ${rule.min}`\n            });\n        }\n        if (value && rule.max && Number(value) > rule.max) {\n            errors.push({\n                field,\n                message: `${field} cannot exceed ${rule.max}`\n            });\n        }\n        if (value && rule.enum && !rule.enum.includes(value)) {\n            errors.push({\n                field,\n                message: `${field} must be one of: ${rule.enum.join(', ')}`\n            });\n        }\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/apiResponse.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-utils.js":
/*!*******************************!*\
  !*** ./src/lib/auth-utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RESPONSE_CODE: () => (/* binding */ RESPONSE_CODE),\n/* harmony export */   RESPONSE_FAILURE: () => (/* binding */ RESPONSE_FAILURE),\n/* harmony export */   RESPONSE_SUCCESS: () => (/* binding */ RESPONSE_SUCCESS),\n/* harmony export */   comparePassword: () => (/* binding */ comparePassword),\n/* harmony export */   generateOtp: () => (/* binding */ generateOtp),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   sendOtpMail: () => (/* binding */ sendOtpMail),\n/* harmony export */   sendResponse: () => (/* binding */ sendResponse),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n\n\n\n// Generate random OTP\nfunction generateOtp() {\n    return Math.floor(100000 + Math.random() * 900000).toString();\n}\n// Hash password\nasync function hashPassword(password) {\n    const saltRounds = 10;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, saltRounds);\n}\n// Compare password\nasync function comparePassword(password, hashedPassword) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\n// Sign JWT token\nfunction signToken(payload) {\n    const secret = process.env.JWT_SECRET || 'your-secret-key';\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n        expiresIn: '7d'\n    });\n}\n// Verify JWT token\nfunction verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET || 'your-secret-key';\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n    } catch (error) {\n        console.error('Token verification error:', error.message);\n        return null;\n    }\n}\n// Create email transporter\nfunction createTransporter() {\n    return nodemailer__WEBPACK_IMPORTED_MODULE_2__.createTransport({\n        service: 'gmail',\n        auth: {\n            user: process.env.EMAIL_USER,\n            pass: process.env.EMAIL_PASS\n        }\n    });\n}\n// Send OTP email\nasync function sendOtpMail(email, firstName, lastName, otp) {\n    try {\n        // For development, just log the OTP\n        if (true) {\n            console.log(`OTP for ${email}: ${otp}`);\n            return;\n        }\n        const transporter = createTransporter();\n        const mailOptions = {\n            from: process.env.EMAIL_USER,\n            to: email,\n            subject: 'BookMyService - Email Verification',\n            html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h2 style=\"color: #3C50E0;\">BookMyService Email Verification</h2>\n          <p>Hello ${firstName},</p>\n          <p>Thank you for registering with BookMyService. Please use the following OTP to verify your email address:</p>\n          <div style=\"background-color: #f8f9fa; padding: 20px; text-align: center; margin: 20px 0;\">\n            <h1 style=\"color: #3C50E0; font-size: 32px; margin: 0;\">${otp}</h1>\n          </div>\n          <p>This OTP will expire in 5 minutes.</p>\n          <p>If you didn't request this verification, please ignore this email.</p>\n          <p>Best regards,<br>BookMyService Team</p>\n        </div>\n      `\n        };\n        await transporter.sendMail(mailOptions);\n        console.log('OTP email sent successfully');\n    } catch (error) {\n        console.error('Error sending OTP email:', error);\n        throw new Error('Failed to send OTP email');\n    }\n}\n// Send response helper\nfunction sendResponse(res, data, message, success, statusCode) {\n    return res.status(statusCode).json({\n        success,\n        message,\n        data\n    });\n}\n// Response constants\nconst RESPONSE_SUCCESS = true;\nconst RESPONSE_FAILURE = false;\nconst RESPONSE_CODE = {\n    SUCCESS: 200,\n    CREATED: 201,\n    BAD_REQUEST: 400,\n    UNAUTHORISED: 401,\n    FORBIDDEN: 403,\n    NOT_FOUND: 404,\n    INTERNAL_SERVER_ERROR: 500\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgtdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDQztBQUNLO0FBRXBDLHNCQUFzQjtBQUNmLFNBQVNHO0lBQ2QsT0FBT0MsS0FBS0MsS0FBSyxDQUFDLFNBQVNELEtBQUtFLE1BQU0sS0FBSyxRQUFRQyxRQUFRO0FBQzdEO0FBRUEsZ0JBQWdCO0FBQ1QsZUFBZUMsYUFBYUMsUUFBUTtJQUN6QyxNQUFNQyxhQUFhO0lBQ25CLE9BQU8sTUFBTVYscURBQVcsQ0FBQ1MsVUFBVUM7QUFDckM7QUFFQSxtQkFBbUI7QUFDWixlQUFlRSxnQkFBZ0JILFFBQVEsRUFBRUksY0FBYztJQUM1RCxPQUFPLE1BQU1iLHdEQUFjLENBQUNTLFVBQVVJO0FBQ3hDO0FBRUEsaUJBQWlCO0FBQ1YsU0FBU0UsVUFBVUMsT0FBTztJQUMvQixNQUFNQyxTQUFTQyxRQUFRQyxHQUFHLENBQUNDLFVBQVUsSUFBSTtJQUN6QyxPQUFPbkIsd0RBQVEsQ0FBQ2UsU0FBU0MsUUFBUTtRQUFFSyxXQUFXO0lBQUs7QUFDckQ7QUFFQSxtQkFBbUI7QUFDWixTQUFTQyxZQUFZQyxLQUFLO0lBQy9CLElBQUk7UUFDRixNQUFNUCxTQUFTQyxRQUFRQyxHQUFHLENBQUNDLFVBQVUsSUFBSTtRQUN6QyxPQUFPbkIsMERBQVUsQ0FBQ3VCLE9BQU9QO0lBQzNCLEVBQUUsT0FBT1MsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQSxNQUFNRSxPQUFPO1FBQ3hELE9BQU87SUFDVDtBQUNGO0FBRUEsMkJBQTJCO0FBQzNCLFNBQVNDO0lBQ1AsT0FBTzNCLHVEQUEwQixDQUFDO1FBQ2hDNkIsU0FBUztRQUNUQyxNQUFNO1lBQ0pDLE1BQU1mLFFBQVFDLEdBQUcsQ0FBQ2UsVUFBVTtZQUM1QkMsTUFBTWpCLFFBQVFDLEdBQUcsQ0FBQ2lCLFVBQVU7UUFDOUI7SUFDRjtBQUNGO0FBRUEsaUJBQWlCO0FBQ1YsZUFBZUMsWUFBWUMsS0FBSyxFQUFFQyxTQUFTLEVBQUVDLFFBQVEsRUFBRUMsR0FBRztJQUMvRCxJQUFJO1FBQ0Ysb0NBQW9DO1FBQ3BDLElBQUl2QixJQUFzQyxFQUFFO1lBQzFDUyxRQUFRZSxHQUFHLENBQUMsQ0FBQyxRQUFRLEVBQUVKLE1BQU0sRUFBRSxFQUFFRyxLQUFLO1lBQ3RDO1FBQ0Y7UUFFQSxNQUFNRSxjQUFjZDtRQUVwQixNQUFNZSxjQUFjO1lBQ2xCQyxNQUFNM0IsUUFBUUMsR0FBRyxDQUFDZSxVQUFVO1lBQzVCWSxJQUFJUjtZQUNKUyxTQUFTO1lBQ1RDLE1BQU0sQ0FBQzs7O21CQUdNLEVBQUVULFVBQVU7OztvRUFHcUMsRUFBRUUsSUFBSTs7Ozs7O01BTXBFLENBQUM7UUFDSDtRQUVBLE1BQU1FLFlBQVlNLFFBQVEsQ0FBQ0w7UUFDM0JqQixRQUFRZSxHQUFHLENBQUM7SUFDZCxFQUFFLE9BQU9oQixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE1BQU0sSUFBSXdCLE1BQU07SUFDbEI7QUFDRjtBQUVBLHVCQUF1QjtBQUNoQixTQUFTQyxhQUFhQyxHQUFHLEVBQUVDLElBQUksRUFBRXpCLE9BQU8sRUFBRTBCLE9BQU8sRUFBRUMsVUFBVTtJQUNsRSxPQUFPSCxJQUFJSSxNQUFNLENBQUNELFlBQVlFLElBQUksQ0FBQztRQUNqQ0g7UUFDQTFCO1FBQ0F5QjtJQUNGO0FBQ0Y7QUFFQSxxQkFBcUI7QUFDZCxNQUFNSyxtQkFBbUIsS0FBSztBQUM5QixNQUFNQyxtQkFBbUIsTUFBTTtBQUUvQixNQUFNQyxnQkFBZ0I7SUFDM0JDLFNBQVM7SUFDVEMsU0FBUztJQUNUQyxhQUFhO0lBQ2JDLGNBQWM7SUFDZEMsV0FBVztJQUNYQyxXQUFXO0lBQ1hDLHVCQUF1QjtBQUN6QixFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEJodXNoYW4gcGF0aWxcXE9uZURyaXZlXFxEZXNrdG9wXFxCb29rIG15IFNlcnZpY2UgbmV3XFxuZXh0anMtYWRtaW4tZGFzaGJvYXJkLW1haW5cXHNyY1xcbGliXFxhdXRoLXV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0anMnO1xuaW1wb3J0IGp3dCBmcm9tICdqc29ud2VidG9rZW4nO1xuaW1wb3J0IG5vZGVtYWlsZXIgZnJvbSAnbm9kZW1haWxlcic7XG5cbi8vIEdlbmVyYXRlIHJhbmRvbSBPVFBcbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZU90cCgpIHtcbiAgcmV0dXJuIE1hdGguZmxvb3IoMTAwMDAwICsgTWF0aC5yYW5kb20oKSAqIDkwMDAwMCkudG9TdHJpbmcoKTtcbn1cblxuLy8gSGFzaCBwYXNzd29yZFxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGhhc2hQYXNzd29yZChwYXNzd29yZCkge1xuICBjb25zdCBzYWx0Um91bmRzID0gMTA7XG4gIHJldHVybiBhd2FpdCBiY3J5cHQuaGFzaChwYXNzd29yZCwgc2FsdFJvdW5kcyk7XG59XG5cbi8vIENvbXBhcmUgcGFzc3dvcmRcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjb21wYXJlUGFzc3dvcmQocGFzc3dvcmQsIGhhc2hlZFBhc3N3b3JkKSB7XG4gIHJldHVybiBhd2FpdCBiY3J5cHQuY29tcGFyZShwYXNzd29yZCwgaGFzaGVkUGFzc3dvcmQpO1xufVxuXG4vLyBTaWduIEpXVCB0b2tlblxuZXhwb3J0IGZ1bmN0aW9uIHNpZ25Ub2tlbihwYXlsb2FkKSB7XG4gIGNvbnN0IHNlY3JldCA9IHByb2Nlc3MuZW52LkpXVF9TRUNSRVQgfHwgJ3lvdXItc2VjcmV0LWtleSc7XG4gIHJldHVybiBqd3Quc2lnbihwYXlsb2FkLCBzZWNyZXQsIHsgZXhwaXJlc0luOiAnN2QnIH0pO1xufVxuXG4vLyBWZXJpZnkgSldUIHRva2VuXG5leHBvcnQgZnVuY3Rpb24gdmVyaWZ5VG9rZW4odG9rZW4pIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzZWNyZXQgPSBwcm9jZXNzLmVudi5KV1RfU0VDUkVUIHx8ICd5b3VyLXNlY3JldC1rZXknO1xuICAgIHJldHVybiBqd3QudmVyaWZ5KHRva2VuLCBzZWNyZXQpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIHZlcmlmaWNhdGlvbiBlcnJvcjonLCBlcnJvci5tZXNzYWdlKTtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxufVxuXG4vLyBDcmVhdGUgZW1haWwgdHJhbnNwb3J0ZXJcbmZ1bmN0aW9uIGNyZWF0ZVRyYW5zcG9ydGVyKCkge1xuICByZXR1cm4gbm9kZW1haWxlci5jcmVhdGVUcmFuc3BvcnQoe1xuICAgIHNlcnZpY2U6ICdnbWFpbCcsXG4gICAgYXV0aDoge1xuICAgICAgdXNlcjogcHJvY2Vzcy5lbnYuRU1BSUxfVVNFUixcbiAgICAgIHBhc3M6IHByb2Nlc3MuZW52LkVNQUlMX1BBU1MsXG4gICAgfSxcbiAgfSk7XG59XG5cbi8vIFNlbmQgT1RQIGVtYWlsXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc2VuZE90cE1haWwoZW1haWwsIGZpcnN0TmFtZSwgbGFzdE5hbWUsIG90cCkge1xuICB0cnkge1xuICAgIC8vIEZvciBkZXZlbG9wbWVudCwganVzdCBsb2cgdGhlIE9UUFxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xuICAgICAgY29uc29sZS5sb2coYE9UUCBmb3IgJHtlbWFpbH06ICR7b3RwfWApO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnN0IHRyYW5zcG9ydGVyID0gY3JlYXRlVHJhbnNwb3J0ZXIoKTtcbiAgICBcbiAgICBjb25zdCBtYWlsT3B0aW9ucyA9IHtcbiAgICAgIGZyb206IHByb2Nlc3MuZW52LkVNQUlMX1VTRVIsXG4gICAgICB0bzogZW1haWwsXG4gICAgICBzdWJqZWN0OiAnQm9va015U2VydmljZSAtIEVtYWlsIFZlcmlmaWNhdGlvbicsXG4gICAgICBodG1sOiBgXG4gICAgICAgIDxkaXYgc3R5bGU9XCJmb250LWZhbWlseTogQXJpYWwsIHNhbnMtc2VyaWY7IG1heC13aWR0aDogNjAwcHg7IG1hcmdpbjogMCBhdXRvO1wiPlxuICAgICAgICAgIDxoMiBzdHlsZT1cImNvbG9yOiAjM0M1MEUwO1wiPkJvb2tNeVNlcnZpY2UgRW1haWwgVmVyaWZpY2F0aW9uPC9oMj5cbiAgICAgICAgICA8cD5IZWxsbyAke2ZpcnN0TmFtZX0sPC9wPlxuICAgICAgICAgIDxwPlRoYW5rIHlvdSBmb3IgcmVnaXN0ZXJpbmcgd2l0aCBCb29rTXlTZXJ2aWNlLiBQbGVhc2UgdXNlIHRoZSBmb2xsb3dpbmcgT1RQIHRvIHZlcmlmeSB5b3VyIGVtYWlsIGFkZHJlc3M6PC9wPlxuICAgICAgICAgIDxkaXYgc3R5bGU9XCJiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOyBwYWRkaW5nOiAyMHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7IG1hcmdpbjogMjBweCAwO1wiPlxuICAgICAgICAgICAgPGgxIHN0eWxlPVwiY29sb3I6ICMzQzUwRTA7IGZvbnQtc2l6ZTogMzJweDsgbWFyZ2luOiAwO1wiPiR7b3RwfTwvaDE+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPHA+VGhpcyBPVFAgd2lsbCBleHBpcmUgaW4gNSBtaW51dGVzLjwvcD5cbiAgICAgICAgICA8cD5JZiB5b3UgZGlkbid0IHJlcXVlc3QgdGhpcyB2ZXJpZmljYXRpb24sIHBsZWFzZSBpZ25vcmUgdGhpcyBlbWFpbC48L3A+XG4gICAgICAgICAgPHA+QmVzdCByZWdhcmRzLDxicj5Cb29rTXlTZXJ2aWNlIFRlYW08L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgYCxcbiAgICB9O1xuXG4gICAgYXdhaXQgdHJhbnNwb3J0ZXIuc2VuZE1haWwobWFpbE9wdGlvbnMpO1xuICAgIGNvbnNvbGUubG9nKCdPVFAgZW1haWwgc2VudCBzdWNjZXNzZnVsbHknKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZW5kaW5nIE9UUCBlbWFpbDonLCBlcnJvcik7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gc2VuZCBPVFAgZW1haWwnKTtcbiAgfVxufVxuXG4vLyBTZW5kIHJlc3BvbnNlIGhlbHBlclxuZXhwb3J0IGZ1bmN0aW9uIHNlbmRSZXNwb25zZShyZXMsIGRhdGEsIG1lc3NhZ2UsIHN1Y2Nlc3MsIHN0YXR1c0NvZGUpIHtcbiAgcmV0dXJuIHJlcy5zdGF0dXMoc3RhdHVzQ29kZSkuanNvbih7XG4gICAgc3VjY2VzcyxcbiAgICBtZXNzYWdlLFxuICAgIGRhdGEsXG4gIH0pO1xufVxuXG4vLyBSZXNwb25zZSBjb25zdGFudHNcbmV4cG9ydCBjb25zdCBSRVNQT05TRV9TVUNDRVNTID0gdHJ1ZTtcbmV4cG9ydCBjb25zdCBSRVNQT05TRV9GQUlMVVJFID0gZmFsc2U7XG5cbmV4cG9ydCBjb25zdCBSRVNQT05TRV9DT0RFID0ge1xuICBTVUNDRVNTOiAyMDAsXG4gIENSRUFURUQ6IDIwMSxcbiAgQkFEX1JFUVVFU1Q6IDQwMCxcbiAgVU5BVVRIT1JJU0VEOiA0MDEsXG4gIEZPUkJJRERFTjogNDAzLFxuICBOT1RfRk9VTkQ6IDQwNCxcbiAgSU5URVJOQUxfU0VSVkVSX0VSUk9SOiA1MDAsXG59O1xuIl0sIm5hbWVzIjpbImJjcnlwdCIsImp3dCIsIm5vZGVtYWlsZXIiLCJnZW5lcmF0ZU90cCIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsInRvU3RyaW5nIiwiaGFzaFBhc3N3b3JkIiwicGFzc3dvcmQiLCJzYWx0Um91bmRzIiwiaGFzaCIsImNvbXBhcmVQYXNzd29yZCIsImhhc2hlZFBhc3N3b3JkIiwiY29tcGFyZSIsInNpZ25Ub2tlbiIsInBheWxvYWQiLCJzZWNyZXQiLCJwcm9jZXNzIiwiZW52IiwiSldUX1NFQ1JFVCIsInNpZ24iLCJleHBpcmVzSW4iLCJ2ZXJpZnlUb2tlbiIsInRva2VuIiwidmVyaWZ5IiwiZXJyb3IiLCJjb25zb2xlIiwibWVzc2FnZSIsImNyZWF0ZVRyYW5zcG9ydGVyIiwiY3JlYXRlVHJhbnNwb3J0Iiwic2VydmljZSIsImF1dGgiLCJ1c2VyIiwiRU1BSUxfVVNFUiIsInBhc3MiLCJFTUFJTF9QQVNTIiwic2VuZE90cE1haWwiLCJlbWFpbCIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwib3RwIiwibG9nIiwidHJhbnNwb3J0ZXIiLCJtYWlsT3B0aW9ucyIsImZyb20iLCJ0byIsInN1YmplY3QiLCJodG1sIiwic2VuZE1haWwiLCJFcnJvciIsInNlbmRSZXNwb25zZSIsInJlcyIsImRhdGEiLCJzdWNjZXNzIiwic3RhdHVzQ29kZSIsInN0YXR1cyIsImpzb24iLCJSRVNQT05TRV9TVUNDRVNTIiwiUkVTUE9OU0VfRkFJTFVSRSIsIlJFU1BPTlNFX0NPREUiLCJTVUNDRVNTIiwiQ1JFQVRFRCIsIkJBRF9SRVFVRVNUIiwiVU5BVVRIT1JJU0VEIiwiRk9SQklEREVOIiwiTk9UX0ZPVU5EIiwiSU5URVJOQUxfU0VSVkVSX0VSUk9SIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-utils.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.js":
/*!****************************!*\
  !*** ./src/lib/mongodb.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.js\n");

/***/ }),

/***/ "(rsc)/./src/models/Booking.js":
/*!*******************************!*\
  !*** ./src/models/Booking.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst bookingSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    // Customer who made the booking\n    user: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: 'User',\n        required: true\n    },\n    // Service being booked\n    service: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: 'Service',\n        required: true\n    },\n    // Business owner providing the service\n    businessOwner: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: 'User',\n        required: true\n    },\n    // Booking details\n    preferredDate: {\n        type: Date,\n        required: true\n    },\n    preferredTime: {\n        type: String,\n        required: false\n    },\n    // Customer message/requirements\n    message: {\n        type: String,\n        trim: true,\n        maxlength: 1000\n    },\n    // Booking status\n    status: {\n        type: String,\n        enum: [\n            'pending',\n            'confirmed',\n            'rejected',\n            'in_progress',\n            'completed',\n            'cancelled'\n        ],\n        default: 'pending'\n    },\n    // Financial details\n    totalAmount: {\n        type: Number,\n        required: true,\n        min: 0\n    },\n    // Payment details\n    paymentStatus: {\n        type: String,\n        enum: [\n            'pending',\n            'paid',\n            'refunded',\n            'failed'\n        ],\n        default: 'pending'\n    },\n    paymentMethod: {\n        type: String,\n        enum: [\n            'cash',\n            'card',\n            'bank_transfer',\n            'paypal',\n            'stripe'\n        ],\n        required: false\n    },\n    paymentTransactionId: {\n        type: String,\n        required: false\n    },\n    // Service delivery details\n    actualStartTime: {\n        type: Date,\n        required: false\n    },\n    actualEndTime: {\n        type: Date,\n        required: false\n    },\n    // Customer address (if service is at customer location)\n    customerAddress: {\n        street: String,\n        city: String,\n        state: String,\n        zipCode: String,\n        country: String,\n        coordinates: {\n            latitude: Number,\n            longitude: Number\n        }\n    },\n    // Rating and review (after service completion)\n    rating: {\n        type: Number,\n        min: 1,\n        max: 5,\n        required: false\n    },\n    review: {\n        type: String,\n        trim: true,\n        maxlength: 1000,\n        required: false\n    },\n    // Business owner response to review\n    businessOwnerResponse: {\n        type: String,\n        trim: true,\n        maxlength: 500,\n        required: false\n    },\n    // Timestamps for different status changes\n    confirmedAt: {\n        type: Date,\n        required: false\n    },\n    rejectedAt: {\n        type: Date,\n        required: false\n    },\n    startedAt: {\n        type: Date,\n        required: false\n    },\n    completedAt: {\n        type: Date,\n        required: false\n    },\n    cancelledAt: {\n        type: Date,\n        required: false\n    },\n    cancelledBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: 'User',\n        required: false\n    },\n    // Cancellation reason\n    cancellationReason: {\n        type: String,\n        trim: true,\n        maxlength: 500,\n        required: false\n    },\n    // Additional notes from business owner\n    businessOwnerNotes: {\n        type: String,\n        trim: true,\n        maxlength: 1000,\n        required: false\n    },\n    // Special requirements or instructions\n    specialRequirements: {\n        type: String,\n        trim: true,\n        maxlength: 500,\n        required: false\n    },\n    // Estimated duration (in minutes)\n    estimatedDuration: {\n        type: Number,\n        min: 15,\n        required: false\n    },\n    // Actual duration (in minutes)\n    actualDuration: {\n        type: Number,\n        min: 0,\n        required: false\n    },\n    // Communication history\n    communications: [\n        {\n            from: {\n                type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n                ref: 'User',\n                required: true\n            },\n            message: {\n                type: String,\n                required: true,\n                trim: true,\n                maxlength: 1000\n            },\n            timestamp: {\n                type: Date,\n                default: Date.now\n            },\n            type: {\n                type: String,\n                enum: [\n                    'message',\n                    'status_update',\n                    'system'\n                ],\n                default: 'message'\n            }\n        }\n    ],\n    // Attachments (photos, documents, etc.)\n    attachments: [\n        {\n            filename: String,\n            url: String,\n            type: {\n                type: String,\n                enum: [\n                    'image',\n                    'document',\n                    'other'\n                ]\n            },\n            uploadedBy: {\n                type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n                ref: 'User'\n            },\n            uploadedAt: {\n                type: Date,\n                default: Date.now\n            }\n        }\n    ],\n    // Metadata\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    // For soft delete\n    isDeleted: {\n        type: Boolean,\n        default: false\n    },\n    // Track if service was deleted after booking\n    serviceDeleted: {\n        type: Boolean,\n        default: false\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nbookingSchema.index({\n    user: 1,\n    createdAt: -1\n});\nbookingSchema.index({\n    businessOwner: 1,\n    createdAt: -1\n});\nbookingSchema.index({\n    service: 1,\n    createdAt: -1\n});\nbookingSchema.index({\n    status: 1,\n    createdAt: -1\n});\nbookingSchema.index({\n    preferredDate: 1\n});\nbookingSchema.index({\n    paymentStatus: 1\n});\n// Virtual for booking duration in a readable format\nbookingSchema.virtual('durationFormatted').get(function() {\n    if (!this.actualDuration) return null;\n    const hours = Math.floor(this.actualDuration / 60);\n    const minutes = this.actualDuration % 60;\n    if (hours > 0) {\n        return `${hours}h ${minutes}m`;\n    }\n    return `${minutes}m`;\n});\n// Virtual for booking age\nbookingSchema.virtual('ageInDays').get(function() {\n    const now = new Date();\n    const created = this.createdAt;\n    const diffTime = Math.abs(now - created);\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n});\n// Pre-save middleware\nbookingSchema.pre('save', function(next) {\n    // Calculate actual duration if both start and end times are set\n    if (this.actualStartTime && this.actualEndTime) {\n        const diffMs = this.actualEndTime - this.actualStartTime;\n        this.actualDuration = Math.round(diffMs / (1000 * 60)); // Convert to minutes\n    }\n    next();\n});\n// Static methods\nbookingSchema.statics.getBookingStats = async function(businessOwnerId, startDate, endDate) {\n    const matchStage = {\n        businessOwner: businessOwnerId,\n        isDeleted: false\n    };\n    if (startDate && endDate) {\n        matchStage.createdAt = {\n            $gte: new Date(startDate),\n            $lte: new Date(endDate)\n        };\n    }\n    return await this.aggregate([\n        {\n            $match: matchStage\n        },\n        {\n            $group: {\n                _id: '$status',\n                count: {\n                    $sum: 1\n                },\n                totalRevenue: {\n                    $sum: {\n                        $cond: [\n                            {\n                                $eq: [\n                                    '$status',\n                                    'completed'\n                                ]\n                            },\n                            '$totalAmount',\n                            0\n                        ]\n                    }\n                }\n            }\n        }\n    ]);\n};\n// Instance methods\nbookingSchema.methods.canBeCancelled = function() {\n    return [\n        'pending',\n        'confirmed'\n    ].includes(this.status);\n};\nbookingSchema.methods.canBeModified = function() {\n    return [\n        'pending'\n    ].includes(this.status);\n};\nbookingSchema.methods.addCommunication = function(fromUserId, message, type = 'message') {\n    this.communications.push({\n        from: fromUserId,\n        message,\n        type,\n        timestamp: new Date()\n    });\n    return this.save();\n};\nconst Booking = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Booking || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Booking', bookingSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Booking);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbW9kZWxzL0Jvb2tpbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBRWhDLE1BQU1DLGdCQUFnQixJQUFJRCx3REFBZSxDQUFDO0lBQ3hDLGdDQUFnQztJQUNoQ0csTUFBTTtRQUNKQyxNQUFNSix3REFBZSxDQUFDSyxLQUFLLENBQUNDLFFBQVE7UUFDcENDLEtBQUs7UUFDTEMsVUFBVTtJQUNaO0lBRUEsdUJBQXVCO0lBQ3ZCQyxTQUFTO1FBQ1BMLE1BQU1KLHdEQUFlLENBQUNLLEtBQUssQ0FBQ0MsUUFBUTtRQUNwQ0MsS0FBSztRQUNMQyxVQUFVO0lBQ1o7SUFFQSx1Q0FBdUM7SUFDdkNFLGVBQWU7UUFDYk4sTUFBTUosd0RBQWUsQ0FBQ0ssS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xDLFVBQVU7SUFDWjtJQUVBLGtCQUFrQjtJQUNsQkcsZUFBZTtRQUNiUCxNQUFNUTtRQUNOSixVQUFVO0lBQ1o7SUFFQUssZUFBZTtRQUNiVCxNQUFNVTtRQUNOTixVQUFVO0lBQ1o7SUFFQSxnQ0FBZ0M7SUFDaENPLFNBQVM7UUFDUFgsTUFBTVU7UUFDTkUsTUFBTTtRQUNOQyxXQUFXO0lBQ2I7SUFFQSxpQkFBaUI7SUFDakJDLFFBQVE7UUFDTmQsTUFBTVU7UUFDTkssTUFBTTtZQUFDO1lBQVc7WUFBYTtZQUFZO1lBQWU7WUFBYTtTQUFZO1FBQ25GQyxTQUFTO0lBQ1g7SUFFQSxvQkFBb0I7SUFDcEJDLGFBQWE7UUFDWGpCLE1BQU1rQjtRQUNOZCxVQUFVO1FBQ1ZlLEtBQUs7SUFDUDtJQUVBLGtCQUFrQjtJQUNsQkMsZUFBZTtRQUNicEIsTUFBTVU7UUFDTkssTUFBTTtZQUFDO1lBQVc7WUFBUTtZQUFZO1NBQVM7UUFDL0NDLFNBQVM7SUFDWDtJQUVBSyxlQUFlO1FBQ2JyQixNQUFNVTtRQUNOSyxNQUFNO1lBQUM7WUFBUTtZQUFRO1lBQWlCO1lBQVU7U0FBUztRQUMzRFgsVUFBVTtJQUNaO0lBRUFrQixzQkFBc0I7UUFDcEJ0QixNQUFNVTtRQUNOTixVQUFVO0lBQ1o7SUFFQSwyQkFBMkI7SUFDM0JtQixpQkFBaUI7UUFDZnZCLE1BQU1RO1FBQ05KLFVBQVU7SUFDWjtJQUVBb0IsZUFBZTtRQUNieEIsTUFBTVE7UUFDTkosVUFBVTtJQUNaO0lBRUEsd0RBQXdEO0lBQ3hEcUIsaUJBQWlCO1FBQ2ZDLFFBQVFoQjtRQUNSaUIsTUFBTWpCO1FBQ05rQixPQUFPbEI7UUFDUG1CLFNBQVNuQjtRQUNUb0IsU0FBU3BCO1FBQ1RxQixhQUFhO1lBQ1hDLFVBQVVkO1lBQ1ZlLFdBQVdmO1FBQ2I7SUFDRjtJQUVBLCtDQUErQztJQUMvQ2dCLFFBQVE7UUFDTmxDLE1BQU1rQjtRQUNOQyxLQUFLO1FBQ0xnQixLQUFLO1FBQ0wvQixVQUFVO0lBQ1o7SUFFQWdDLFFBQVE7UUFDTnBDLE1BQU1VO1FBQ05FLE1BQU07UUFDTkMsV0FBVztRQUNYVCxVQUFVO0lBQ1o7SUFFQSxvQ0FBb0M7SUFDcENpQyx1QkFBdUI7UUFDckJyQyxNQUFNVTtRQUNORSxNQUFNO1FBQ05DLFdBQVc7UUFDWFQsVUFBVTtJQUNaO0lBRUEsMENBQTBDO0lBQzFDa0MsYUFBYTtRQUNYdEMsTUFBTVE7UUFDTkosVUFBVTtJQUNaO0lBRUFtQyxZQUFZO1FBQ1Z2QyxNQUFNUTtRQUNOSixVQUFVO0lBQ1o7SUFFQW9DLFdBQVc7UUFDVHhDLE1BQU1RO1FBQ05KLFVBQVU7SUFDWjtJQUVBcUMsYUFBYTtRQUNYekMsTUFBTVE7UUFDTkosVUFBVTtJQUNaO0lBRUFzQyxhQUFhO1FBQ1gxQyxNQUFNUTtRQUNOSixVQUFVO0lBQ1o7SUFFQXVDLGFBQWE7UUFDWDNDLE1BQU1KLHdEQUFlLENBQUNLLEtBQUssQ0FBQ0MsUUFBUTtRQUNwQ0MsS0FBSztRQUNMQyxVQUFVO0lBQ1o7SUFFQSxzQkFBc0I7SUFDdEJ3QyxvQkFBb0I7UUFDbEI1QyxNQUFNVTtRQUNORSxNQUFNO1FBQ05DLFdBQVc7UUFDWFQsVUFBVTtJQUNaO0lBRUEsdUNBQXVDO0lBQ3ZDeUMsb0JBQW9CO1FBQ2xCN0MsTUFBTVU7UUFDTkUsTUFBTTtRQUNOQyxXQUFXO1FBQ1hULFVBQVU7SUFDWjtJQUVBLHVDQUF1QztJQUN2QzBDLHFCQUFxQjtRQUNuQjlDLE1BQU1VO1FBQ05FLE1BQU07UUFDTkMsV0FBVztRQUNYVCxVQUFVO0lBQ1o7SUFFQSxrQ0FBa0M7SUFDbEMyQyxtQkFBbUI7UUFDakIvQyxNQUFNa0I7UUFDTkMsS0FBSztRQUNMZixVQUFVO0lBQ1o7SUFFQSwrQkFBK0I7SUFDL0I0QyxnQkFBZ0I7UUFDZGhELE1BQU1rQjtRQUNOQyxLQUFLO1FBQ0xmLFVBQVU7SUFDWjtJQUVBLHdCQUF3QjtJQUN4QjZDLGdCQUFnQjtRQUFDO1lBQ2ZDLE1BQU07Z0JBQ0psRCxNQUFNSix3REFBZSxDQUFDSyxLQUFLLENBQUNDLFFBQVE7Z0JBQ3BDQyxLQUFLO2dCQUNMQyxVQUFVO1lBQ1o7WUFDQU8sU0FBUztnQkFDUFgsTUFBTVU7Z0JBQ05OLFVBQVU7Z0JBQ1ZRLE1BQU07Z0JBQ05DLFdBQVc7WUFDYjtZQUNBc0MsV0FBVztnQkFDVG5ELE1BQU1RO2dCQUNOUSxTQUFTUixLQUFLNEMsR0FBRztZQUNuQjtZQUNBcEQsTUFBTTtnQkFDSkEsTUFBTVU7Z0JBQ05LLE1BQU07b0JBQUM7b0JBQVc7b0JBQWlCO2lCQUFTO2dCQUM1Q0MsU0FBUztZQUNYO1FBQ0Y7S0FBRTtJQUVGLHdDQUF3QztJQUN4Q3FDLGFBQWE7UUFBQztZQUNaQyxVQUFVNUM7WUFDVjZDLEtBQUs3QztZQUNMVixNQUFNO2dCQUNKQSxNQUFNVTtnQkFDTkssTUFBTTtvQkFBQztvQkFBUztvQkFBWTtpQkFBUTtZQUN0QztZQUNBeUMsWUFBWTtnQkFDVnhELE1BQU1KLHdEQUFlLENBQUNLLEtBQUssQ0FBQ0MsUUFBUTtnQkFDcENDLEtBQUs7WUFDUDtZQUNBc0QsWUFBWTtnQkFDVnpELE1BQU1RO2dCQUNOUSxTQUFTUixLQUFLNEMsR0FBRztZQUNuQjtRQUNGO0tBQUU7SUFFRixXQUFXO0lBQ1hNLFVBQVU7UUFDUjFELE1BQU0yRDtRQUNOM0MsU0FBUztJQUNYO0lBRUEsa0JBQWtCO0lBQ2xCNEMsV0FBVztRQUNUNUQsTUFBTTJEO1FBQ04zQyxTQUFTO0lBQ1g7SUFFQSw2Q0FBNkM7SUFDN0M2QyxnQkFBZ0I7UUFDZDdELE1BQU0yRDtRQUNOM0MsU0FBUztJQUNYO0FBQ0YsR0FBRztJQUNEOEMsWUFBWTtJQUNaQyxRQUFRO1FBQUVDLFVBQVU7SUFBSztJQUN6QkMsVUFBVTtRQUFFRCxVQUFVO0lBQUs7QUFDN0I7QUFFQSx1Q0FBdUM7QUFDdkNuRSxjQUFjcUUsS0FBSyxDQUFDO0lBQUVuRSxNQUFNO0lBQUdvRSxXQUFXLENBQUM7QUFBRTtBQUM3Q3RFLGNBQWNxRSxLQUFLLENBQUM7SUFBRTVELGVBQWU7SUFBRzZELFdBQVcsQ0FBQztBQUFFO0FBQ3REdEUsY0FBY3FFLEtBQUssQ0FBQztJQUFFN0QsU0FBUztJQUFHOEQsV0FBVyxDQUFDO0FBQUU7QUFDaER0RSxjQUFjcUUsS0FBSyxDQUFDO0lBQUVwRCxRQUFRO0lBQUdxRCxXQUFXLENBQUM7QUFBRTtBQUMvQ3RFLGNBQWNxRSxLQUFLLENBQUM7SUFBRTNELGVBQWU7QUFBRTtBQUN2Q1YsY0FBY3FFLEtBQUssQ0FBQztJQUFFOUMsZUFBZTtBQUFFO0FBRXZDLG9EQUFvRDtBQUNwRHZCLGNBQWN1RSxPQUFPLENBQUMscUJBQXFCQyxHQUFHLENBQUM7SUFDN0MsSUFBSSxDQUFDLElBQUksQ0FBQ3JCLGNBQWMsRUFBRSxPQUFPO0lBRWpDLE1BQU1zQixRQUFRQyxLQUFLQyxLQUFLLENBQUMsSUFBSSxDQUFDeEIsY0FBYyxHQUFHO0lBQy9DLE1BQU15QixVQUFVLElBQUksQ0FBQ3pCLGNBQWMsR0FBRztJQUV0QyxJQUFJc0IsUUFBUSxHQUFHO1FBQ2IsT0FBTyxHQUFHQSxNQUFNLEVBQUUsRUFBRUcsUUFBUSxDQUFDLENBQUM7SUFDaEM7SUFDQSxPQUFPLEdBQUdBLFFBQVEsQ0FBQyxDQUFDO0FBQ3RCO0FBRUEsMEJBQTBCO0FBQzFCNUUsY0FBY3VFLE9BQU8sQ0FBQyxhQUFhQyxHQUFHLENBQUM7SUFDckMsTUFBTWpCLE1BQU0sSUFBSTVDO0lBQ2hCLE1BQU1rRSxVQUFVLElBQUksQ0FBQ1AsU0FBUztJQUM5QixNQUFNUSxXQUFXSixLQUFLSyxHQUFHLENBQUN4QixNQUFNc0I7SUFDaEMsT0FBT0gsS0FBS00sSUFBSSxDQUFDRixXQUFZLFFBQU8sS0FBSyxLQUFLLEVBQUM7QUFDakQ7QUFFQSxzQkFBc0I7QUFDdEI5RSxjQUFjaUYsR0FBRyxDQUFDLFFBQVEsU0FBU0MsSUFBSTtJQUNyQyxnRUFBZ0U7SUFDaEUsSUFBSSxJQUFJLENBQUN4RCxlQUFlLElBQUksSUFBSSxDQUFDQyxhQUFhLEVBQUU7UUFDOUMsTUFBTXdELFNBQVMsSUFBSSxDQUFDeEQsYUFBYSxHQUFHLElBQUksQ0FBQ0QsZUFBZTtRQUN4RCxJQUFJLENBQUN5QixjQUFjLEdBQUd1QixLQUFLVSxLQUFLLENBQUNELFNBQVUsUUFBTyxFQUFDLElBQUsscUJBQXFCO0lBQy9FO0lBRUFEO0FBQ0Y7QUFFQSxpQkFBaUI7QUFDakJsRixjQUFjcUYsT0FBTyxDQUFDQyxlQUFlLEdBQUcsZUFBZUMsZUFBZSxFQUFFQyxTQUFTLEVBQUVDLE9BQU87SUFDeEYsTUFBTUMsYUFBYTtRQUNqQmpGLGVBQWU4RTtRQUNmeEIsV0FBVztJQUNiO0lBRUEsSUFBSXlCLGFBQWFDLFNBQVM7UUFDeEJDLFdBQVdwQixTQUFTLEdBQUc7WUFDckJxQixNQUFNLElBQUloRixLQUFLNkU7WUFDZkksTUFBTSxJQUFJakYsS0FBSzhFO1FBQ2pCO0lBQ0Y7SUFFQSxPQUFPLE1BQU0sSUFBSSxDQUFDSSxTQUFTLENBQUM7UUFDMUI7WUFBRUMsUUFBUUo7UUFBVztRQUNyQjtZQUNFSyxRQUFRO2dCQUNOQyxLQUFLO2dCQUNMQyxPQUFPO29CQUFFQyxNQUFNO2dCQUFFO2dCQUNqQkMsY0FBYztvQkFDWkQsTUFBTTt3QkFDSkUsT0FBTzs0QkFDTDtnQ0FBRUMsS0FBSztvQ0FBQztvQ0FBVztpQ0FBWTs0QkFBQzs0QkFDaEM7NEJBQ0E7eUJBQ0Q7b0JBQ0g7Z0JBQ0Y7WUFDRjtRQUNGO0tBQ0Q7QUFDSDtBQUVBLG1CQUFtQjtBQUNuQnJHLGNBQWNzRyxPQUFPLENBQUNDLGNBQWMsR0FBRztJQUNyQyxPQUFPO1FBQUM7UUFBVztLQUFZLENBQUNDLFFBQVEsQ0FBQyxJQUFJLENBQUN2RixNQUFNO0FBQ3REO0FBRUFqQixjQUFjc0csT0FBTyxDQUFDRyxhQUFhLEdBQUc7SUFDcEMsT0FBTztRQUFDO0tBQVUsQ0FBQ0QsUUFBUSxDQUFDLElBQUksQ0FBQ3ZGLE1BQU07QUFDekM7QUFFQWpCLGNBQWNzRyxPQUFPLENBQUNJLGdCQUFnQixHQUFHLFNBQVNDLFVBQVUsRUFBRTdGLE9BQU8sRUFBRVgsT0FBTyxTQUFTO0lBQ3JGLElBQUksQ0FBQ2lELGNBQWMsQ0FBQ3dELElBQUksQ0FBQztRQUN2QnZELE1BQU1zRDtRQUNON0Y7UUFDQVg7UUFDQW1ELFdBQVcsSUFBSTNDO0lBQ2pCO0lBQ0EsT0FBTyxJQUFJLENBQUNrRyxJQUFJO0FBQ2xCO0FBRUEsTUFBTUMsVUFBVS9HLHdEQUFlLENBQUMrRyxPQUFPLElBQUkvRyxxREFBYyxDQUFDLFdBQVdDO0FBRXJFLGlFQUFlOEcsT0FBT0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxCaHVzaGFuIHBhdGlsXFxPbmVEcml2ZVxcRGVza3RvcFxcQm9vayBteSBTZXJ2aWNlIG5ld1xcbmV4dGpzLWFkbWluLWRhc2hib2FyZC1tYWluXFxzcmNcXG1vZGVsc1xcQm9va2luZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UgZnJvbSAnbW9uZ29vc2UnO1xuXG5jb25zdCBib29raW5nU2NoZW1hID0gbmV3IG1vbmdvb3NlLlNjaGVtYSh7XG4gIC8vIEN1c3RvbWVyIHdobyBtYWRlIHRoZSBib29raW5nXG4gIHVzZXI6IHtcbiAgICB0eXBlOiBtb25nb29zZS5TY2hlbWEuVHlwZXMuT2JqZWN0SWQsXG4gICAgcmVmOiAnVXNlcicsXG4gICAgcmVxdWlyZWQ6IHRydWVcbiAgfSxcbiAgXG4gIC8vIFNlcnZpY2UgYmVpbmcgYm9va2VkXG4gIHNlcnZpY2U6IHtcbiAgICB0eXBlOiBtb25nb29zZS5TY2hlbWEuVHlwZXMuT2JqZWN0SWQsXG4gICAgcmVmOiAnU2VydmljZScsXG4gICAgcmVxdWlyZWQ6IHRydWVcbiAgfSxcbiAgXG4gIC8vIEJ1c2luZXNzIG93bmVyIHByb3ZpZGluZyB0aGUgc2VydmljZVxuICBidXNpbmVzc093bmVyOiB7XG4gICAgdHlwZTogbW9uZ29vc2UuU2NoZW1hLlR5cGVzLk9iamVjdElkLFxuICAgIHJlZjogJ1VzZXInLFxuICAgIHJlcXVpcmVkOiB0cnVlXG4gIH0sXG4gIFxuICAvLyBCb29raW5nIGRldGFpbHNcbiAgcHJlZmVycmVkRGF0ZToge1xuICAgIHR5cGU6IERhdGUsXG4gICAgcmVxdWlyZWQ6IHRydWVcbiAgfSxcbiAgXG4gIHByZWZlcnJlZFRpbWU6IHtcbiAgICB0eXBlOiBTdHJpbmcsIC8vIEZvcm1hdDogXCJISDpNTVwiICgyNC1ob3VyIGZvcm1hdClcbiAgICByZXF1aXJlZDogZmFsc2VcbiAgfSxcbiAgXG4gIC8vIEN1c3RvbWVyIG1lc3NhZ2UvcmVxdWlyZW1lbnRzXG4gIG1lc3NhZ2U6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgdHJpbTogdHJ1ZSxcbiAgICBtYXhsZW5ndGg6IDEwMDBcbiAgfSxcbiAgXG4gIC8vIEJvb2tpbmcgc3RhdHVzXG4gIHN0YXR1czoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICBlbnVtOiBbJ3BlbmRpbmcnLCAnY29uZmlybWVkJywgJ3JlamVjdGVkJywgJ2luX3Byb2dyZXNzJywgJ2NvbXBsZXRlZCcsICdjYW5jZWxsZWQnXSxcbiAgICBkZWZhdWx0OiAncGVuZGluZydcbiAgfSxcbiAgXG4gIC8vIEZpbmFuY2lhbCBkZXRhaWxzXG4gIHRvdGFsQW1vdW50OiB7XG4gICAgdHlwZTogTnVtYmVyLFxuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIG1pbjogMFxuICB9LFxuICBcbiAgLy8gUGF5bWVudCBkZXRhaWxzXG4gIHBheW1lbnRTdGF0dXM6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgZW51bTogWydwZW5kaW5nJywgJ3BhaWQnLCAncmVmdW5kZWQnLCAnZmFpbGVkJ10sXG4gICAgZGVmYXVsdDogJ3BlbmRpbmcnXG4gIH0sXG4gIFxuICBwYXltZW50TWV0aG9kOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIGVudW06IFsnY2FzaCcsICdjYXJkJywgJ2JhbmtfdHJhbnNmZXInLCAncGF5cGFsJywgJ3N0cmlwZSddLFxuICAgIHJlcXVpcmVkOiBmYWxzZVxuICB9LFxuICBcbiAgcGF5bWVudFRyYW5zYWN0aW9uSWQ6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgcmVxdWlyZWQ6IGZhbHNlXG4gIH0sXG4gIFxuICAvLyBTZXJ2aWNlIGRlbGl2ZXJ5IGRldGFpbHNcbiAgYWN0dWFsU3RhcnRUaW1lOiB7XG4gICAgdHlwZTogRGF0ZSxcbiAgICByZXF1aXJlZDogZmFsc2VcbiAgfSxcbiAgXG4gIGFjdHVhbEVuZFRpbWU6IHtcbiAgICB0eXBlOiBEYXRlLFxuICAgIHJlcXVpcmVkOiBmYWxzZVxuICB9LFxuICBcbiAgLy8gQ3VzdG9tZXIgYWRkcmVzcyAoaWYgc2VydmljZSBpcyBhdCBjdXN0b21lciBsb2NhdGlvbilcbiAgY3VzdG9tZXJBZGRyZXNzOiB7XG4gICAgc3RyZWV0OiBTdHJpbmcsXG4gICAgY2l0eTogU3RyaW5nLFxuICAgIHN0YXRlOiBTdHJpbmcsXG4gICAgemlwQ29kZTogU3RyaW5nLFxuICAgIGNvdW50cnk6IFN0cmluZyxcbiAgICBjb29yZGluYXRlczoge1xuICAgICAgbGF0aXR1ZGU6IE51bWJlcixcbiAgICAgIGxvbmdpdHVkZTogTnVtYmVyXG4gICAgfVxuICB9LFxuICBcbiAgLy8gUmF0aW5nIGFuZCByZXZpZXcgKGFmdGVyIHNlcnZpY2UgY29tcGxldGlvbilcbiAgcmF0aW5nOiB7XG4gICAgdHlwZTogTnVtYmVyLFxuICAgIG1pbjogMSxcbiAgICBtYXg6IDUsXG4gICAgcmVxdWlyZWQ6IGZhbHNlXG4gIH0sXG4gIFxuICByZXZpZXc6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgdHJpbTogdHJ1ZSxcbiAgICBtYXhsZW5ndGg6IDEwMDAsXG4gICAgcmVxdWlyZWQ6IGZhbHNlXG4gIH0sXG4gIFxuICAvLyBCdXNpbmVzcyBvd25lciByZXNwb25zZSB0byByZXZpZXdcbiAgYnVzaW5lc3NPd25lclJlc3BvbnNlOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHRyaW06IHRydWUsXG4gICAgbWF4bGVuZ3RoOiA1MDAsXG4gICAgcmVxdWlyZWQ6IGZhbHNlXG4gIH0sXG4gIFxuICAvLyBUaW1lc3RhbXBzIGZvciBkaWZmZXJlbnQgc3RhdHVzIGNoYW5nZXNcbiAgY29uZmlybWVkQXQ6IHtcbiAgICB0eXBlOiBEYXRlLFxuICAgIHJlcXVpcmVkOiBmYWxzZVxuICB9LFxuICBcbiAgcmVqZWN0ZWRBdDoge1xuICAgIHR5cGU6IERhdGUsXG4gICAgcmVxdWlyZWQ6IGZhbHNlXG4gIH0sXG4gIFxuICBzdGFydGVkQXQ6IHtcbiAgICB0eXBlOiBEYXRlLFxuICAgIHJlcXVpcmVkOiBmYWxzZVxuICB9LFxuICBcbiAgY29tcGxldGVkQXQ6IHtcbiAgICB0eXBlOiBEYXRlLFxuICAgIHJlcXVpcmVkOiBmYWxzZVxuICB9LFxuICBcbiAgY2FuY2VsbGVkQXQ6IHtcbiAgICB0eXBlOiBEYXRlLFxuICAgIHJlcXVpcmVkOiBmYWxzZVxuICB9LFxuICBcbiAgY2FuY2VsbGVkQnk6IHtcbiAgICB0eXBlOiBtb25nb29zZS5TY2hlbWEuVHlwZXMuT2JqZWN0SWQsXG4gICAgcmVmOiAnVXNlcicsXG4gICAgcmVxdWlyZWQ6IGZhbHNlXG4gIH0sXG4gIFxuICAvLyBDYW5jZWxsYXRpb24gcmVhc29uXG4gIGNhbmNlbGxhdGlvblJlYXNvbjoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICB0cmltOiB0cnVlLFxuICAgIG1heGxlbmd0aDogNTAwLFxuICAgIHJlcXVpcmVkOiBmYWxzZVxuICB9LFxuICBcbiAgLy8gQWRkaXRpb25hbCBub3RlcyBmcm9tIGJ1c2luZXNzIG93bmVyXG4gIGJ1c2luZXNzT3duZXJOb3Rlczoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICB0cmltOiB0cnVlLFxuICAgIG1heGxlbmd0aDogMTAwMCxcbiAgICByZXF1aXJlZDogZmFsc2VcbiAgfSxcbiAgXG4gIC8vIFNwZWNpYWwgcmVxdWlyZW1lbnRzIG9yIGluc3RydWN0aW9uc1xuICBzcGVjaWFsUmVxdWlyZW1lbnRzOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHRyaW06IHRydWUsXG4gICAgbWF4bGVuZ3RoOiA1MDAsXG4gICAgcmVxdWlyZWQ6IGZhbHNlXG4gIH0sXG4gIFxuICAvLyBFc3RpbWF0ZWQgZHVyYXRpb24gKGluIG1pbnV0ZXMpXG4gIGVzdGltYXRlZER1cmF0aW9uOiB7XG4gICAgdHlwZTogTnVtYmVyLFxuICAgIG1pbjogMTUsXG4gICAgcmVxdWlyZWQ6IGZhbHNlXG4gIH0sXG4gIFxuICAvLyBBY3R1YWwgZHVyYXRpb24gKGluIG1pbnV0ZXMpXG4gIGFjdHVhbER1cmF0aW9uOiB7XG4gICAgdHlwZTogTnVtYmVyLFxuICAgIG1pbjogMCxcbiAgICByZXF1aXJlZDogZmFsc2VcbiAgfSxcbiAgXG4gIC8vIENvbW11bmljYXRpb24gaGlzdG9yeVxuICBjb21tdW5pY2F0aW9uczogW3tcbiAgICBmcm9tOiB7XG4gICAgICB0eXBlOiBtb25nb29zZS5TY2hlbWEuVHlwZXMuT2JqZWN0SWQsXG4gICAgICByZWY6ICdVc2VyJyxcbiAgICAgIHJlcXVpcmVkOiB0cnVlXG4gICAgfSxcbiAgICBtZXNzYWdlOiB7XG4gICAgICB0eXBlOiBTdHJpbmcsXG4gICAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICAgIHRyaW06IHRydWUsXG4gICAgICBtYXhsZW5ndGg6IDEwMDBcbiAgICB9LFxuICAgIHRpbWVzdGFtcDoge1xuICAgICAgdHlwZTogRGF0ZSxcbiAgICAgIGRlZmF1bHQ6IERhdGUubm93XG4gICAgfSxcbiAgICB0eXBlOiB7XG4gICAgICB0eXBlOiBTdHJpbmcsXG4gICAgICBlbnVtOiBbJ21lc3NhZ2UnLCAnc3RhdHVzX3VwZGF0ZScsICdzeXN0ZW0nXSxcbiAgICAgIGRlZmF1bHQ6ICdtZXNzYWdlJ1xuICAgIH1cbiAgfV0sXG4gIFxuICAvLyBBdHRhY2htZW50cyAocGhvdG9zLCBkb2N1bWVudHMsIGV0Yy4pXG4gIGF0dGFjaG1lbnRzOiBbe1xuICAgIGZpbGVuYW1lOiBTdHJpbmcsXG4gICAgdXJsOiBTdHJpbmcsXG4gICAgdHlwZToge1xuICAgICAgdHlwZTogU3RyaW5nLFxuICAgICAgZW51bTogWydpbWFnZScsICdkb2N1bWVudCcsICdvdGhlciddXG4gICAgfSxcbiAgICB1cGxvYWRlZEJ5OiB7XG4gICAgICB0eXBlOiBtb25nb29zZS5TY2hlbWEuVHlwZXMuT2JqZWN0SWQsXG4gICAgICByZWY6ICdVc2VyJ1xuICAgIH0sXG4gICAgdXBsb2FkZWRBdDoge1xuICAgICAgdHlwZTogRGF0ZSxcbiAgICAgIGRlZmF1bHQ6IERhdGUubm93XG4gICAgfVxuICB9XSxcbiAgXG4gIC8vIE1ldGFkYXRhXG4gIGlzQWN0aXZlOiB7XG4gICAgdHlwZTogQm9vbGVhbixcbiAgICBkZWZhdWx0OiB0cnVlXG4gIH0sXG4gIFxuICAvLyBGb3Igc29mdCBkZWxldGVcbiAgaXNEZWxldGVkOiB7XG4gICAgdHlwZTogQm9vbGVhbixcbiAgICBkZWZhdWx0OiBmYWxzZVxuICB9LFxuICBcbiAgLy8gVHJhY2sgaWYgc2VydmljZSB3YXMgZGVsZXRlZCBhZnRlciBib29raW5nXG4gIHNlcnZpY2VEZWxldGVkOiB7XG4gICAgdHlwZTogQm9vbGVhbixcbiAgICBkZWZhdWx0OiBmYWxzZVxuICB9XG59LCB7XG4gIHRpbWVzdGFtcHM6IHRydWUsIC8vIEFkZHMgY3JlYXRlZEF0IGFuZCB1cGRhdGVkQXRcbiAgdG9KU09OOiB7IHZpcnR1YWxzOiB0cnVlIH0sXG4gIHRvT2JqZWN0OiB7IHZpcnR1YWxzOiB0cnVlIH1cbn0pO1xuXG4vLyBJbmRleGVzIGZvciBiZXR0ZXIgcXVlcnkgcGVyZm9ybWFuY2VcbmJvb2tpbmdTY2hlbWEuaW5kZXgoeyB1c2VyOiAxLCBjcmVhdGVkQXQ6IC0xIH0pO1xuYm9va2luZ1NjaGVtYS5pbmRleCh7IGJ1c2luZXNzT3duZXI6IDEsIGNyZWF0ZWRBdDogLTEgfSk7XG5ib29raW5nU2NoZW1hLmluZGV4KHsgc2VydmljZTogMSwgY3JlYXRlZEF0OiAtMSB9KTtcbmJvb2tpbmdTY2hlbWEuaW5kZXgoeyBzdGF0dXM6IDEsIGNyZWF0ZWRBdDogLTEgfSk7XG5ib29raW5nU2NoZW1hLmluZGV4KHsgcHJlZmVycmVkRGF0ZTogMSB9KTtcbmJvb2tpbmdTY2hlbWEuaW5kZXgoeyBwYXltZW50U3RhdHVzOiAxIH0pO1xuXG4vLyBWaXJ0dWFsIGZvciBib29raW5nIGR1cmF0aW9uIGluIGEgcmVhZGFibGUgZm9ybWF0XG5ib29raW5nU2NoZW1hLnZpcnR1YWwoJ2R1cmF0aW9uRm9ybWF0dGVkJykuZ2V0KGZ1bmN0aW9uKCkge1xuICBpZiAoIXRoaXMuYWN0dWFsRHVyYXRpb24pIHJldHVybiBudWxsO1xuICBcbiAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKHRoaXMuYWN0dWFsRHVyYXRpb24gLyA2MCk7XG4gIGNvbnN0IG1pbnV0ZXMgPSB0aGlzLmFjdHVhbER1cmF0aW9uICUgNjA7XG4gIFxuICBpZiAoaG91cnMgPiAwKSB7XG4gICAgcmV0dXJuIGAke2hvdXJzfWggJHttaW51dGVzfW1gO1xuICB9XG4gIHJldHVybiBgJHttaW51dGVzfW1gO1xufSk7XG5cbi8vIFZpcnR1YWwgZm9yIGJvb2tpbmcgYWdlXG5ib29raW5nU2NoZW1hLnZpcnR1YWwoJ2FnZUluRGF5cycpLmdldChmdW5jdGlvbigpIHtcbiAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcbiAgY29uc3QgY3JlYXRlZCA9IHRoaXMuY3JlYXRlZEF0O1xuICBjb25zdCBkaWZmVGltZSA9IE1hdGguYWJzKG5vdyAtIGNyZWF0ZWQpO1xuICByZXR1cm4gTWF0aC5jZWlsKGRpZmZUaW1lIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKTtcbn0pO1xuXG4vLyBQcmUtc2F2ZSBtaWRkbGV3YXJlXG5ib29raW5nU2NoZW1hLnByZSgnc2F2ZScsIGZ1bmN0aW9uKG5leHQpIHtcbiAgLy8gQ2FsY3VsYXRlIGFjdHVhbCBkdXJhdGlvbiBpZiBib3RoIHN0YXJ0IGFuZCBlbmQgdGltZXMgYXJlIHNldFxuICBpZiAodGhpcy5hY3R1YWxTdGFydFRpbWUgJiYgdGhpcy5hY3R1YWxFbmRUaW1lKSB7XG4gICAgY29uc3QgZGlmZk1zID0gdGhpcy5hY3R1YWxFbmRUaW1lIC0gdGhpcy5hY3R1YWxTdGFydFRpbWU7XG4gICAgdGhpcy5hY3R1YWxEdXJhdGlvbiA9IE1hdGgucm91bmQoZGlmZk1zIC8gKDEwMDAgKiA2MCkpOyAvLyBDb252ZXJ0IHRvIG1pbnV0ZXNcbiAgfVxuICBcbiAgbmV4dCgpO1xufSk7XG5cbi8vIFN0YXRpYyBtZXRob2RzXG5ib29raW5nU2NoZW1hLnN0YXRpY3MuZ2V0Qm9va2luZ1N0YXRzID0gYXN5bmMgZnVuY3Rpb24oYnVzaW5lc3NPd25lcklkLCBzdGFydERhdGUsIGVuZERhdGUpIHtcbiAgY29uc3QgbWF0Y2hTdGFnZSA9IHtcbiAgICBidXNpbmVzc093bmVyOiBidXNpbmVzc093bmVySWQsXG4gICAgaXNEZWxldGVkOiBmYWxzZVxuICB9O1xuICBcbiAgaWYgKHN0YXJ0RGF0ZSAmJiBlbmREYXRlKSB7XG4gICAgbWF0Y2hTdGFnZS5jcmVhdGVkQXQgPSB7XG4gICAgICAkZ3RlOiBuZXcgRGF0ZShzdGFydERhdGUpLFxuICAgICAgJGx0ZTogbmV3IERhdGUoZW5kRGF0ZSlcbiAgICB9O1xuICB9XG4gIFxuICByZXR1cm4gYXdhaXQgdGhpcy5hZ2dyZWdhdGUoW1xuICAgIHsgJG1hdGNoOiBtYXRjaFN0YWdlIH0sXG4gICAge1xuICAgICAgJGdyb3VwOiB7XG4gICAgICAgIF9pZDogJyRzdGF0dXMnLFxuICAgICAgICBjb3VudDogeyAkc3VtOiAxIH0sXG4gICAgICAgIHRvdGFsUmV2ZW51ZToge1xuICAgICAgICAgICRzdW06IHtcbiAgICAgICAgICAgICRjb25kOiBbXG4gICAgICAgICAgICAgIHsgJGVxOiBbJyRzdGF0dXMnLCAnY29tcGxldGVkJ10gfSxcbiAgICAgICAgICAgICAgJyR0b3RhbEFtb3VudCcsXG4gICAgICAgICAgICAgIDBcbiAgICAgICAgICAgIF1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIF0pO1xufTtcblxuLy8gSW5zdGFuY2UgbWV0aG9kc1xuYm9va2luZ1NjaGVtYS5tZXRob2RzLmNhbkJlQ2FuY2VsbGVkID0gZnVuY3Rpb24oKSB7XG4gIHJldHVybiBbJ3BlbmRpbmcnLCAnY29uZmlybWVkJ10uaW5jbHVkZXModGhpcy5zdGF0dXMpO1xufTtcblxuYm9va2luZ1NjaGVtYS5tZXRob2RzLmNhbkJlTW9kaWZpZWQgPSBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIFsncGVuZGluZyddLmluY2x1ZGVzKHRoaXMuc3RhdHVzKTtcbn07XG5cbmJvb2tpbmdTY2hlbWEubWV0aG9kcy5hZGRDb21tdW5pY2F0aW9uID0gZnVuY3Rpb24oZnJvbVVzZXJJZCwgbWVzc2FnZSwgdHlwZSA9ICdtZXNzYWdlJykge1xuICB0aGlzLmNvbW11bmljYXRpb25zLnB1c2goe1xuICAgIGZyb206IGZyb21Vc2VySWQsXG4gICAgbWVzc2FnZSxcbiAgICB0eXBlLFxuICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKVxuICB9KTtcbiAgcmV0dXJuIHRoaXMuc2F2ZSgpO1xufTtcblxuY29uc3QgQm9va2luZyA9IG1vbmdvb3NlLm1vZGVscy5Cb29raW5nIHx8IG1vbmdvb3NlLm1vZGVsKCdCb29raW5nJywgYm9va2luZ1NjaGVtYSk7XG5cbmV4cG9ydCBkZWZhdWx0IEJvb2tpbmc7XG4iXSwibmFtZXMiOlsibW9uZ29vc2UiLCJib29raW5nU2NoZW1hIiwiU2NoZW1hIiwidXNlciIsInR5cGUiLCJUeXBlcyIsIk9iamVjdElkIiwicmVmIiwicmVxdWlyZWQiLCJzZXJ2aWNlIiwiYnVzaW5lc3NPd25lciIsInByZWZlcnJlZERhdGUiLCJEYXRlIiwicHJlZmVycmVkVGltZSIsIlN0cmluZyIsIm1lc3NhZ2UiLCJ0cmltIiwibWF4bGVuZ3RoIiwic3RhdHVzIiwiZW51bSIsImRlZmF1bHQiLCJ0b3RhbEFtb3VudCIsIk51bWJlciIsIm1pbiIsInBheW1lbnRTdGF0dXMiLCJwYXltZW50TWV0aG9kIiwicGF5bWVudFRyYW5zYWN0aW9uSWQiLCJhY3R1YWxTdGFydFRpbWUiLCJhY3R1YWxFbmRUaW1lIiwiY3VzdG9tZXJBZGRyZXNzIiwic3RyZWV0IiwiY2l0eSIsInN0YXRlIiwiemlwQ29kZSIsImNvdW50cnkiLCJjb29yZGluYXRlcyIsImxhdGl0dWRlIiwibG9uZ2l0dWRlIiwicmF0aW5nIiwibWF4IiwicmV2aWV3IiwiYnVzaW5lc3NPd25lclJlc3BvbnNlIiwiY29uZmlybWVkQXQiLCJyZWplY3RlZEF0Iiwic3RhcnRlZEF0IiwiY29tcGxldGVkQXQiLCJjYW5jZWxsZWRBdCIsImNhbmNlbGxlZEJ5IiwiY2FuY2VsbGF0aW9uUmVhc29uIiwiYnVzaW5lc3NPd25lck5vdGVzIiwic3BlY2lhbFJlcXVpcmVtZW50cyIsImVzdGltYXRlZER1cmF0aW9uIiwiYWN0dWFsRHVyYXRpb24iLCJjb21tdW5pY2F0aW9ucyIsImZyb20iLCJ0aW1lc3RhbXAiLCJub3ciLCJhdHRhY2htZW50cyIsImZpbGVuYW1lIiwidXJsIiwidXBsb2FkZWRCeSIsInVwbG9hZGVkQXQiLCJpc0FjdGl2ZSIsIkJvb2xlYW4iLCJpc0RlbGV0ZWQiLCJzZXJ2aWNlRGVsZXRlZCIsInRpbWVzdGFtcHMiLCJ0b0pTT04iLCJ2aXJ0dWFscyIsInRvT2JqZWN0IiwiaW5kZXgiLCJjcmVhdGVkQXQiLCJ2aXJ0dWFsIiwiZ2V0IiwiaG91cnMiLCJNYXRoIiwiZmxvb3IiLCJtaW51dGVzIiwiY3JlYXRlZCIsImRpZmZUaW1lIiwiYWJzIiwiY2VpbCIsInByZSIsIm5leHQiLCJkaWZmTXMiLCJyb3VuZCIsInN0YXRpY3MiLCJnZXRCb29raW5nU3RhdHMiLCJidXNpbmVzc093bmVySWQiLCJzdGFydERhdGUiLCJlbmREYXRlIiwibWF0Y2hTdGFnZSIsIiRndGUiLCIkbHRlIiwiYWdncmVnYXRlIiwiJG1hdGNoIiwiJGdyb3VwIiwiX2lkIiwiY291bnQiLCIkc3VtIiwidG90YWxSZXZlbnVlIiwiJGNvbmQiLCIkZXEiLCJtZXRob2RzIiwiY2FuQmVDYW5jZWxsZWQiLCJpbmNsdWRlcyIsImNhbkJlTW9kaWZpZWQiLCJhZGRDb21tdW5pY2F0aW9uIiwiZnJvbVVzZXJJZCIsInB1c2giLCJzYXZlIiwiQm9va2luZyIsIm1vZGVscyIsIm1vZGVsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Booking.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/nodemailer","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute&page=%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbusiness%2Fdashboard%2Frecent-bookings%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();