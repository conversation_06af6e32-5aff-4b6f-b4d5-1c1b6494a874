import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { generateToken, generateOTP } from '@/middleware/auth';
import { validateEmail } from '@/lib/utils';

export async function POST(request) {
  try {
    await connectDB();
    
    const { email } = await request.json();
    
    // Validate input
    if (!email) {
      return NextResponse.json({
        success: false,
        message: 'Email is required'
      }, { status: 400 });
    }
    
    if (!validateEmail(email)) {
      return NextResponse.json({
        success: false,
        message: 'Please provide a valid email address'
      }, { status: 400 });
    }
    
    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'No account found with this email address'
      }, { status: 404 });
    }
    
    if (!user.isActive) {
      return NextResponse.json({
        success: false,
        message: 'Your account has been deactivated. Please contact support.'
      }, { status: 403 });
    }
    
    // Generate OTP
    const otp = generateOTP();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    
    // Save OTP to user
    user.otp = otp;
    user.otpExpires = otpExpires;
    await user.save();
    
    // TODO: Send OTP via email (implement email service)
    console.log(`OTP for ${email}: ${otp}`); // For development
    
    return NextResponse.json({
      success: true,
      message: 'OTP sent to your email address',
      data: {
        email: user.email,
        // In development, return OTP for testing
        ...(process.env.NODE_ENV === 'development' && { otp })
      }
    });
    
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json({
      success: false,
      message: 'An error occurred during login'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    success: false,
    message: 'Method not allowed'
  }, { status: 405 });
}
