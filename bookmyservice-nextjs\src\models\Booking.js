import mongoose from 'mongoose';

const BookingSchema = new mongoose.Schema({
  service: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service',
    required: [true, 'Service is required']
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required']
  },
  businessOwner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Business owner is required']
  },
  // Booking details
  scheduledDate: {
    type: Date,
    required: [true, 'Scheduled date is required']
  },
  scheduledTime: {
    type: String,
    required: [true, 'Scheduled time is required']
  },
  duration: {
    type: Number, // in minutes
    required: [true, 'Duration is required']
  },
  // Status tracking
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'cancelled_by_provider'],
    default: 'pending'
  },
  // Payment information
  amount: {
    type: Number,
    required: [true, 'Amount is required'],
    min: [0, 'Amount cannot be negative']
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'refunded', 'failed'],
    default: 'pending'
  },
  paymentIntentId: String, // Stripe payment intent ID
  refundId: String, // Stripe refund ID
  // Customer details
  customerInfo: {
    name: String,
    email: String,
    phone: String,
    address: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String
    }
  },
  // Service location
  serviceLocation: {
    type: {
      type: String,
      enum: ['customer_location', 'business_location', 'online'],
      default: 'customer_location'
    },
    address: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    instructions: String
  },
  // Additional details
  specialRequests: String,
  notes: String,
  // Completion details
  completionOTP: String,
  completedAt: Date,
  // Cancellation details
  cancellationReason: String,
  cancelledAt: Date,
  cancelledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  // Communication
  messages: [{
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    message: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    isRead: {
      type: Boolean,
      default: false
    }
  }],
  // Attachments
  attachments: [{
    name: String,
    url: String,
    type: String, // image, document, etc.
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  // Review and rating
  review: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    reviewedAt: Date
  },
  // Tracking
  statusHistory: [{
    status: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String
  }],
  // Notifications
  notifications: {
    userNotified: {
      type: Boolean,
      default: false
    },
    businessOwnerNotified: {
      type: Boolean,
      default: false
    },
    reminderSent: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true
});

// Indexes
BookingSchema.index({ user: 1 });
BookingSchema.index({ businessOwner: 1 });
BookingSchema.index({ service: 1 });
BookingSchema.index({ status: 1 });
BookingSchema.index({ scheduledDate: 1 });
BookingSchema.index({ paymentStatus: 1 });
BookingSchema.index({ createdAt: -1 });

// Virtual for formatted amount
BookingSchema.virtual('formattedAmount').get(function() {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  });
  return formatter.format(this.amount);
});

// Virtual for booking duration in hours
BookingSchema.virtual('durationHours').get(function() {
  return Math.round((this.duration / 60) * 100) / 100;
});

// Virtual for full service address
BookingSchema.virtual('fullServiceAddress').get(function() {
  if (!this.serviceLocation.address) return '';
  const { street, city, state, zipCode, country } = this.serviceLocation.address;
  return [street, city, state, zipCode, country].filter(Boolean).join(', ');
});

// Ensure virtual fields are serialized
BookingSchema.set('toJSON', { virtuals: true });
BookingSchema.set('toObject', { virtuals: true });

// Pre-save middleware to track status changes
BookingSchema.pre('save', function(next) {
  if (this.isModified('status')) {
    this.statusHistory.push({
      status: this.status,
      timestamp: new Date(),
      updatedBy: this._updatedBy || null,
      notes: this._statusNotes || null
    });
  }
  next();
});

// Instance methods
BookingSchema.methods.updateStatus = function(newStatus, updatedBy, notes) {
  this._updatedBy = updatedBy;
  this._statusNotes = notes;
  this.status = newStatus;
  
  if (newStatus === 'completed') {
    this.completedAt = new Date();
  } else if (newStatus === 'cancelled' || newStatus === 'cancelled_by_provider') {
    this.cancelledAt = new Date();
    this.cancelledBy = updatedBy;
  }
  
  return this.save();
};

BookingSchema.methods.addMessage = function(senderId, message) {
  this.messages.push({
    sender: senderId,
    message,
    timestamp: new Date()
  });
  return this.save();
};

BookingSchema.methods.addReview = function(rating, comment) {
  this.review = {
    rating,
    comment,
    reviewedAt: new Date()
  };
  return this.save();
};

BookingSchema.methods.generateCompletionOTP = function() {
  this.completionOTP = Math.floor(100000 + Math.random() * 900000).toString();
  return this.save();
};

BookingSchema.methods.verifyCompletionOTP = function(otp) {
  return this.completionOTP === otp;
};

BookingSchema.methods.canBeCancelled = function() {
  const now = new Date();
  const scheduledDateTime = new Date(`${this.scheduledDate.toDateString()} ${this.scheduledTime}`);
  const hoursUntilService = (scheduledDateTime - now) / (1000 * 60 * 60);
  
  // Can cancel if more than 24 hours before scheduled time
  return hoursUntilService > 24 && ['pending', 'confirmed'].includes(this.status);
};

BookingSchema.methods.isUpcoming = function() {
  const now = new Date();
  const scheduledDateTime = new Date(`${this.scheduledDate.toDateString()} ${this.scheduledTime}`);
  return scheduledDateTime > now && ['confirmed', 'in_progress'].includes(this.status);
};

export default mongoose.models.Booking || mongoose.model('Booking', BookingSchema);
