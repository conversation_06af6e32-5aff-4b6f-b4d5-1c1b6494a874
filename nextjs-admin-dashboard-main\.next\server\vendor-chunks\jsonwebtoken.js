/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsonwebtoken";
exports.ids = ["vendor-chunks/jsonwebtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/jsonwebtoken/decode.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/decode.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\n\nmodule.exports = function (jwt, options) {\n  options = options || {};\n  var decoded = jws.decode(jwt, options);\n  if (!decoded) { return null; }\n  var payload = decoded.payload;\n\n  //try parse the payload\n  if(typeof payload === 'string') {\n    try {\n      var obj = JSON.parse(payload);\n      if(obj !== null && typeof obj === 'object') {\n        payload = obj;\n      }\n    } catch (e) { }\n  }\n\n  //return header if `complete` option is enabled.  header includes claims\n  //such as `kid` and `alg` used to select the key within a JWKS needed to\n  //verify the signature\n  if (options.complete === true) {\n    return {\n      header: decoded.header,\n      payload: payload,\n      signature: decoded.signature\n    };\n  }\n  return payload;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2RlY29kZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxVQUFVLG1CQUFPLENBQUMsOENBQUs7O0FBRXZCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmh1c2hhbiBwYXRpbFxcT25lRHJpdmVcXERlc2t0b3BcXEJvb2sgbXkgU2VydmljZSBuZXdcXG5leHRqcy1hZG1pbi1kYXNoYm9hcmQtbWFpblxcbm9kZV9tb2R1bGVzXFxqc29ud2VidG9rZW5cXGRlY29kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgandzID0gcmVxdWlyZSgnandzJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKGp3dCwgb3B0aW9ucykge1xuICBvcHRpb25zID0gb3B0aW9ucyB8fCB7fTtcbiAgdmFyIGRlY29kZWQgPSBqd3MuZGVjb2RlKGp3dCwgb3B0aW9ucyk7XG4gIGlmICghZGVjb2RlZCkgeyByZXR1cm4gbnVsbDsgfVxuICB2YXIgcGF5bG9hZCA9IGRlY29kZWQucGF5bG9hZDtcblxuICAvL3RyeSBwYXJzZSB0aGUgcGF5bG9hZFxuICBpZih0eXBlb2YgcGF5bG9hZCA9PT0gJ3N0cmluZycpIHtcbiAgICB0cnkge1xuICAgICAgdmFyIG9iaiA9IEpTT04ucGFyc2UocGF5bG9hZCk7XG4gICAgICBpZihvYmogIT09IG51bGwgJiYgdHlwZW9mIG9iaiA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgcGF5bG9hZCA9IG9iajtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlKSB7IH1cbiAgfVxuXG4gIC8vcmV0dXJuIGhlYWRlciBpZiBgY29tcGxldGVgIG9wdGlvbiBpcyBlbmFibGVkLiAgaGVhZGVyIGluY2x1ZGVzIGNsYWltc1xuICAvL3N1Y2ggYXMgYGtpZGAgYW5kIGBhbGdgIHVzZWQgdG8gc2VsZWN0IHRoZSBrZXkgd2l0aGluIGEgSldLUyBuZWVkZWQgdG9cbiAgLy92ZXJpZnkgdGhlIHNpZ25hdHVyZVxuICBpZiAob3B0aW9ucy5jb21wbGV0ZSA9PT0gdHJ1ZSkge1xuICAgIHJldHVybiB7XG4gICAgICBoZWFkZXI6IGRlY29kZWQuaGVhZGVyLFxuICAgICAgcGF5bG9hZDogcGF5bG9hZCxcbiAgICAgIHNpZ25hdHVyZTogZGVjb2RlZC5zaWduYXR1cmVcbiAgICB9O1xuICB9XG4gIHJldHVybiBwYXlsb2FkO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/decode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/index.js":
/*!********************************************!*\
  !*** ./node_modules/jsonwebtoken/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = {\n  decode: __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\"),\n  verify: __webpack_require__(/*! ./verify */ \"(rsc)/./node_modules/jsonwebtoken/verify.js\"),\n  sign: __webpack_require__(/*! ./sign */ \"(rsc)/./node_modules/jsonwebtoken/sign.js\"),\n  JsonWebTokenError: __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\"),\n  NotBeforeError: __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\"),\n  TokenExpiredError: __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\"),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EsVUFBVSxtQkFBTyxDQUFDLDZEQUFVO0FBQzVCLFVBQVUsbUJBQU8sQ0FBQyw2REFBVTtBQUM1QixRQUFRLG1CQUFPLENBQUMseURBQVE7QUFDeEIscUJBQXFCLG1CQUFPLENBQUMsMkZBQXlCO0FBQ3RELGtCQUFrQixtQkFBTyxDQUFDLHFGQUFzQjtBQUNoRCxxQkFBcUIsbUJBQU8sQ0FBQywyRkFBeUI7QUFDdEQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmh1c2hhbiBwYXRpbFxcT25lRHJpdmVcXERlc2t0b3BcXEJvb2sgbXkgU2VydmljZSBuZXdcXG5leHRqcy1hZG1pbi1kYXNoYm9hcmQtbWFpblxcbm9kZV9tb2R1bGVzXFxqc29ud2VidG9rZW5cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0ge1xuICBkZWNvZGU6IHJlcXVpcmUoJy4vZGVjb2RlJyksXG4gIHZlcmlmeTogcmVxdWlyZSgnLi92ZXJpZnknKSxcbiAgc2lnbjogcmVxdWlyZSgnLi9zaWduJyksXG4gIEpzb25XZWJUb2tlbkVycm9yOiByZXF1aXJlKCcuL2xpYi9Kc29uV2ViVG9rZW5FcnJvcicpLFxuICBOb3RCZWZvcmVFcnJvcjogcmVxdWlyZSgnLi9saWIvTm90QmVmb3JlRXJyb3InKSxcbiAgVG9rZW5FeHBpcmVkRXJyb3I6IHJlcXVpcmUoJy4vbGliL1Rva2VuRXhwaXJlZEVycm9yJyksXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/JsonWebTokenError.js ***!
  \************************************************************/
/***/ ((module) => {

eval("var JsonWebTokenError = function (message, error) {\n  Error.call(this, message);\n  if(Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  }\n  this.name = 'JsonWebTokenError';\n  this.message = message;\n  if (error) this.inner = error;\n};\n\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\n\nmodule.exports = JsonWebTokenError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Kc29uV2ViVG9rZW5FcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxCaHVzaGFuIHBhdGlsXFxPbmVEcml2ZVxcRGVza3RvcFxcQm9vayBteSBTZXJ2aWNlIG5ld1xcbmV4dGpzLWFkbWluLWRhc2hib2FyZC1tYWluXFxub2RlX21vZHVsZXNcXGpzb253ZWJ0b2tlblxcbGliXFxKc29uV2ViVG9rZW5FcnJvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgSnNvbldlYlRva2VuRXJyb3IgPSBmdW5jdGlvbiAobWVzc2FnZSwgZXJyb3IpIHtcbiAgRXJyb3IuY2FsbCh0aGlzLCBtZXNzYWdlKTtcbiAgaWYoRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UpIHtcbiAgICBFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSh0aGlzLCB0aGlzLmNvbnN0cnVjdG9yKTtcbiAgfVxuICB0aGlzLm5hbWUgPSAnSnNvbldlYlRva2VuRXJyb3InO1xuICB0aGlzLm1lc3NhZ2UgPSBtZXNzYWdlO1xuICBpZiAoZXJyb3IpIHRoaXMuaW5uZXIgPSBlcnJvcjtcbn07XG5cbkpzb25XZWJUb2tlbkVycm9yLnByb3RvdHlwZSA9IE9iamVjdC5jcmVhdGUoRXJyb3IucHJvdG90eXBlKTtcbkpzb25XZWJUb2tlbkVycm9yLnByb3RvdHlwZS5jb25zdHJ1Y3RvciA9IEpzb25XZWJUb2tlbkVycm9yO1xuXG5tb2R1bGUuZXhwb3J0cyA9IEpzb25XZWJUb2tlbkVycm9yO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js":
/*!*********************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/NotBeforeError.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\n\nvar NotBeforeError = function (message, date) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'NotBeforeError';\n  this.date = date;\n};\n\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\n\nNotBeforeError.prototype.constructor = NotBeforeError;\n\nmodule.exports = NotBeforeError;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSx3QkFBd0IsbUJBQU8sQ0FBQyx1RkFBcUI7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmh1c2hhbiBwYXRpbFxcT25lRHJpdmVcXERlc2t0b3BcXEJvb2sgbXkgU2VydmljZSBuZXdcXG5leHRqcy1hZG1pbi1kYXNoYm9hcmQtbWFpblxcbm9kZV9tb2R1bGVzXFxqc29ud2VidG9rZW5cXGxpYlxcTm90QmVmb3JlRXJyb3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gcmVxdWlyZSgnLi9Kc29uV2ViVG9rZW5FcnJvcicpO1xuXG52YXIgTm90QmVmb3JlRXJyb3IgPSBmdW5jdGlvbiAobWVzc2FnZSwgZGF0ZSkge1xuICBKc29uV2ViVG9rZW5FcnJvci5jYWxsKHRoaXMsIG1lc3NhZ2UpO1xuICB0aGlzLm5hbWUgPSAnTm90QmVmb3JlRXJyb3InO1xuICB0aGlzLmRhdGUgPSBkYXRlO1xufTtcblxuTm90QmVmb3JlRXJyb3IucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShKc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUpO1xuXG5Ob3RCZWZvcmVFcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBOb3RCZWZvcmVFcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBOb3RCZWZvcmVFcnJvcjsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/TokenExpiredError.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\n\nvar TokenExpiredError = function (message, expiredAt) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'TokenExpiredError';\n  this.expiredAt = expiredAt;\n};\n\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\n\nTokenExpiredError.prototype.constructor = TokenExpiredError;\n\nmodule.exports = TokenExpiredError;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSx3QkFBd0IsbUJBQU8sQ0FBQyx1RkFBcUI7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmh1c2hhbiBwYXRpbFxcT25lRHJpdmVcXERlc2t0b3BcXEJvb2sgbXkgU2VydmljZSBuZXdcXG5leHRqcy1hZG1pbi1kYXNoYm9hcmQtbWFpblxcbm9kZV9tb2R1bGVzXFxqc29ud2VidG9rZW5cXGxpYlxcVG9rZW5FeHBpcmVkRXJyb3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gcmVxdWlyZSgnLi9Kc29uV2ViVG9rZW5FcnJvcicpO1xuXG52YXIgVG9rZW5FeHBpcmVkRXJyb3IgPSBmdW5jdGlvbiAobWVzc2FnZSwgZXhwaXJlZEF0KSB7XG4gIEpzb25XZWJUb2tlbkVycm9yLmNhbGwodGhpcywgbWVzc2FnZSk7XG4gIHRoaXMubmFtZSA9ICdUb2tlbkV4cGlyZWRFcnJvcic7XG4gIHRoaXMuZXhwaXJlZEF0ID0gZXhwaXJlZEF0O1xufTtcblxuVG9rZW5FeHBpcmVkRXJyb3IucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShKc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUpO1xuXG5Ub2tlbkV4cGlyZWRFcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBUb2tlbkV4cGlyZWRFcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBUb2tlbkV4cGlyZWRFcnJvcjsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js":
/*!************************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\n\nmodule.exports = semver.satisfies(process.version, '>=15.7.0');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9hc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxlQUFlLG1CQUFPLENBQUMsb0RBQVE7O0FBRS9CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEJodXNoYW4gcGF0aWxcXE9uZURyaXZlXFxEZXNrdG9wXFxCb29rIG15IFNlcnZpY2UgbmV3XFxuZXh0anMtYWRtaW4tZGFzaGJvYXJkLW1haW5cXG5vZGVfbW9kdWxlc1xcanNvbndlYnRva2VuXFxsaWJcXGFzeW1tZXRyaWNLZXlEZXRhaWxzU3VwcG9ydGVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHNlbXZlciA9IHJlcXVpcmUoJ3NlbXZlcicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNlbXZlci5zYXRpc2ZpZXMocHJvY2Vzcy52ZXJzaW9uLCAnPj0xNS43LjAnKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js":
/*!******************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/psSupported.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\n\nmodule.exports = semver.satisfies(process.version, '^6.12.0 || >=8.0.0');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9wc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxhQUFhLG1CQUFPLENBQUMsb0RBQVE7O0FBRTdCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEJodXNoYW4gcGF0aWxcXE9uZURyaXZlXFxEZXNrdG9wXFxCb29rIG15IFNlcnZpY2UgbmV3XFxuZXh0anMtYWRtaW4tZGFzaGJvYXJkLW1haW5cXG5vZGVfbW9kdWxlc1xcanNvbndlYnRva2VuXFxsaWJcXHBzU3VwcG9ydGVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBzZW12ZXIgPSByZXF1aXJlKCdzZW12ZXInKTtcblxubW9kdWxlLmV4cG9ydHMgPSBzZW12ZXIuc2F0aXNmaWVzKHByb2Nlc3MudmVyc2lvbiwgJ142LjEyLjAgfHwgPj04LjAuMCcpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js":
/*!********************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\n\nmodule.exports = semver.satisfies(process.version, '>=16.9.0');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsbUJBQU8sQ0FBQyxvREFBUTs7QUFFL0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmh1c2hhbiBwYXRpbFxcT25lRHJpdmVcXERlc2t0b3BcXEJvb2sgbXkgU2VydmljZSBuZXdcXG5leHRqcy1hZG1pbi1kYXNoYm9hcmQtbWFpblxcbm9kZV9tb2R1bGVzXFxqc29ud2VidG9rZW5cXGxpYlxccnNhUHNzS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzZW12ZXIgPSByZXF1aXJlKCdzZW12ZXInKTtcblxubW9kdWxlLmV4cG9ydHMgPSBzZW12ZXIuc2F0aXNmaWVzKHByb2Nlc3MudmVyc2lvbiwgJz49MTYuOS4wJyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/timespan.js":
/*!***************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/timespan.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var ms = __webpack_require__(/*! ms */ \"(rsc)/./node_modules/ms/index.js\");\n\nmodule.exports = function (time, iat) {\n  var timestamp = iat || Math.floor(Date.now() / 1000);\n\n  if (typeof time === 'string') {\n    var milliseconds = ms(time);\n    if (typeof milliseconds === 'undefined') {\n      return;\n    }\n    return Math.floor(timestamp + milliseconds / 1000);\n  } else if (typeof time === 'number') {\n    return timestamp + time;\n  } else {\n    return;\n  }\n\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxTQUFTLG1CQUFPLENBQUMsNENBQUk7O0FBRXJCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmh1c2hhbiBwYXRpbFxcT25lRHJpdmVcXERlc2t0b3BcXEJvb2sgbXkgU2VydmljZSBuZXdcXG5leHRqcy1hZG1pbi1kYXNoYm9hcmQtbWFpblxcbm9kZV9tb2R1bGVzXFxqc29ud2VidG9rZW5cXGxpYlxcdGltZXNwYW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIG1zID0gcmVxdWlyZSgnbXMnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAodGltZSwgaWF0KSB7XG4gIHZhciB0aW1lc3RhbXAgPSBpYXQgfHwgTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCk7XG5cbiAgaWYgKHR5cGVvZiB0aW1lID09PSAnc3RyaW5nJykge1xuICAgIHZhciBtaWxsaXNlY29uZHMgPSBtcyh0aW1lKTtcbiAgICBpZiAodHlwZW9mIG1pbGxpc2Vjb25kcyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgcmV0dXJuIE1hdGguZmxvb3IodGltZXN0YW1wICsgbWlsbGlzZWNvbmRzIC8gMTAwMCk7XG4gIH0gZWxzZSBpZiAodHlwZW9mIHRpbWUgPT09ICdudW1iZXInKSB7XG4gICAgcmV0dXJuIHRpbWVzdGFtcCArIHRpbWU7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuO1xuICB9XG5cbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js":
/*!****************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ASYMMETRIC_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./asymmetricKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\");\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./rsaPssKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\");\n\nconst allowedAlgorithmsForKeys = {\n  'ec': ['ES256', 'ES384', 'ES512'],\n  'rsa': ['RS256', 'PS256', 'RS384', 'PS384', 'RS512', 'PS512'],\n  'rsa-pss': ['PS256', 'PS384', 'PS512']\n};\n\nconst allowedCurves = {\n  ES256: 'prime256v1',\n  ES384: 'secp384r1',\n  ES512: 'secp521r1',\n};\n\nmodule.exports = function(algorithm, key) {\n  if (!algorithm || !key) return;\n\n  const keyType = key.asymmetricKeyType;\n  if (!keyType) return;\n\n  const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n\n  if (!allowedAlgorithms) {\n    throw new Error(`Unknown key type \"${keyType}\".`);\n  }\n\n  if (!allowedAlgorithms.includes(algorithm)) {\n    throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(', ')}.`)\n  }\n\n  /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */\n  /* istanbul ignore next */\n  if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n    switch (keyType) {\n    case 'ec':\n      const keyCurve = key.asymmetricKeyDetails.namedCurve;\n      const allowedCurve = allowedCurves[algorithm];\n\n      if (keyCurve !== allowedCurve) {\n        throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n      }\n      break;\n\n    case 'rsa-pss':\n      if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n        const length = parseInt(algorithm.slice(-3), 10);\n        const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n\n        if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n          throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n        }\n\n        if (saltLength !== undefined && saltLength > length >> 3) {\n          throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`)\n        }\n      }\n      break;\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/sign.js":
/*!*******************************************!*\
  !*** ./node_modules/jsonwebtoken/sign.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst includes = __webpack_require__(/*! lodash.includes */ \"(rsc)/./node_modules/lodash.includes/index.js\");\nconst isBoolean = __webpack_require__(/*! lodash.isboolean */ \"(rsc)/./node_modules/lodash.isboolean/index.js\");\nconst isInteger = __webpack_require__(/*! lodash.isinteger */ \"(rsc)/./node_modules/lodash.isinteger/index.js\");\nconst isNumber = __webpack_require__(/*! lodash.isnumber */ \"(rsc)/./node_modules/lodash.isnumber/index.js\");\nconst isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(rsc)/./node_modules/lodash.isplainobject/index.js\");\nconst isString = __webpack_require__(/*! lodash.isstring */ \"(rsc)/./node_modules/lodash.isstring/index.js\");\nconst once = __webpack_require__(/*! lodash.once */ \"(rsc)/./node_modules/lodash.once/index.js\");\nconst { KeyObject, createSecretKey, createPrivateKey } = __webpack_require__(/*! crypto */ \"crypto\")\n\nconst SUPPORTED_ALGS = ['RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512', 'HS256', 'HS384', 'HS512', 'none'];\nif (PS_SUPPORTED) {\n  SUPPORTED_ALGS.splice(3, 0, 'PS256', 'PS384', 'PS512');\n}\n\nconst sign_options_schema = {\n  expiresIn: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"expiresIn\" should be a number of seconds or string representing a timespan' },\n  notBefore: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"notBefore\" should be a number of seconds or string representing a timespan' },\n  audience: { isValid: function(value) { return isString(value) || Array.isArray(value); }, message: '\"audience\" must be a string or array' },\n  algorithm: { isValid: includes.bind(null, SUPPORTED_ALGS), message: '\"algorithm\" must be a valid string enum value' },\n  header: { isValid: isPlainObject, message: '\"header\" must be an object' },\n  encoding: { isValid: isString, message: '\"encoding\" must be a string' },\n  issuer: { isValid: isString, message: '\"issuer\" must be a string' },\n  subject: { isValid: isString, message: '\"subject\" must be a string' },\n  jwtid: { isValid: isString, message: '\"jwtid\" must be a string' },\n  noTimestamp: { isValid: isBoolean, message: '\"noTimestamp\" must be a boolean' },\n  keyid: { isValid: isString, message: '\"keyid\" must be a string' },\n  mutatePayload: { isValid: isBoolean, message: '\"mutatePayload\" must be a boolean' },\n  allowInsecureKeySizes: { isValid: isBoolean, message: '\"allowInsecureKeySizes\" must be a boolean'},\n  allowInvalidAsymmetricKeyTypes: { isValid: isBoolean, message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'}\n};\n\nconst registered_claims_schema = {\n  iat: { isValid: isNumber, message: '\"iat\" should be a number of seconds' },\n  exp: { isValid: isNumber, message: '\"exp\" should be a number of seconds' },\n  nbf: { isValid: isNumber, message: '\"nbf\" should be a number of seconds' }\n};\n\nfunction validate(schema, allowUnknown, object, parameterName) {\n  if (!isPlainObject(object)) {\n    throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n  }\n  Object.keys(object)\n    .forEach(function(key) {\n      const validator = schema[key];\n      if (!validator) {\n        if (!allowUnknown) {\n          throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n        }\n        return;\n      }\n      if (!validator.isValid(object[key])) {\n        throw new Error(validator.message);\n      }\n    });\n}\n\nfunction validateOptions(options) {\n  return validate(sign_options_schema, false, options, 'options');\n}\n\nfunction validatePayload(payload) {\n  return validate(registered_claims_schema, true, payload, 'payload');\n}\n\nconst options_to_payload = {\n  'audience': 'aud',\n  'issuer': 'iss',\n  'subject': 'sub',\n  'jwtid': 'jti'\n};\n\nconst options_for_objects = [\n  'expiresIn',\n  'notBefore',\n  'noTimestamp',\n  'audience',\n  'issuer',\n  'subject',\n  'jwtid',\n];\n\nmodule.exports = function (payload, secretOrPrivateKey, options, callback) {\n  if (typeof options === 'function') {\n    callback = options;\n    options = {};\n  } else {\n    options = options || {};\n  }\n\n  const isObjectPayload = typeof payload === 'object' &&\n                        !Buffer.isBuffer(payload);\n\n  const header = Object.assign({\n    alg: options.algorithm || 'HS256',\n    typ: isObjectPayload ? 'JWT' : undefined,\n    kid: options.keyid\n  }, options.header);\n\n  function failure(err) {\n    if (callback) {\n      return callback(err);\n    }\n    throw err;\n  }\n\n  if (!secretOrPrivateKey && options.algorithm !== 'none') {\n    return failure(new Error('secretOrPrivateKey must have a value'));\n  }\n\n  if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n    try {\n      secretOrPrivateKey = createPrivateKey(secretOrPrivateKey)\n    } catch (_) {\n      try {\n        secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === 'string' ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey)\n      } catch (_) {\n        return failure(new Error('secretOrPrivateKey is not valid key material'));\n      }\n    }\n  }\n\n  if (header.alg.startsWith('HS') && secretOrPrivateKey.type !== 'secret') {\n    return failure(new Error((`secretOrPrivateKey must be a symmetric key when using ${header.alg}`)))\n  } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n    if (secretOrPrivateKey.type !== 'private') {\n      return failure(new Error((`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`)))\n    }\n    if (!options.allowInsecureKeySizes &&\n      !header.alg.startsWith('ES') &&\n      secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n      secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n      return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n    }\n  }\n\n  if (typeof payload === 'undefined') {\n    return failure(new Error('payload is required'));\n  } else if (isObjectPayload) {\n    try {\n      validatePayload(payload);\n    }\n    catch (error) {\n      return failure(error);\n    }\n    if (!options.mutatePayload) {\n      payload = Object.assign({},payload);\n    }\n  } else {\n    const invalid_options = options_for_objects.filter(function (opt) {\n      return typeof options[opt] !== 'undefined';\n    });\n\n    if (invalid_options.length > 0) {\n      return failure(new Error('invalid ' + invalid_options.join(',') + ' option for ' + (typeof payload ) + ' payload'));\n    }\n  }\n\n  if (typeof payload.exp !== 'undefined' && typeof options.expiresIn !== 'undefined') {\n    return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n  }\n\n  if (typeof payload.nbf !== 'undefined' && typeof options.notBefore !== 'undefined') {\n    return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n  }\n\n  try {\n    validateOptions(options);\n  }\n  catch (error) {\n    return failure(error);\n  }\n\n  if (!options.allowInvalidAsymmetricKeyTypes) {\n    try {\n      validateAsymmetricKey(header.alg, secretOrPrivateKey);\n    } catch (error) {\n      return failure(error);\n    }\n  }\n\n  const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n\n  if (options.noTimestamp) {\n    delete payload.iat;\n  } else if (isObjectPayload) {\n    payload.iat = timestamp;\n  }\n\n  if (typeof options.notBefore !== 'undefined') {\n    try {\n      payload.nbf = timespan(options.notBefore, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.nbf === 'undefined') {\n      return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  if (typeof options.expiresIn !== 'undefined' && typeof payload === 'object') {\n    try {\n      payload.exp = timespan(options.expiresIn, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.exp === 'undefined') {\n      return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  Object.keys(options_to_payload).forEach(function (key) {\n    const claim = options_to_payload[key];\n    if (typeof options[key] !== 'undefined') {\n      if (typeof payload[claim] !== 'undefined') {\n        return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n      }\n      payload[claim] = options[key];\n    }\n  });\n\n  const encoding = options.encoding || 'utf8';\n\n  if (typeof callback === 'function') {\n    callback = callback && once(callback);\n\n    jws.createSign({\n      header: header,\n      privateKey: secretOrPrivateKey,\n      payload: payload,\n      encoding: encoding\n    }).once('error', callback)\n      .once('done', function (signature) {\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if(!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n          return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`))\n        }\n        callback(null, signature);\n      });\n  } else {\n    let signature = jws.sign({header: header, payload: payload, secret: secretOrPrivateKey, encoding: encoding});\n    // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n    if(!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n      throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`)\n    }\n    return signature\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/sign.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/verify.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/verify.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const JsonWebTokenError = __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nconst NotBeforeError = __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\");\nconst TokenExpiredError = __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\");\nconst decode = __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\");\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst {KeyObject, createSecretKey, createPublicKey} = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst PUB_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nconst EC_KEY_ALGS = ['ES256', 'ES384', 'ES512'];\nconst RSA_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nconst HS_ALGS = ['HS256', 'HS384', 'HS512'];\n\nif (PS_SUPPORTED) {\n  PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, 'PS256', 'PS384', 'PS512');\n  RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, 'PS256', 'PS384', 'PS512');\n}\n\nmodule.exports = function (jwtString, secretOrPublicKey, options, callback) {\n  if ((typeof options === 'function') && !callback) {\n    callback = options;\n    options = {};\n  }\n\n  if (!options) {\n    options = {};\n  }\n\n  //clone this object since we are going to mutate it.\n  options = Object.assign({}, options);\n\n  let done;\n\n  if (callback) {\n    done = callback;\n  } else {\n    done = function(err, data) {\n      if (err) throw err;\n      return data;\n    };\n  }\n\n  if (options.clockTimestamp && typeof options.clockTimestamp !== 'number') {\n    return done(new JsonWebTokenError('clockTimestamp must be a number'));\n  }\n\n  if (options.nonce !== undefined && (typeof options.nonce !== 'string' || options.nonce.trim() === '')) {\n    return done(new JsonWebTokenError('nonce must be a non-empty string'));\n  }\n\n  if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== 'boolean') {\n    return done(new JsonWebTokenError('allowInvalidAsymmetricKeyTypes must be a boolean'));\n  }\n\n  const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n\n  if (!jwtString){\n    return done(new JsonWebTokenError('jwt must be provided'));\n  }\n\n  if (typeof jwtString !== 'string') {\n    return done(new JsonWebTokenError('jwt must be a string'));\n  }\n\n  const parts = jwtString.split('.');\n\n  if (parts.length !== 3){\n    return done(new JsonWebTokenError('jwt malformed'));\n  }\n\n  let decodedToken;\n\n  try {\n    decodedToken = decode(jwtString, { complete: true });\n  } catch(err) {\n    return done(err);\n  }\n\n  if (!decodedToken) {\n    return done(new JsonWebTokenError('invalid token'));\n  }\n\n  const header = decodedToken.header;\n  let getSecret;\n\n  if(typeof secretOrPublicKey === 'function') {\n    if(!callback) {\n      return done(new JsonWebTokenError('verify must be called asynchronous if secret or public key is provided as a callback'));\n    }\n\n    getSecret = secretOrPublicKey;\n  }\n  else {\n    getSecret = function(header, secretCallback) {\n      return secretCallback(null, secretOrPublicKey);\n    };\n  }\n\n  return getSecret(header, function(err, secretOrPublicKey) {\n    if(err) {\n      return done(new JsonWebTokenError('error in secret or public key callback: ' + err.message));\n    }\n\n    const hasSignature = parts[2].trim() !== '';\n\n    if (!hasSignature && secretOrPublicKey){\n      return done(new JsonWebTokenError('jwt signature is required'));\n    }\n\n    if (hasSignature && !secretOrPublicKey) {\n      return done(new JsonWebTokenError('secret or public key must be provided'));\n    }\n\n    if (!hasSignature && !options.algorithms) {\n      return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n    }\n\n    if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n      try {\n        secretOrPublicKey = createPublicKey(secretOrPublicKey);\n      } catch (_) {\n        try {\n          secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === 'string' ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n        } catch (_) {\n          return done(new JsonWebTokenError('secretOrPublicKey is not valid key material'))\n        }\n      }\n    }\n\n    if (!options.algorithms) {\n      if (secretOrPublicKey.type === 'secret') {\n        options.algorithms = HS_ALGS;\n      } else if (['rsa', 'rsa-pss'].includes(secretOrPublicKey.asymmetricKeyType)) {\n        options.algorithms = RSA_KEY_ALGS\n      } else if (secretOrPublicKey.asymmetricKeyType === 'ec') {\n        options.algorithms = EC_KEY_ALGS\n      } else {\n        options.algorithms = PUB_KEY_ALGS\n      }\n    }\n\n    if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n      return done(new JsonWebTokenError('invalid algorithm'));\n    }\n\n    if (header.alg.startsWith('HS') && secretOrPublicKey.type !== 'secret') {\n      return done(new JsonWebTokenError((`secretOrPublicKey must be a symmetric key when using ${header.alg}`)))\n    } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== 'public') {\n      return done(new JsonWebTokenError((`secretOrPublicKey must be an asymmetric key when using ${header.alg}`)))\n    }\n\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n      try {\n        validateAsymmetricKey(header.alg, secretOrPublicKey);\n      } catch (e) {\n        return done(e);\n      }\n    }\n\n    let valid;\n\n    try {\n      valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n    } catch (e) {\n      return done(e);\n    }\n\n    if (!valid) {\n      return done(new JsonWebTokenError('invalid signature'));\n    }\n\n    const payload = decodedToken.payload;\n\n    if (typeof payload.nbf !== 'undefined' && !options.ignoreNotBefore) {\n      if (typeof payload.nbf !== 'number') {\n        return done(new JsonWebTokenError('invalid nbf value'));\n      }\n      if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n        return done(new NotBeforeError('jwt not active', new Date(payload.nbf * 1000)));\n      }\n    }\n\n    if (typeof payload.exp !== 'undefined' && !options.ignoreExpiration) {\n      if (typeof payload.exp !== 'number') {\n        return done(new JsonWebTokenError('invalid exp value'));\n      }\n      if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('jwt expired', new Date(payload.exp * 1000)));\n      }\n    }\n\n    if (options.audience) {\n      const audiences = Array.isArray(options.audience) ? options.audience : [options.audience];\n      const target = Array.isArray(payload.aud) ? payload.aud : [payload.aud];\n\n      const match = target.some(function (targetAudience) {\n        return audiences.some(function (audience) {\n          return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n        });\n      });\n\n      if (!match) {\n        return done(new JsonWebTokenError('jwt audience invalid. expected: ' + audiences.join(' or ')));\n      }\n    }\n\n    if (options.issuer) {\n      const invalid_issuer =\n              (typeof options.issuer === 'string' && payload.iss !== options.issuer) ||\n              (Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1);\n\n      if (invalid_issuer) {\n        return done(new JsonWebTokenError('jwt issuer invalid. expected: ' + options.issuer));\n      }\n    }\n\n    if (options.subject) {\n      if (payload.sub !== options.subject) {\n        return done(new JsonWebTokenError('jwt subject invalid. expected: ' + options.subject));\n      }\n    }\n\n    if (options.jwtid) {\n      if (payload.jti !== options.jwtid) {\n        return done(new JsonWebTokenError('jwt jwtid invalid. expected: ' + options.jwtid));\n      }\n    }\n\n    if (options.nonce) {\n      if (payload.nonce !== options.nonce) {\n        return done(new JsonWebTokenError('jwt nonce invalid. expected: ' + options.nonce));\n      }\n    }\n\n    if (options.maxAge) {\n      if (typeof payload.iat !== 'number') {\n        return done(new JsonWebTokenError('iat required when maxAge is specified'));\n      }\n\n      const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n      if (typeof maxAgeTimestamp === 'undefined') {\n        return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n      }\n      if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('maxAge exceeded', new Date(maxAgeTimestamp * 1000)));\n      }\n    }\n\n    if (options.complete === true) {\n      const signature = decodedToken.signature;\n\n      return done(null, {\n        header: header,\n        payload: payload,\n        signature: signature\n      });\n    }\n\n    return done(null, payload);\n  });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/verify.js\n");

/***/ })

};
;