import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { generateOtp, hashPassword, sendOtpMail } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();
    
    const { firstName, lastName, email, password, phoneNumber } = await request.json();

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return NextResponse.json({
        success: false,
        message: 'Email already exists'
      }, { status: 400 });
    }

    // Generate OTP
    const otp = generateOtp();
    
    // Send OTP email
    await sendOtpMail(email, firstName, '', otp);

    // Store user data and OTP in cookies (temporary storage)
    const response = NextResponse.json({
      success: true,
      message: 'OTP sent successfully'
    });

    // Set cookies with user data and OTP
    response.cookies.set('user_data', JSON.stringify({
      firstName,
      lastName,
      email,
      password,
      phoneNumber
    }), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 5 * 60 * 1000, // 5 minutes
      sameSite: 'strict'
    });

    response.cookies.set('otp', otp, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 5 * 60 * 1000, // 5 minutes
      sameSite: 'strict'
    });

    return response;

  } catch (error) {
    console.error('Register Error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to send OTP'
    }, { status: 500 });
  }
}
