"use client";

import { createContext, useContext, useState, useEffect } from "react";
import { toast } from 'react-toastify';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Restore authUser from token on mount or when profile is updated
  useEffect(() => {
    const restoreAuth = async () => {
      const token = localStorage.getItem("token");
      if (token) {
        try {
          console.log("Fetching user data from server...");
          // Fetch user data using the token
          const response = await fetch("/api/auth/me", {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            credentials: "include",
          });

          if (!response.ok) {
            // If the token is invalid or expired, clear it
            if (response.status === 401) {
              console.log("Token expired or invalid, clearing token");
              localStorage.removeItem("token");
            }
            setUser(null);
            setLoading(false);
            return;
          }

          const data = await response.json();
          console.log("Auth data from server:", data);

          if (data.success && data.data) {
            console.log("Setting auth user with role:", data.data.role);
            console.log("Business logo URL:", data.data.businessLogo);
            setUser(data.data); // Restore user data
          } else {
            console.log("Invalid response format from server");
            setUser(null);
          }
        } catch (err) {
          console.error("Restore Auth Error:", err);
          // Don't remove token on network errors, as it might be a temporary issue
          setUser(null);
        }
      }
      setLoading(false); // Done checking
    };
    restoreAuth();

    // Listen for profile updates
    const handleStorageChange = (e) => {
      // If this is a custom event (no key) or the updatedProfile key changed
      if (!e.key || e.key === 'updatedProfile') {
        console.log("Profile updated, refreshing user data");
        restoreAuth();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const login = (userData, token) => {
    console.log("Login with user data:", userData);
    console.log("User role:", userData.role);
    localStorage.setItem("token", token);
    setUser(userData);
    toast.success(`Welcome back, ${userData.firstName || userData.ownerFirstName || 'User'}!`);
  };

  const logout = () => {
    localStorage.removeItem("token");
    setUser(null);
    toast.info("You have been logged out");
  };

  const updateUser = (userData) => {
    const updatedUser = { ...user, ...userData };
    setUser(updatedUser);
    // Trigger storage event to refresh auth data
    window.dispatchEvent(new StorageEvent('storage', { key: 'updatedProfile' }));
  };

  const value = {
    user,
    setUser,
    loading,
    login,
    logout,
    updateUser,
    isAuthenticated: !!user,
    isBusinessOwner: user?.role === 'business_owner',
    isAdmin: user?.role === 'admin',
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
