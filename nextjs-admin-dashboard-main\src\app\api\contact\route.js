import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { apiResponse } from '@/lib/apiResponse';
import { sendEmail } from '@/lib/email';

// Contact Message Schema (we'll create this model)
import mongoose from 'mongoose';

const contactMessageSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  subject: {
    type: String,
    required: true,
    trim: true
  },
  message: {
    type: String,
    required: true,
    trim: true
  },
  status: {
    type: String,
    enum: ['new', 'read', 'replied', 'closed'],
    default: 'new'
  },
  ipAddress: String,
  userAgent: String,
  createdAt: {
    type: Date,
    default: Date.now
  }
});

const ContactMessage = mongoose.models.ContactMessage || mongoose.model('ContactMessage', contactMessageSchema);

export async function POST(request) {
  try {
    await connectDB();
    
    const { name, email, subject, message } = await request.json();
    
    // Validate required fields
    if (!name || !email || !subject || !message) {
      return apiResponse.error('All fields are required', 400);
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return apiResponse.error('Invalid email format', 400);
    }
    
    // Get client info
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    // Create contact message
    const contactMessage = new ContactMessage({
      name,
      email,
      subject,
      message,
      ipAddress: clientIP,
      userAgent
    });
    
    await contactMessage.save();
    
    // Send email notification to admin
    try {
      await sendEmail({
        to: process.env.ADMIN_EMAIL || '<EMAIL>',
        subject: `New Contact Message: ${subject}`,
        html: `
          <h2>New Contact Message Received</h2>
          <p><strong>From:</strong> ${name} (${email})</p>
          <p><strong>Subject:</strong> ${subject}</p>
          <p><strong>Message:</strong></p>
          <p>${message.replace(/\n/g, '<br>')}</p>
          <hr>
          <p><small>Sent from IP: ${clientIP}</small></p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send email notification:', emailError);
      // Don't fail the request if email fails
    }
    
    // Send auto-reply to user
    try {
      await sendEmail({
        to: email,
        subject: 'Thank you for contacting BookMyService',
        html: `
          <h2>Thank you for reaching out!</h2>
          <p>Dear ${name},</p>
          <p>We have received your message and will get back to you as soon as possible.</p>
          <p><strong>Your message:</strong></p>
          <p><em>"${message}"</em></p>
          <p>Our typical response time is within 24 hours during business days.</p>
          <br>
          <p>Best regards,<br>The BookMyService Team</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send auto-reply:', emailError);
      // Don't fail the request if email fails
    }
    
    return apiResponse.success(
      { id: contactMessage._id },
      'Message sent successfully! We\'ll get back to you soon.',
      201
    );
    
  } catch (error) {
    console.error('Error processing contact form:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return apiResponse.error(`Validation error: ${errors.join(', ')}`, 400);
    }
    
    return apiResponse.error('Failed to send message. Please try again later.', 500);
  }
}

export async function GET(request) {
  try {
    await connectDB();
    
    // This endpoint is for admin use only - we'll add auth later
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const status = searchParams.get('status');
    const skip = (page - 1) * limit;
    
    let query = {};
    if (status) {
      query.status = status;
    }
    
    const messages = await ContactMessage.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    const total = await ContactMessage.countDocuments(query);
    
    return apiResponse.success({
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }, 'Contact messages fetched successfully');
    
  } catch (error) {
    console.error('Error fetching contact messages:', error);
    return apiResponse.error('Failed to fetch contact messages', 500);
  }
}
