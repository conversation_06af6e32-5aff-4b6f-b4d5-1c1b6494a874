# BookMyService - Next.js Platform

A comprehensive service booking platform built with Next.js, featuring role-based access control, real-time booking management, and a modern admin dashboard.

## 🚀 Features

### User Roles & Capabilities

#### **Regular Users**
- Browse and search services by category
- Book services with preferred date/time
- Track booking status and history
- Leave reviews and ratings
- Manage profile and preferences

#### **Business Owners**
- Create and manage service listings
- Handle booking requests (accept/reject)
- Track revenue and performance analytics
- Manage availability and pricing
- Communicate with customers

#### **Admins & Super Admins**
- User management and role assignment
- Service oversight and moderation
- Platform analytics and reporting
- Content management
- System configuration

### Core Functionality
- **Authentication & Authorization**: JWT-based auth with role-based access
- **Service Management**: Full CRUD operations for services
- **Booking System**: Real-time booking with status tracking
- **Payment Integration**: Ready for Stripe/PayPal integration
- **Email Notifications**: Automated email system for all interactions
- **Search & Filtering**: Advanced search with category and location filters
- **Rating & Reviews**: Customer feedback system
- **Dashboard Analytics**: Comprehensive reporting for all user types

## 🛠 Tech Stack

- **Frontend**: Next.js 14, React 18, Tailwind CSS
- **Backend**: Next.js API Routes, Node.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT tokens with bcrypt password hashing
- **Email**: Nodemailer with multiple provider support
- **UI Components**: Custom components with Tailwind CSS
- **State Management**: React Context API
- **File Upload**: Ready for cloud storage integration

## 📁 Project Structure

```
src/
├── app/                          # Next.js App Router
│   ├── api/                      # API Routes
│   │   ├── auth/                 # Authentication endpoints
│   │   ├── admin/                # Admin-only endpoints
│   │   ├── business/             # Business owner endpoints
│   │   ├── bookings/             # Booking management
│   │   ├── services/             # Service management
│   │   └── contact/              # Contact form
│   ├── admin/                    # Admin dashboard pages
│   ├── business/                 # Business owner pages
│   ├── auth/                     # Authentication pages
│   ├── services/                 # Service browsing
│   ├── my-bookings/              # User booking history
│   └── contact/                  # Contact page
├── components/                   # Reusable components
│   ├── Auth/                     # Authentication components
│   ├── Layouts/                  # Layout components
│   │   └── sidebar/              # Role-based sidebars
│   └── FormElements/             # Form components
├── contexts/                     # React contexts
│   └── AuthContext.jsx           # Authentication context
├── lib/                          # Utility libraries
│   ├── mongodb.js                # Database connection
│   ├── auth-utils.js             # Authentication utilities
│   ├── apiResponse.js            # API response helpers
│   └── email.js                  # Email utilities
└── models/                       # Database models
    ├── User.js                   # User model
    ├── Service.js                # Service model
    └── Booking.js                # Booking model
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- MongoDB database
- Email service (Gmail, SendGrid, etc.)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd nextjs-admin-dashboard-main
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Setup**
Create a `.env.local` file in the root directory:

```env
# Database
MONGODB_URI=mongodb://localhost:27017/bookmyservice
# or MongoDB Atlas: mongodb+srv://username:<EMAIL>/bookmyservice

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Email Configuration (Choose one)
# Gmail
EMAIL_SERVICE=gmail
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your-app-password

# SendGrid
EMAIL_SERVICE=sendgrid
SENDGRID_API_KEY=your-sendgrid-api-key

# Generic SMTP
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password

# Email Settings
FROM_NAME=BookMyService
FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# App Settings
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret
```

4. **Start the development server**
```bash
npm run dev
```

5. **Create test users** (Optional)
Visit `http://localhost:3000/api/test-users` to create sample users for testing.

## 👥 Test Users

After running the test users API, you can login with:

- **Regular User**: `<EMAIL>` / `password123`
- **Business Owner**: `<EMAIL>` / `password123`  
- **Admin**: `<EMAIL>` / `password123`
- **Super Admin**: `<EMAIL>` / `password123`

## 🔐 Authentication Flow

1. **Registration**: Users register with email verification
2. **Login**: JWT token-based authentication
3. **Role Assignment**: Automatic role-based dashboard routing
4. **Protected Routes**: Role-based access control
5. **Token Refresh**: Automatic token management

## 📊 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/forgot-password` - Password reset request
- `POST /api/auth/reset-password` - Password reset

### Services
- `GET /api/services` - List all services
- `POST /api/services` - Create service (Business Owner)
- `GET /api/services/categories` - Get service categories
- `GET /api/business/services` - Get business owner's services
- `PATCH /api/business/services/:id/toggle-status` - Toggle service status

### Bookings
- `GET /api/bookings` - List bookings (role-based)
- `POST /api/bookings` - Create booking
- `GET /api/bookings/my-bookings` - User's bookings
- `PATCH /api/bookings/:id/cancel` - Cancel booking
- `PATCH /api/business/bookings/:id/status` - Update booking status

### Admin
- `GET /api/admin/users` - List all users
- `PATCH /api/admin/users/:id/toggle-status` - Toggle user status
- `PATCH /api/admin/users/:id/role` - Change user role
- `GET /api/admin/services` - List all services
- `DELETE /api/admin/services/:id` - Delete service

## 🎨 UI Components

### Role-Based Sidebars
- **UserSidebar**: Home, Browse Services, My Bookings, Profile
- **BusinessOwnerSidebar**: Dashboard, Manage Services, Bookings, Revenue
- **AdminSidebar**: Users, Services, Analytics, Settings

### Form Components
- Input fields with validation
- Select dropdowns
- File upload components
- Date/time pickers

## 🔧 Configuration

### Email Setup
The platform supports multiple email providers:

1. **Gmail**: Use App Passwords for authentication
2. **SendGrid**: Professional email service
3. **Generic SMTP**: Any SMTP provider

### Database Setup
- MongoDB with Mongoose ODM
- Automatic indexing for performance
- Data validation and sanitization

### Security Features
- Password hashing with bcrypt
- JWT token authentication
- Rate limiting on sensitive endpoints
- Input validation and sanitization
- Role-based access control

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push

### Other Platforms
- **Netlify**: Configure build settings
- **Railway**: One-click deployment
- **DigitalOcean**: App Platform deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: [Link to docs]

## 🔄 Updates & Roadmap

### Current Version: 1.0.0
- ✅ Core booking system
- ✅ Role-based access control
- ✅ Admin dashboard
- ✅ Email notifications

### Upcoming Features
- 🔄 Payment integration (Stripe/PayPal)
- 🔄 Real-time chat system
- 🔄 Mobile app (React Native)
- 🔄 Advanced analytics
- 🔄 Multi-language support

---

**Built with ❤️ using Next.js and modern web technologies**
