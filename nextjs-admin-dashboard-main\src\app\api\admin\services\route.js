import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Service from '@/models/Service';
import Booking from '@/models/Booking';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';

export async function GET(request) {
  try {
    await connectDB();
    
    // Verify authentication and admin role
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || (decoded.role !== 'admin' && decoded.role !== 'super_admin')) {
      return apiResponse.error('Admin access required', 403);
    }
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const skip = (page - 1) * limit;
    
    // Build query
    let query = {};
    
    // Category filter
    if (category && category !== 'all') {
      query.category = category;
    }
    
    // Status filter
    if (status && status !== 'all') {
      query.status = status;
    }
    
    // Search filter
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }
    
    // Get services with business owner details and booking counts
    const services = await Service.find(query)
      .populate('businessOwner', 'businessName ownerFirstName ownerLastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    // Get booking counts for each service
    const servicesWithStats = await Promise.all(
      services.map(async (service) => {
        const bookingsCount = await Booking.countDocuments({ 
          service: service._id 
        });
        
        const revenue = await Booking.aggregate([
          { $match: { service: service._id, status: 'completed' } },
          { $group: { _id: null, total: { $sum: '$totalAmount' } } }
        ]);
        
        return {
          ...service.toObject(),
          bookingsCount,
          totalRevenue: revenue[0]?.total || 0
        };
      })
    );
    
    const total = await Service.countDocuments(query);
    
    // Get service statistics
    const stats = await Service.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);
    
    const categoryStats = await Service.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);
    
    return apiResponse.success({
      services: servicesWithStats,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      stats: {
        byStatus: stats,
        byCategory: categoryStats
      }
    }, 'Services fetched successfully');
    
  } catch (error) {
    console.error('Error fetching admin services:', error);
    return apiResponse.error('Failed to fetch services', 500);
  }
}
