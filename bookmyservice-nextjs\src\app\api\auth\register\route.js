import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { generateOTP } from '@/middleware/auth';
import { validateEmail, validatePhone } from '@/lib/utils';

export async function POST(request) {
  try {
    await connectDB();
    
    const { 
      firstName, 
      lastName, 
      email, 
      phoneNumber, 
      role = 'user',
      // Business owner specific fields
      businessName,
      businessCategory,
      businessDescription,
      businessAddress,
      businessPhone,
      businessEmail
    } = await request.json();
    
    // Validate required fields
    if (!firstName || !lastName || !email) {
      return NextResponse.json({
        success: false,
        message: 'First name, last name, and email are required'
      }, { status: 400 });
    }
    
    if (!validateEmail(email)) {
      return NextResponse.json({
        success: false,
        message: 'Please provide a valid email address'
      }, { status: 400 });
    }
    
    if (phoneNumber && !validatePhone(phoneNumber)) {
      return NextResponse.json({
        success: false,
        message: 'Please provide a valid phone number'
      }, { status: 400 });
    }
    
    // Validate role
    if (!['user', 'business_owner'].includes(role)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid role specified'
      }, { status: 400 });
    }
    
    // Additional validation for business owners
    if (role === 'business_owner') {
      if (!businessName || !businessCategory) {
        return NextResponse.json({
          success: false,
          message: 'Business name and category are required for business owners'
        }, { status: 400 });
      }
    }
    
    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    
    if (existingUser) {
      return NextResponse.json({
        success: false,
        message: 'An account with this email already exists'
      }, { status: 409 });
    }
    
    // Generate OTP for email verification
    const otp = generateOTP();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    
    // Create user data
    const userData = {
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.toLowerCase().trim(),
      phoneNumber: phoneNumber?.trim(),
      role,
      otp,
      otpExpires,
      isEmailVerified: false
    };
    
    // Add business owner specific fields
    if (role === 'business_owner') {
      userData.businessName = businessName.trim();
      userData.businessCategory = businessCategory;
      userData.businessDescription = businessDescription?.trim();
      userData.businessPhone = businessPhone?.trim();
      userData.businessEmail = businessEmail?.toLowerCase().trim();
      
      if (businessAddress) {
        userData.businessAddress = {
          street: businessAddress.street?.trim(),
          city: businessAddress.city?.trim(),
          state: businessAddress.state?.trim(),
          zipCode: businessAddress.zipCode?.trim(),
          country: businessAddress.country?.trim() || 'United States'
        };
      }
    }
    
    // Create new user
    const user = new User(userData);
    await user.save();
    
    // TODO: Send OTP via email (implement email service)
    console.log(`Registration OTP for ${email}: ${otp}`); // For development
    
    return NextResponse.json({
      success: true,
      message: 'Registration successful. Please verify your email with the OTP sent to your email address.',
      data: {
        userId: user._id,
        email: user.email,
        role: user.role,
        // In development, return OTP for testing
        ...(process.env.NODE_ENV === 'development' && { otp })
      }
    }, { status: 201 });
    
  } catch (error) {
    console.error('Registration error:', error);
    
    // Handle mongoose validation errors
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json({
        success: false,
        message: 'Validation failed',
        errors
      }, { status: 400 });
    }
    
    // Handle duplicate key error
    if (error.code === 11000) {
      return NextResponse.json({
        success: false,
        message: 'An account with this email already exists'
      }, { status: 409 });
    }
    
    return NextResponse.json({
      success: false,
      message: 'An error occurred during registration'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    success: false,
    message: 'Method not allowed'
  }, { status: 405 });
}
