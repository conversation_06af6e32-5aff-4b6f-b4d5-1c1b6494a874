{"name": "bookmyservice", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "helmet": "^8.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongodb": "^6.12.0", "mongoose": "^8.9.5", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.16", "nodemon": "^3.1.9", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "stripe": "^18.0.0"}}