/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/profile/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/profile/page.jsx */ \"(app-pages-browser)/./src/app/profile/page.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQmh1c2hhbiUyMHBhdGlsJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQm9vayUyMG15JTIwU2VydmljZSUyMG5ldyU1QyU1Q25leHRqcy1hZG1pbi1kYXNoYm9hcmQtbWFpbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb2ZpbGUlNUMlNUNwYWdlLmpzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhLQUErSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQmh1c2hhbiBwYXRpbFxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXEJvb2sgbXkgU2VydmljZSBuZXdcXFxcbmV4dGpzLWFkbWluLWRhc2hib2FyZC1tYWluXFxcXHNyY1xcXFxhcHBcXFxccHJvZmlsZVxcXFxwYWdlLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/profile/page.jsx":
/*!**********************************!*\
  !*** ./src/app/profile/page.jsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfilePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ProfilePage() {\n    var _user_firstName, _user_lastName;\n    _s();\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: (user === null || user === void 0 ? void 0 : user.firstName) || '',\n        lastName: (user === null || user === void 0 ? void 0 : user.lastName) || '',\n        email: (user === null || user === void 0 ? void 0 : user.email) || '',\n        phoneNumber: (user === null || user === void 0 ? void 0 : user.phoneNumber) || '',\n        businessName: (user === null || user === void 0 ? void 0 : user.businessName) || '',\n        businessCategory: (user === null || user === void 0 ? void 0 : user.businessCategory) || '',\n        businessDescription: (user === null || user === void 0 ? void 0 : user.businessDescription) || ''\n    });\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Update user context\n            updateUser(formData);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success('Profile updated successfully!');\n        } catch (error) {\n            console.error('Profile update error:', error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to update profile');\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-96 items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-heading-3 font-bold text-dark dark:text-white mb-4\",\n                        children: \"Access Denied\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-body-sm text-dark-5 dark:text-dark-6\",\n                        children: \"Please log in to view your profile.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-heading-3 font-bold text-dark dark:text-white\",\n                        children: \"Profile Settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-body-sm text-dark-5 dark:text-dark-6\",\n                        children: \"Manage your account information and preferences.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto h-24 w-24 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-gray-600 dark:text-gray-300\",\n                                                children: [\n                                                    (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName.charAt(0),\n                                                    (_user_lastName = user.lastName) === null || _user_lastName === void 0 ? void 0 : _user_lastName.charAt(0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-dark dark:text-white\",\n                                            children: [\n                                                user.firstName,\n                                                \" \",\n                                                user.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 mt-2\",\n                                            children: user.role.replace('_', ' ').toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 dark:text-gray-400\",\n                                                    children: \"Member since:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-dark dark:text-white\",\n                                                    children: new Date(user.createdAt || Date.now()).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 dark:text-gray-400\",\n                                                    children: \"Email verified:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat(user.isEmailVerified ? 'text-green-600' : 'text-red-600'),\n                                                    children: user.isEmailVerified ? 'Yes' : 'No'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 dark:text-gray-400\",\n                                                    children: \"Account status:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat(user.isActive ? 'text-green-600' : 'text-red-600'),\n                                                    children: user.isActive ? 'Active' : 'Inactive'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-dark dark:text-white mb-6\",\n                                    children: \"Personal Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"firstName\",\n                                                            className: \"block text-sm font-medium text-dark dark:text-white mb-2\",\n                                                            children: \"First Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"firstName\",\n                                                            name: \"firstName\",\n                                                            value: formData.firstName,\n                                                            onChange: handleInputChange,\n                                                            className: \"w-full rounded-lg border border-gray-300 px-4 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-dark dark:text-white\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"lastName\",\n                                                            className: \"block text-sm font-medium text-dark dark:text-white mb-2\",\n                                                            children: \"Last Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"lastName\",\n                                                            name: \"lastName\",\n                                                            value: formData.lastName,\n                                                            onChange: handleInputChange,\n                                                            className: \"w-full rounded-lg border border-gray-300 px-4 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-dark dark:text-white\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"email\",\n                                                    className: \"block text-sm font-medium text-dark dark:text-white mb-2\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    value: formData.email,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full rounded-lg border border-gray-300 px-4 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-dark dark:text-white\",\n                                                    required: true,\n                                                    disabled: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                    children: \"Email cannot be changed. Contact support if needed.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"phoneNumber\",\n                                                    className: \"block text-sm font-medium text-dark dark:text-white mb-2\",\n                                                    children: \"Phone Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"tel\",\n                                                    id: \"phoneNumber\",\n                                                    name: \"phoneNumber\",\n                                                    value: formData.phoneNumber,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full rounded-lg border border-gray-300 px-4 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-dark dark:text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        user.role === 'business_owner' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"border-gray-200 dark:border-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-md font-semibold text-dark dark:text-white\",\n                                                    children: \"Business Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"businessName\",\n                                                            className: \"block text-sm font-medium text-dark dark:text-white mb-2\",\n                                                            children: \"Business Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"businessName\",\n                                                            name: \"businessName\",\n                                                            value: formData.businessName,\n                                                            onChange: handleInputChange,\n                                                            className: \"w-full rounded-lg border border-gray-300 px-4 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-dark dark:text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"businessCategory\",\n                                                            className: \"block text-sm font-medium text-dark dark:text-white mb-2\",\n                                                            children: \"Business Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"businessCategory\",\n                                                            name: \"businessCategory\",\n                                                            value: formData.businessCategory,\n                                                            onChange: handleInputChange,\n                                                            className: \"w-full rounded-lg border border-gray-300 px-4 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-dark dark:text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"Select Category\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Cleaning\",\n                                                                    children: \"Cleaning\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Repair & Maintenance\",\n                                                                    children: \"Repair & Maintenance\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Home & Garden\",\n                                                                    children: \"Home & Garden\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Health & Wellness\",\n                                                                    children: \"Health & Wellness\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Technology\",\n                                                                    children: \"Technology\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Other\",\n                                                                    children: \"Other\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"businessDescription\",\n                                                            className: \"block text-sm font-medium text-dark dark:text-white mb-2\",\n                                                            children: \"Business Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"businessDescription\",\n                                                            name: \"businessDescription\",\n                                                            value: formData.businessDescription,\n                                                            onChange: handleInputChange,\n                                                            rows: 4,\n                                                            className: \"w-full rounded-lg border border-gray-300 px-4 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-dark dark:text-white\",\n                                                            placeholder: \"Describe your business and services...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: loading,\n                                                    className: \"inline-flex items-center justify-center rounded-lg bg-primary px-6 py-3 text-sm font-medium text-white hover:bg-blue-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Updating...\"\n                                                        ]\n                                                    }, void 0, true) : 'Update Profile'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"inline-flex items-center justify-center rounded-lg border border-gray-300 px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800 transition-colors\",\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\profile\\\\page.jsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"oBGxbK9+YV4InUR8jxKnZZGtzHc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/profile/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Restore authUser from token on mount or when profile is updated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const restoreAuth = {\n                \"AuthProvider.useEffect.restoreAuth\": async ()=>{\n                    const token = localStorage.getItem(\"token\");\n                    if (token) {\n                        try {\n                            console.log(\"Fetching user data from server...\");\n                            // Fetch user data using the token\n                            const response = await fetch(\"/api/auth/me\", {\n                                method: \"GET\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\",\n                                    Authorization: \"Bearer \".concat(token)\n                                },\n                                credentials: \"include\"\n                            });\n                            if (!response.ok) {\n                                // If the token is invalid or expired, clear it\n                                if (response.status === 401) {\n                                    console.log(\"Token expired or invalid, clearing token\");\n                                    localStorage.removeItem(\"token\");\n                                }\n                                setUser(null);\n                                setLoading(false);\n                                return;\n                            }\n                            const data = await response.json();\n                            console.log(\"Auth data from server:\", data);\n                            if (data.success && data.data) {\n                                console.log(\"Setting auth user with role:\", data.data.role);\n                                console.log(\"Business logo URL:\", data.data.businessLogo);\n                                setUser(data.data); // Restore user data\n                            } else {\n                                console.log(\"Invalid response format from server\");\n                                setUser(null);\n                            }\n                        } catch (err) {\n                            console.error(\"Restore Auth Error:\", err);\n                            // Don't remove token on network errors, as it might be a temporary issue\n                            setUser(null);\n                        }\n                    }\n                    setLoading(false); // Done checking\n                }\n            }[\"AuthProvider.useEffect.restoreAuth\"];\n            restoreAuth();\n            // Listen for profile updates\n            const handleStorageChange = {\n                \"AuthProvider.useEffect.handleStorageChange\": (e)=>{\n                    // If this is a custom event (no key) or the updatedProfile key changed\n                    if (!e.key || e.key === 'updatedProfile') {\n                        console.log(\"Profile updated, refreshing user data\");\n                        restoreAuth();\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = (userData, token)=>{\n        console.log(\"Login with user data:\", userData);\n        console.log(\"User role:\", userData.role);\n        localStorage.setItem(\"token\", token);\n        setUser(userData);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Welcome back, \".concat(userData.firstName || userData.ownerFirstName || 'User', \"!\"));\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"token\");\n        setUser(null);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"You have been logged out\");\n    };\n    const updateUser = (userData)=>{\n        const updatedUser = {\n            ...user,\n            ...userData\n        };\n        setUser(updatedUser);\n        // Trigger storage event to refresh auth data\n        window.dispatchEvent(new StorageEvent('storage', {\n            key: 'updatedProfile'\n        }));\n    };\n    const value = {\n        user,\n        setUser,\n        loading,\n        login,\n        logout,\n        updateUser,\n        isAuthenticated: !!user,\n        isBusinessOwner: (user === null || user === void 0 ? void 0 : user.role) === 'business_owner',\n        isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.role) === 'super_admin',\n        isSuperAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'super_admin'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\contexts\\\\AuthContext.jsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE$2\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function disabledLog() {}\n    function disableLogs() {\n      if (0 === disabledDepth) {\n        prevLog = console.log;\n        prevInfo = console.info;\n        prevWarn = console.warn;\n        prevError = console.error;\n        prevGroup = console.group;\n        prevGroupCollapsed = console.groupCollapsed;\n        prevGroupEnd = console.groupEnd;\n        var props = {\n          configurable: !0,\n          enumerable: !0,\n          value: disabledLog,\n          writable: !0\n        };\n        Object.defineProperties(console, {\n          info: props,\n          log: props,\n          warn: props,\n          error: props,\n          group: props,\n          groupCollapsed: props,\n          groupEnd: props\n        });\n      }\n      disabledDepth++;\n    }\n    function reenableLogs() {\n      disabledDepth--;\n      if (0 === disabledDepth) {\n        var props = { configurable: !0, enumerable: !0, writable: !0 };\n        Object.defineProperties(console, {\n          log: assign({}, props, { value: prevLog }),\n          info: assign({}, props, { value: prevInfo }),\n          warn: assign({}, props, { value: prevWarn }),\n          error: assign({}, props, { value: prevError }),\n          group: assign({}, props, { value: prevGroup }),\n          groupCollapsed: assign({}, props, { value: prevGroupCollapsed }),\n          groupEnd: assign({}, props, { value: prevGroupEnd })\n        });\n      }\n      0 > disabledDepth &&\n        console.error(\n          \"disabledDepth fell below zero. This is a bug in React. Please file an issue.\"\n        );\n    }\n    function describeBuiltInComponentFrame(name) {\n      if (void 0 === prefix)\n        try {\n          throw Error();\n        } catch (x) {\n          var match = x.stack.trim().match(/\\n( *(at )?)/);\n          prefix = (match && match[1]) || \"\";\n          suffix =\n            -1 < x.stack.indexOf(\"\\n    at\")\n              ? \" (<anonymous>)\"\n              : -1 < x.stack.indexOf(\"@\")\n                ? \"@unknown:0:0\"\n                : \"\";\n        }\n      return \"\\n\" + prefix + name + suffix;\n    }\n    function describeNativeComponentFrame(fn, construct) {\n      if (!fn || reentry) return \"\";\n      var frame = componentFrameCache.get(fn);\n      if (void 0 !== frame) return frame;\n      reentry = !0;\n      frame = Error.prepareStackTrace;\n      Error.prepareStackTrace = void 0;\n      var previousDispatcher = null;\n      previousDispatcher = ReactSharedInternals.H;\n      ReactSharedInternals.H = null;\n      disableLogs();\n      try {\n        var RunInRootFrame = {\n          DetermineComponentFrameRoot: function () {\n            try {\n              if (construct) {\n                var Fake = function () {\n                  throw Error();\n                };\n                Object.defineProperty(Fake.prototype, \"props\", {\n                  set: function () {\n                    throw Error();\n                  }\n                });\n                if (\"object\" === typeof Reflect && Reflect.construct) {\n                  try {\n                    Reflect.construct(Fake, []);\n                  } catch (x) {\n                    var control = x;\n                  }\n                  Reflect.construct(fn, [], Fake);\n                } else {\n                  try {\n                    Fake.call();\n                  } catch (x$0) {\n                    control = x$0;\n                  }\n                  fn.call(Fake.prototype);\n                }\n              } else {\n                try {\n                  throw Error();\n                } catch (x$1) {\n                  control = x$1;\n                }\n                (Fake = fn()) &&\n                  \"function\" === typeof Fake.catch &&\n                  Fake.catch(function () {});\n              }\n            } catch (sample) {\n              if (sample && control && \"string\" === typeof sample.stack)\n                return [sample.stack, control.stack];\n            }\n            return [null, null];\n          }\n        };\n        RunInRootFrame.DetermineComponentFrameRoot.displayName =\n          \"DetermineComponentFrameRoot\";\n        var namePropDescriptor = Object.getOwnPropertyDescriptor(\n          RunInRootFrame.DetermineComponentFrameRoot,\n          \"name\"\n        );\n        namePropDescriptor &&\n          namePropDescriptor.configurable &&\n          Object.defineProperty(\n            RunInRootFrame.DetermineComponentFrameRoot,\n            \"name\",\n            { value: \"DetermineComponentFrameRoot\" }\n          );\n        var _RunInRootFrame$Deter =\n            RunInRootFrame.DetermineComponentFrameRoot(),\n          sampleStack = _RunInRootFrame$Deter[0],\n          controlStack = _RunInRootFrame$Deter[1];\n        if (sampleStack && controlStack) {\n          var sampleLines = sampleStack.split(\"\\n\"),\n            controlLines = controlStack.split(\"\\n\");\n          for (\n            _RunInRootFrame$Deter = namePropDescriptor = 0;\n            namePropDescriptor < sampleLines.length &&\n            !sampleLines[namePropDescriptor].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            namePropDescriptor++;\n          for (\n            ;\n            _RunInRootFrame$Deter < controlLines.length &&\n            !controlLines[_RunInRootFrame$Deter].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            _RunInRootFrame$Deter++;\n          if (\n            namePropDescriptor === sampleLines.length ||\n            _RunInRootFrame$Deter === controlLines.length\n          )\n            for (\n              namePropDescriptor = sampleLines.length - 1,\n                _RunInRootFrame$Deter = controlLines.length - 1;\n              1 <= namePropDescriptor &&\n              0 <= _RunInRootFrame$Deter &&\n              sampleLines[namePropDescriptor] !==\n                controlLines[_RunInRootFrame$Deter];\n\n            )\n              _RunInRootFrame$Deter--;\n          for (\n            ;\n            1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter;\n            namePropDescriptor--, _RunInRootFrame$Deter--\n          )\n            if (\n              sampleLines[namePropDescriptor] !==\n              controlLines[_RunInRootFrame$Deter]\n            ) {\n              if (1 !== namePropDescriptor || 1 !== _RunInRootFrame$Deter) {\n                do\n                  if (\n                    (namePropDescriptor--,\n                    _RunInRootFrame$Deter--,\n                    0 > _RunInRootFrame$Deter ||\n                      sampleLines[namePropDescriptor] !==\n                        controlLines[_RunInRootFrame$Deter])\n                  ) {\n                    var _frame =\n                      \"\\n\" +\n                      sampleLines[namePropDescriptor].replace(\n                        \" at new \",\n                        \" at \"\n                      );\n                    fn.displayName &&\n                      _frame.includes(\"<anonymous>\") &&\n                      (_frame = _frame.replace(\"<anonymous>\", fn.displayName));\n                    \"function\" === typeof fn &&\n                      componentFrameCache.set(fn, _frame);\n                    return _frame;\n                  }\n                while (1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter);\n              }\n              break;\n            }\n        }\n      } finally {\n        (reentry = !1),\n          (ReactSharedInternals.H = previousDispatcher),\n          reenableLogs(),\n          (Error.prepareStackTrace = frame);\n      }\n      sampleLines = (sampleLines = fn ? fn.displayName || fn.name : \"\")\n        ? describeBuiltInComponentFrame(sampleLines)\n        : \"\";\n      \"function\" === typeof fn && componentFrameCache.set(fn, sampleLines);\n      return sampleLines;\n    }\n    function describeUnknownElementTypeFrameInDEV(type) {\n      if (null == type) return \"\";\n      if (\"function\" === typeof type) {\n        var prototype = type.prototype;\n        return describeNativeComponentFrame(\n          type,\n          !(!prototype || !prototype.isReactComponent)\n        );\n      }\n      if (\"string\" === typeof type) return describeBuiltInComponentFrame(type);\n      switch (type) {\n        case REACT_SUSPENSE_TYPE:\n          return describeBuiltInComponentFrame(\"Suspense\");\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame(\"SuspenseList\");\n      }\n      if (\"object\" === typeof type)\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return (type = describeNativeComponentFrame(type.render, !1)), type;\n          case REACT_MEMO_TYPE:\n            return describeUnknownElementTypeFrameInDEV(type.type);\n          case REACT_LAZY_TYPE:\n            prototype = type._payload;\n            type = type._init;\n            try {\n              return describeUnknownElementTypeFrameInDEV(type(prototype));\n            } catch (x) {}\n        }\n      return \"\";\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, self, source, owner, props) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      if (\n        \"string\" === typeof type ||\n        \"function\" === typeof type ||\n        type === REACT_FRAGMENT_TYPE ||\n        type === REACT_PROFILER_TYPE ||\n        type === REACT_STRICT_MODE_TYPE ||\n        type === REACT_SUSPENSE_TYPE ||\n        type === REACT_SUSPENSE_LIST_TYPE ||\n        type === REACT_OFFSCREEN_TYPE ||\n        (\"object\" === typeof type &&\n          null !== type &&\n          (type.$$typeof === REACT_LAZY_TYPE ||\n            type.$$typeof === REACT_MEMO_TYPE ||\n            type.$$typeof === REACT_CONTEXT_TYPE ||\n            type.$$typeof === REACT_CONSUMER_TYPE ||\n            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n            type.$$typeof === REACT_CLIENT_REFERENCE$1 ||\n            void 0 !== type.getModuleId))\n      ) {\n        var children = config.children;\n        if (void 0 !== children)\n          if (isStaticChildren)\n            if (isArrayImpl(children)) {\n              for (\n                isStaticChildren = 0;\n                isStaticChildren < children.length;\n                isStaticChildren++\n              )\n                validateChildKeys(children[isStaticChildren], type);\n              Object.freeze && Object.freeze(children);\n            } else\n              console.error(\n                \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n              );\n          else validateChildKeys(children, type);\n      } else {\n        children = \"\";\n        if (\n          void 0 === type ||\n          (\"object\" === typeof type &&\n            null !== type &&\n            0 === Object.keys(type).length)\n        )\n          children +=\n            \" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\";\n        null === type\n          ? (isStaticChildren = \"null\")\n          : isArrayImpl(type)\n            ? (isStaticChildren = \"array\")\n            : void 0 !== type && type.$$typeof === REACT_ELEMENT_TYPE\n              ? ((isStaticChildren =\n                  \"<\" +\n                  (getComponentNameFromType(type.type) || \"Unknown\") +\n                  \" />\"),\n                (children =\n                  \" Did you accidentally export a JSX literal instead of a component?\"))\n              : (isStaticChildren = typeof type);\n        console.error(\n          \"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",\n          isStaticChildren,\n          children\n        );\n      }\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(type, children, self, source, getOwner(), maybeKey);\n    }\n    function validateChildKeys(node, parentType) {\n      if (\n        \"object\" === typeof node &&\n        node &&\n        node.$$typeof !== REACT_CLIENT_REFERENCE\n      )\n        if (isArrayImpl(node))\n          for (var i = 0; i < node.length; i++) {\n            var child = node[i];\n            isValidElement(child) && validateExplicitKey(child, parentType);\n          }\n        else if (isValidElement(node))\n          node._store && (node._store.validated = 1);\n        else if (\n          (null === node || \"object\" !== typeof node\n            ? (i = null)\n            : ((i =\n                (MAYBE_ITERATOR_SYMBOL && node[MAYBE_ITERATOR_SYMBOL]) ||\n                node[\"@@iterator\"]),\n              (i = \"function\" === typeof i ? i : null)),\n          \"function\" === typeof i &&\n            i !== node.entries &&\n            ((i = i.call(node)), i !== node))\n        )\n          for (; !(node = i.next()).done; )\n            isValidElement(node.value) &&\n              validateExplicitKey(node.value, parentType);\n    }\n    function isValidElement(object) {\n      return (\n        \"object\" === typeof object &&\n        null !== object &&\n        object.$$typeof === REACT_ELEMENT_TYPE\n      );\n    }\n    function validateExplicitKey(element, parentType) {\n      if (\n        element._store &&\n        !element._store.validated &&\n        null == element.key &&\n        ((element._store.validated = 1),\n        (parentType = getCurrentComponentErrorInfo(parentType)),\n        !ownerHasKeyUseWarning[parentType])\n      ) {\n        ownerHasKeyUseWarning[parentType] = !0;\n        var childOwner = \"\";\n        element &&\n          null != element._owner &&\n          element._owner !== getOwner() &&\n          ((childOwner = null),\n          \"number\" === typeof element._owner.tag\n            ? (childOwner = getComponentNameFromType(element._owner.type))\n            : \"string\" === typeof element._owner.name &&\n              (childOwner = element._owner.name),\n          (childOwner = \" It was passed a child from \" + childOwner + \".\"));\n        var prevGetCurrentStack = ReactSharedInternals.getCurrentStack;\n        ReactSharedInternals.getCurrentStack = function () {\n          var stack = describeUnknownElementTypeFrameInDEV(element.type);\n          prevGetCurrentStack && (stack += prevGetCurrentStack() || \"\");\n          return stack;\n        };\n        console.error(\n          'Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',\n          parentType,\n          childOwner\n        );\n        ReactSharedInternals.getCurrentStack = prevGetCurrentStack;\n      }\n    }\n    function getCurrentComponentErrorInfo(parentType) {\n      var info = \"\",\n        owner = getOwner();\n      owner &&\n        (owner = getComponentNameFromType(owner.type)) &&\n        (info = \"\\n\\nCheck the render method of `\" + owner + \"`.\");\n      info ||\n        ((parentType = getComponentNameFromType(parentType)) &&\n          (info =\n            \"\\n\\nCheck the top-level render call using <\" + parentType + \">.\"));\n      return info;\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\"),\n      MAYBE_ITERATOR_SYMBOL = Symbol.iterator,\n      REACT_CLIENT_REFERENCE$2 = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      assign = Object.assign,\n      REACT_CLIENT_REFERENCE$1 = Symbol.for(\"react.client.reference\"),\n      isArrayImpl = Array.isArray,\n      disabledDepth = 0,\n      prevLog,\n      prevInfo,\n      prevWarn,\n      prevError,\n      prevGroup,\n      prevGroupCollapsed,\n      prevGroupEnd;\n    disabledLog.__reactDisabledLog = !0;\n    var prefix,\n      suffix,\n      reentry = !1;\n    var componentFrameCache = new (\n      \"function\" === typeof WeakMap ? WeakMap : Map\n    )();\n    var REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {},\n      ownerHasKeyUseWarning = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self);\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmh1c2hhbiBwYXRpbFxcT25lRHJpdmVcXERlc2t0b3BcXEJvb2sgbXkgU2VydmljZSBuZXdcXG5leHRqcy1hZG1pbi1kYXNoYm9hcmQtbWFpblxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs":
/*!*****************************************!*\
  !*** ./node_modules/clsx/dist/clsx.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clsx);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxjQUFjLGFBQWEsK0NBQStDLGdEQUFnRCxlQUFlLFFBQVEsSUFBSSwwQ0FBMEMseUNBQXlDLFNBQWdCLGdCQUFnQix3Q0FBd0MsSUFBSSxtREFBbUQsU0FBUyxpRUFBZSxJQUFJIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEJodXNoYW4gcGF0aWxcXE9uZURyaXZlXFxEZXNrdG9wXFxCb29rIG15IFNlcnZpY2UgbmV3XFxuZXh0anMtYWRtaW4tZGFzaGJvYXJkLW1haW5cXG5vZGVfbW9kdWxlc1xcY2xzeFxcZGlzdFxcY2xzeC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcihlKXt2YXIgdCxmLG49XCJcIjtpZihcInN0cmluZ1wiPT10eXBlb2YgZXx8XCJudW1iZXJcIj09dHlwZW9mIGUpbis9ZTtlbHNlIGlmKFwib2JqZWN0XCI9PXR5cGVvZiBlKWlmKEFycmF5LmlzQXJyYXkoZSkpe3ZhciBvPWUubGVuZ3RoO2Zvcih0PTA7dDxvO3QrKyllW3RdJiYoZj1yKGVbdF0pKSYmKG4mJihuKz1cIiBcIiksbis9Zil9ZWxzZSBmb3IoZiBpbiBlKWVbZl0mJihuJiYobis9XCIgXCIpLG4rPWYpO3JldHVybiBufWV4cG9ydCBmdW5jdGlvbiBjbHN4KCl7Zm9yKHZhciBlLHQsZj0wLG49XCJcIixvPWFyZ3VtZW50cy5sZW5ndGg7ZjxvO2YrKykoZT1hcmd1bWVudHNbZl0pJiYodD1yKGUpKSYmKG4mJihuKz1cIiBcIiksbis9dCk7cmV0dXJuIG59ZXhwb3J0IGRlZmF1bHQgY2xzeDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs":
/*!****************************************************!*\
  !*** ./node_modules/react-toastify/dist/index.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bounce: () => (/* binding */ lt),\n/* harmony export */   Flip: () => (/* binding */ uo),\n/* harmony export */   Icons: () => (/* binding */ W),\n/* harmony export */   Slide: () => (/* binding */ mo),\n/* harmony export */   ToastContainer: () => (/* binding */ Lt),\n/* harmony export */   Zoom: () => (/* binding */ po),\n/* harmony export */   collapseToast: () => (/* binding */ Z),\n/* harmony export */   cssTransition: () => (/* binding */ $),\n/* harmony export */   toast: () => (/* binding */ y)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ Bounce,Flip,Icons,Slide,ToastContainer,Zoom,collapseToast,cssTransition,toast auto */ function Mt(t) {\n    if (!t || typeof document == \"undefined\") return;\n    let o = document.head || document.getElementsByTagName(\"head\")[0], e = document.createElement(\"style\");\n    e.type = \"text/css\", o.firstChild ? o.insertBefore(e, o.firstChild) : o.appendChild(e), e.styleSheet ? e.styleSheet.cssText = t : e.appendChild(document.createTextNode(t));\n}\n_c = Mt;\nMt(':root{--toastify-color-light: #fff;--toastify-color-dark: #121212;--toastify-color-info: #3498db;--toastify-color-success: #07bc0c;--toastify-color-warning: #f1c40f;--toastify-color-error: hsl(6, 78%, 57%);--toastify-color-transparent: rgba(255, 255, 255, .7);--toastify-icon-color-info: var(--toastify-color-info);--toastify-icon-color-success: var(--toastify-color-success);--toastify-icon-color-warning: var(--toastify-color-warning);--toastify-icon-color-error: var(--toastify-color-error);--toastify-container-width: fit-content;--toastify-toast-width: 320px;--toastify-toast-offset: 16px;--toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));--toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));--toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));--toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));--toastify-toast-background: #fff;--toastify-toast-padding: 14px;--toastify-toast-min-height: 64px;--toastify-toast-max-height: 800px;--toastify-toast-bd-radius: 6px;--toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, .1);--toastify-font-family: sans-serif;--toastify-z-index: 9999;--toastify-text-color-light: #757575;--toastify-text-color-dark: #fff;--toastify-text-color-info: #fff;--toastify-text-color-success: #fff;--toastify-text-color-warning: #fff;--toastify-text-color-error: #fff;--toastify-spinner-color: #616161;--toastify-spinner-color-empty-area: #e0e0e0;--toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);--toastify-color-progress-dark: #bb86fc;--toastify-color-progress-info: var(--toastify-color-info);--toastify-color-progress-success: var(--toastify-color-success);--toastify-color-progress-warning: var(--toastify-color-warning);--toastify-color-progress-error: var(--toastify-color-error);--toastify-color-progress-bgo: .2}.Toastify__toast-container{z-index:var(--toastify-z-index);-webkit-transform:translate3d(0,0,var(--toastify-z-index));position:fixed;width:var(--toastify-container-width);box-sizing:border-box;color:#fff;display:flex;flex-direction:column}.Toastify__toast-container--top-left{top:var(--toastify-toast-top);left:var(--toastify-toast-left)}.Toastify__toast-container--top-center{top:var(--toastify-toast-top);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--top-right{top:var(--toastify-toast-top);right:var(--toastify-toast-right);align-items:end}.Toastify__toast-container--bottom-left{bottom:var(--toastify-toast-bottom);left:var(--toastify-toast-left)}.Toastify__toast-container--bottom-center{bottom:var(--toastify-toast-bottom);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--bottom-right{bottom:var(--toastify-toast-bottom);right:var(--toastify-toast-right);align-items:end}.Toastify__toast{--y: 0;position:relative;touch-action:none;width:var(--toastify-toast-width);min-height:var(--toastify-toast-min-height);box-sizing:border-box;margin-bottom:1rem;padding:var(--toastify-toast-padding);border-radius:var(--toastify-toast-bd-radius);box-shadow:var(--toastify-toast-shadow);max-height:var(--toastify-toast-max-height);font-family:var(--toastify-font-family);z-index:0;display:flex;flex:1 auto;align-items:center;word-break:break-word}@media only screen and (max-width: 480px){.Toastify__toast-container{width:100vw;left:env(safe-area-inset-left);margin:0}.Toastify__toast-container--top-left,.Toastify__toast-container--top-center,.Toastify__toast-container--top-right{top:env(safe-area-inset-top);transform:translate(0)}.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-right{bottom:env(safe-area-inset-bottom);transform:translate(0)}.Toastify__toast-container--rtl{right:env(safe-area-inset-right);left:initial}.Toastify__toast{--toastify-toast-width: 100%;margin-bottom:0;border-radius:0}}.Toastify__toast-container[data-stacked=true]{width:var(--toastify-toast-width)}.Toastify__toast--stacked{position:absolute;width:100%;transform:translate3d(0,var(--y),0) scale(var(--s));transition:transform .3s}.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,.Toastify__toast--stacked[data-collapsed] .Toastify__close-button{transition:opacity .1s}.Toastify__toast--stacked[data-collapsed=false]{overflow:visible}.Toastify__toast--stacked[data-collapsed=true]:not(:last-child)>*{opacity:0}.Toastify__toast--stacked:after{content:\"\";position:absolute;left:0;right:0;height:calc(var(--g) * 1px);bottom:100%}.Toastify__toast--stacked[data-pos=top]{top:0}.Toastify__toast--stacked[data-pos=bot]{bottom:0}.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before{transform-origin:top}.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before{transform-origin:bottom}.Toastify__toast--stacked:before{content:\"\";position:absolute;left:0;right:0;bottom:0;height:100%;transform:scaleY(3);z-index:-1}.Toastify__toast--rtl{direction:rtl}.Toastify__toast--close-on-click{cursor:pointer}.Toastify__toast-icon{margin-inline-end:10px;width:22px;flex-shrink:0;display:flex}.Toastify--animate{animation-fill-mode:both;animation-duration:.5s}.Toastify--animate-icon{animation-fill-mode:both;animation-duration:.3s}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--light,.Toastify__toast-theme--colored.Toastify__toast--default{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{color:var(--toastify-text-color-info);background:var(--toastify-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{color:var(--toastify-text-color-success);background:var(--toastify-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{color:var(--toastify-text-color-warning);background:var(--toastify-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{color:var(--toastify-text-color-error);background:var(--toastify-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error{background:var(--toastify-color-transparent)}.Toastify__close-button{color:#fff;position:absolute;top:6px;right:6px;background:transparent;outline:none;border:none;padding:0;cursor:pointer;opacity:.7;transition:.3s ease;z-index:1}.Toastify__toast--rtl .Toastify__close-button{left:6px;right:unset}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentColor;height:16px;width:14px}.Toastify__close-button:hover,.Toastify__close-button:focus{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:1;opacity:.7;transform-origin:left}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{right:0;left:initial;transform-origin:right;border-bottom-left-radius:initial}.Toastify__progress-bar--wrp{position:absolute;overflow:hidden;bottom:0;left:0;width:100%;height:5px;border-bottom-left-radius:var(--toastify-toast-bd-radius);border-bottom-right-radius:var(--toastify-toast-bd-radius)}.Toastify__progress-bar--wrp[data-hidden=true]{opacity:0}.Toastify__progress-bar--bg{opacity:var(--toastify-color-progress-bgo);width:100%;height:100%}.Toastify__spinner{width:20px;height:20px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:var(--toastify-spinner-color-empty-area);border-right-color:var(--toastify-spinner-color);animation:Toastify__spin .65s linear infinite}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,var(--y),0)}to{opacity:0;transform:translate3d(2000px,var(--y),0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,var(--y),0)}to{opacity:0;transform:translate3d(-2000px,var(--y),0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--top-left,.Toastify__bounce-enter--bottom-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--top-right,.Toastify__bounce-enter--bottom-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--top-left,.Toastify__bounce-exit--bottom-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--top-right,.Toastify__bounce-exit--bottom-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:translate3d(0,var(--y),0) scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:translate3d(0,var(--y),0) perspective(400px)}30%{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(-20deg);opacity:1}to{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(90deg);opacity:0}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideOutRight{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(110%,var(--y),0)}}@keyframes Toastify__slideOutLeft{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(-110%,var(--y),0)}}@keyframes Toastify__slideOutDown{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,500px,0)}}@keyframes Toastify__slideOutUp{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,-500px,0)}}.Toastify__slide-enter--top-left,.Toastify__slide-enter--bottom-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--top-right,.Toastify__slide-enter--bottom-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--top-left,.Toastify__slide-exit--bottom-left{animation-name:Toastify__slideOutLeft;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-right,.Toastify__slide-exit--bottom-right{animation-name:Toastify__slideOutRight;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown;animation-timing-function:ease-in;animation-duration:.3s}@keyframes Toastify__spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\\n');\n\nvar L = (t)=>typeof t == \"number\" && !isNaN(t), N = (t)=>typeof t == \"string\", P = (t)=>typeof t == \"function\", mt = (t)=>N(t) || L(t), B = (t)=>N(t) || P(t) ? t : null, pt = (t, o)=>t === !1 || L(t) && t > 0 ? t : o, z = (t)=>/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t) || N(t) || P(t) || L(t);\n\nfunction Z(t, o) {\n    let e = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 300;\n    let { scrollHeight: r, style: s } = t;\n    requestAnimationFrame(()=>{\n        s.minHeight = \"initial\", s.height = r + \"px\", s.transition = \"all \".concat(e, \"ms\"), requestAnimationFrame(()=>{\n            s.height = \"0\", s.padding = \"0\", s.margin = \"0\", setTimeout(o, e);\n        });\n    });\n}\n_c1 = Z;\nfunction $(param) {\n    let { enter: t, exit: o, appendPosition: e = !1, collapse: r = !0, collapseDuration: s = 300 } = param;\n    return function(param) {\n        let { children: a, position: d, preventExitTransition: c, done: T, nodeRef: g, isIn: v, playToast: x } = param;\n        let C = e ? \"\".concat(t, \"--\").concat(d) : t, S = e ? \"\".concat(o, \"--\").concat(d) : o, E = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n        return (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(()=>{\n            let f = g.current, p = C.split(\" \"), b = (n)=>{\n                n.target === g.current && (x(), f.removeEventListener(\"animationend\", b), f.removeEventListener(\"animationcancel\", b), E.current === 0 && n.type !== \"animationcancel\" && f.classList.remove(...p));\n            };\n            (()=>{\n                f.classList.add(...p), f.addEventListener(\"animationend\", b), f.addEventListener(\"animationcancel\", b);\n            })();\n        }, []), (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n            let f = g.current, p = ()=>{\n                f.removeEventListener(\"animationend\", p), r ? Z(f, T, s) : T();\n            };\n            v || (c ? p() : (()=>{\n                E.current = 1, f.className += \" \".concat(S), f.addEventListener(\"animationend\", p);\n            })());\n        }, [\n            v\n        ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, a);\n    };\n}\n\nfunction J(t, o) {\n    return {\n        content: tt(t.content, t.props),\n        containerId: t.props.containerId,\n        id: t.props.toastId,\n        theme: t.props.theme,\n        type: t.props.type,\n        data: t.props.data || {},\n        isLoading: t.props.isLoading,\n        icon: t.props.icon,\n        reason: t.removalReason,\n        status: o\n    };\n}\n_c2 = J;\nfunction tt(t, o) {\n    let e = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : !1;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t) && !N(t.type) ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(t, {\n        closeToast: o.closeToast,\n        toastProps: o,\n        data: o.data,\n        isPaused: e\n    }) : P(t) ? t({\n        closeToast: o.closeToast,\n        toastProps: o,\n        data: o.data,\n        isPaused: e\n    }) : t;\n}\n\nfunction yt(param) {\n    let { closeToast: t, theme: o, ariaLabel: e = \"close\" } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        className: \"Toastify__close-button Toastify__close-button--\".concat(o),\n        type: \"button\",\n        onClick: (r)=>{\n            r.stopPropagation(), t(!0);\n        },\n        \"aria-label\": e\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n        \"aria-hidden\": \"true\",\n        viewBox: \"0 0 14 16\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z\"\n    })));\n}\n\n\nfunction gt(param) {\n    let { delay: t, isRunning: o, closeToast: e, type: r = \"default\", hide: s, className: l, controlledProgress: a, progress: d, rtl: c, isIn: T, theme: g } = param;\n    let v = s || a && d === 0, x = {\n        animationDuration: \"\".concat(t, \"ms\"),\n        animationPlayState: o ? \"running\" : \"paused\"\n    };\n    a && (x.transform = \"scaleX(\".concat(d, \")\"));\n    let C = (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"Toastify__progress-bar\", a ? \"Toastify__progress-bar--controlled\" : \"Toastify__progress-bar--animated\", \"Toastify__progress-bar-theme--\".concat(g), \"Toastify__progress-bar--\".concat(r), {\n        [\"Toastify__progress-bar--rtl\"]: c\n    }), S = P(l) ? l({\n        rtl: c,\n        type: r,\n        defaultClassName: C\n    }) : (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(C, l), E = {\n        [a && d >= 1 ? \"onTransitionEnd\" : \"onAnimationEnd\"]: a && d < 1 ? null : ()=>{\n            T && e();\n        }\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"Toastify__progress-bar--wrp\",\n        \"data-hidden\": v\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"Toastify__progress-bar--bg Toastify__progress-bar-theme--\".concat(g, \" Toastify__progress-bar--\").concat(r)\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        role: \"progressbar\",\n        \"aria-hidden\": v ? \"true\" : \"false\",\n        \"aria-label\": \"notification timer\",\n        className: S,\n        style: x,\n        ...E\n    }));\n}\n\n\nvar Xt = 1, at = ()=>\"\".concat(Xt++);\nfunction _t(t, o, e) {\n    let r = 1, s = 0, l = [], a = [], d = o, c = new Map, T = new Set, g = (i)=>(T.add(i), ()=>T.delete(i)), v = ()=>{\n        a = Array.from(c.values()), T.forEach((i)=>i());\n    }, x = (param)=>{\n        let { containerId: i, toastId: n, updateId: u } = param;\n        let h = i ? i !== t : t !== 1, m = c.has(n) && u == null;\n        return h || m;\n    }, C = (i, n)=>{\n        c.forEach((u)=>{\n            var h;\n            (n == null || n === u.props.toastId) && ((h = u.toggle) == null || h.call(u, i));\n        });\n    }, S = (i)=>{\n        var n, u;\n        (u = (n = i.props) == null ? void 0 : n.onClose) == null || u.call(n, i.removalReason), i.isActive = !1;\n    }, E = (i)=>{\n        if (i == null) c.forEach(S);\n        else {\n            let n = c.get(i);\n            n && S(n);\n        }\n        v();\n    }, f = ()=>{\n        s -= l.length, l = [];\n    }, p = (i)=>{\n        var m, _;\n        let { toastId: n, updateId: u } = i.props, h = u == null;\n        i.staleId && c.delete(i.staleId), i.isActive = !0, c.set(n, i), v(), e(J(i, h ? \"added\" : \"updated\")), h && ((_ = (m = i.props).onOpen) == null || _.call(m));\n    };\n    return {\n        id: t,\n        props: d,\n        observe: g,\n        toggle: C,\n        removeToast: E,\n        toasts: c,\n        clearQueue: f,\n        buildToast: (i, n)=>{\n            if (x(n)) return;\n            let { toastId: u, updateId: h, data: m, staleId: _, delay: k } = n, M = h == null;\n            M && s++;\n            let A = {\n                ...d,\n                style: d.toastStyle,\n                key: r++,\n                ...Object.fromEntries(Object.entries(n).filter((param)=>{\n                    let [D, Y] = param;\n                    return Y != null;\n                })),\n                toastId: u,\n                updateId: h,\n                data: m,\n                isIn: !1,\n                className: B(n.className || d.toastClassName),\n                progressClassName: B(n.progressClassName || d.progressClassName),\n                autoClose: n.isLoading ? !1 : pt(n.autoClose, d.autoClose),\n                closeToast (D) {\n                    c.get(u).removalReason = D, E(u);\n                },\n                deleteToast () {\n                    let D = c.get(u);\n                    if (D != null) {\n                        if (e(J(D, \"removed\")), c.delete(u), s--, s < 0 && (s = 0), l.length > 0) {\n                            p(l.shift());\n                            return;\n                        }\n                        v();\n                    }\n                }\n            };\n            A.closeButton = d.closeButton, n.closeButton === !1 || z(n.closeButton) ? A.closeButton = n.closeButton : n.closeButton === !0 && (A.closeButton = z(d.closeButton) ? d.closeButton : !0);\n            let R = {\n                content: i,\n                props: A,\n                staleId: _\n            };\n            d.limit && d.limit > 0 && s > d.limit && M ? l.push(R) : L(k) ? setTimeout(()=>{\n                p(R);\n            }, k) : p(R);\n        },\n        setProps (i) {\n            d = i;\n        },\n        setToggle: (i, n)=>{\n            let u = c.get(i);\n            u && (u.toggle = n);\n        },\n        isToastActive: (i)=>{\n            var n;\n            return (n = c.get(i)) == null ? void 0 : n.isActive;\n        },\n        getSnapshot: ()=>a\n    };\n}\nvar I = new Map, F = [], st = new Set, Vt = (t)=>st.forEach((o)=>o(t)), bt = ()=>I.size > 0;\nfunction Qt() {\n    F.forEach((t)=>nt(t.content, t.options)), F = [];\n}\n_c3 = Qt;\nvar vt = (t, param)=>{\n    let { containerId: o } = param;\n    var e;\n    return (e = I.get(o || 1)) == null ? void 0 : e.toasts.get(t);\n};\nfunction X(t, o) {\n    var r;\n    if (o) return !!((r = I.get(o)) != null && r.isToastActive(t));\n    let e = !1;\n    return I.forEach((s)=>{\n        s.isToastActive(t) && (e = !0);\n    }), e;\n}\n_c4 = X;\nfunction ht(t) {\n    if (!bt()) {\n        F = F.filter((o)=>t != null && o.options.toastId !== t);\n        return;\n    }\n    if (t == null || mt(t)) I.forEach((o)=>{\n        o.removeToast(t);\n    });\n    else if (t && (\"containerId\" in t || \"id\" in t)) {\n        let o = I.get(t.containerId);\n        o ? o.removeToast(t.id) : I.forEach((e)=>{\n            e.removeToast(t.id);\n        });\n    }\n}\nvar Ct = function() {\n    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    I.forEach((o)=>{\n        o.props.limit && (!t.containerId || o.id === t.containerId) && o.clearQueue();\n    });\n};\n_c5 = Ct;\nfunction nt(t, o) {\n    z(t) && (bt() || F.push({\n        content: t,\n        options: o\n    }), I.forEach((e)=>{\n        e.buildToast(t, o);\n    }));\n}\nfunction xt(t) {\n    var o;\n    (o = I.get(t.containerId || 1)) == null || o.setToggle(t.id, t.fn);\n}\nfunction rt(t, o) {\n    I.forEach((e)=>{\n        (o == null || !(o != null && o.containerId) || (o == null ? void 0 : o.containerId) === e.id) && e.toggle(t, o == null ? void 0 : o.id);\n    });\n}\nfunction Et(t) {\n    let o = t.containerId || 1;\n    return {\n        subscribe (e) {\n            let r = _t(o, t, Vt);\n            I.set(o, r);\n            let s = r.observe(e);\n            return Qt(), ()=>{\n                s(), I.delete(o);\n            };\n        },\n        setProps (e) {\n            var r;\n            (r = I.get(o)) == null || r.setProps(e);\n        },\n        getSnapshot () {\n            var e;\n            return (e = I.get(o)) == null ? void 0 : e.getSnapshot();\n        }\n    };\n}\n_c6 = Et;\nfunction Pt(t) {\n    return st.add(t), ()=>{\n        st.delete(t);\n    };\n}\n_c7 = Pt;\nfunction Wt(t) {\n    return t && (N(t.toastId) || L(t.toastId)) ? t.toastId : at();\n}\n_c8 = Wt;\nfunction U(t, o) {\n    return nt(t, o), o.toastId;\n}\n_c9 = U;\nfunction V(t, o) {\n    return {\n        ...o,\n        type: o && o.type || t,\n        toastId: Wt(o)\n    };\n}\n_c10 = V;\nfunction Q(t) {\n    return (o, e)=>U(o, V(t, e));\n}\n_c11 = Q;\nfunction y(t, o) {\n    return U(t, V(\"default\", o));\n}\ny.loading = (t, o)=>U(t, V(\"default\", {\n        isLoading: !0,\n        autoClose: !1,\n        closeOnClick: !1,\n        closeButton: !1,\n        draggable: !1,\n        ...o\n    }));\nfunction Gt(t, param, s) {\n    let { pending: o, error: e, success: r } = param;\n    let l;\n    o && (l = N(o) ? y.loading(o, s) : y.loading(o.render, {\n        ...s,\n        ...o\n    }));\n    let a = {\n        isLoading: null,\n        autoClose: null,\n        closeOnClick: null,\n        closeButton: null,\n        draggable: null\n    }, d = (T, g, v)=>{\n        if (g == null) {\n            y.dismiss(l);\n            return;\n        }\n        let x = {\n            type: T,\n            ...a,\n            ...s,\n            data: v\n        }, C = N(g) ? {\n            render: g\n        } : g;\n        return l ? y.update(l, {\n            ...x,\n            ...C\n        }) : y(C.render, {\n            ...x,\n            ...C\n        }), v;\n    }, c = P(t) ? t() : t;\n    return c.then((T)=>d(\"success\", r, T)).catch((T)=>d(\"error\", e, T)), c;\n}\n_c12 = Gt;\ny.promise = Gt;\ny.success = Q(\"success\");\ny.info = Q(\"info\");\ny.error = Q(\"error\");\ny.warning = Q(\"warning\");\ny.warn = y.warning;\ny.dark = (t, o)=>U(t, V(\"default\", {\n        theme: \"dark\",\n        ...o\n    }));\nfunction qt(t) {\n    ht(t);\n}\ny.dismiss = qt;\ny.clearWaitingQueue = Ct;\ny.isActive = X;\ny.update = function(t) {\n    let o = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    let e = vt(t, o);\n    if (e) {\n        let { props: r, content: s } = e, l = {\n            delay: 100,\n            ...r,\n            ...o,\n            toastId: o.toastId || t,\n            updateId: at()\n        };\n        l.toastId !== t && (l.staleId = t);\n        let a = l.render || s;\n        delete l.render, U(a, l);\n    }\n};\ny.done = (t)=>{\n    y.update(t, {\n        progress: 1\n    });\n};\ny.onChange = Pt;\ny.play = (t)=>rt(!0, t);\ny.pause = (t)=>rt(!1, t);\n\nfunction It(t) {\n    var a;\n    let { subscribe: o, getSnapshot: e, setProps: r } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Et(t)).current;\n    r(t);\n    let s = (a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)(o, e, e)) == null ? void 0 : a.slice();\n    function l(d) {\n        if (!s) return [];\n        let c = new Map;\n        return t.newestOnTop && s.reverse(), s.forEach((T)=>{\n            let { position: g } = T.props;\n            c.has(g) || c.set(g, []), c.get(g).push(T);\n        }), Array.from(c, (T)=>d(T[0], T[1]));\n    }\n    return {\n        getToastToRender: l,\n        isToastActive: X,\n        count: s == null ? void 0 : s.length\n    };\n}\n_c13 = It;\n\nfunction At(t) {\n    let [o, e] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!1), [r, s] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!1), l = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null), a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        start: 0,\n        delta: 0,\n        removalDistance: 0,\n        canCloseOnClick: !0,\n        canDrag: !1,\n        didMove: !1\n    }).current, { autoClose: d, pauseOnHover: c, closeToast: T, onClick: g, closeOnClick: v } = t;\n    xt({\n        id: t.toastId,\n        containerId: t.containerId,\n        fn: e\n    }), (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (t.pauseOnFocusLoss) return x(), ()=>{\n            C();\n        };\n    }, [\n        t.pauseOnFocusLoss\n    ]);\n    function x() {\n        document.hasFocus() || p(), window.addEventListener(\"focus\", f), window.addEventListener(\"blur\", p);\n    }\n    function C() {\n        window.removeEventListener(\"focus\", f), window.removeEventListener(\"blur\", p);\n    }\n    function S(m) {\n        if (t.draggable === !0 || t.draggable === m.pointerType) {\n            b();\n            let _ = l.current;\n            a.canCloseOnClick = !0, a.canDrag = !0, _.style.transition = \"none\", t.draggableDirection === \"x\" ? (a.start = m.clientX, a.removalDistance = _.offsetWidth * (t.draggablePercent / 100)) : (a.start = m.clientY, a.removalDistance = _.offsetHeight * (t.draggablePercent === 80 ? t.draggablePercent * 1.5 : t.draggablePercent) / 100);\n        }\n    }\n    function E(m) {\n        let { top: _, bottom: k, left: M, right: A } = l.current.getBoundingClientRect();\n        m.nativeEvent.type !== \"touchend\" && t.pauseOnHover && m.clientX >= M && m.clientX <= A && m.clientY >= _ && m.clientY <= k ? p() : f();\n    }\n    function f() {\n        e(!0);\n    }\n    function p() {\n        e(!1);\n    }\n    function b() {\n        a.didMove = !1, document.addEventListener(\"pointermove\", n), document.addEventListener(\"pointerup\", u);\n    }\n    function i() {\n        document.removeEventListener(\"pointermove\", n), document.removeEventListener(\"pointerup\", u);\n    }\n    function n(m) {\n        let _ = l.current;\n        if (a.canDrag && _) {\n            a.didMove = !0, o && p(), t.draggableDirection === \"x\" ? a.delta = m.clientX - a.start : a.delta = m.clientY - a.start, a.start !== m.clientX && (a.canCloseOnClick = !1);\n            let k = t.draggableDirection === \"x\" ? \"\".concat(a.delta, \"px, var(--y)\") : \"0, calc(\".concat(a.delta, \"px + var(--y))\");\n            _.style.transform = \"translate3d(\".concat(k, \",0)\"), _.style.opacity = \"\".concat(1 - Math.abs(a.delta / a.removalDistance));\n        }\n    }\n    function u() {\n        i();\n        let m = l.current;\n        if (a.canDrag && a.didMove && m) {\n            if (a.canDrag = !1, Math.abs(a.delta) > a.removalDistance) {\n                s(!0), t.closeToast(!0), t.collapseAll();\n                return;\n            }\n            m.style.transition = \"transform 0.2s, opacity 0.2s\", m.style.removeProperty(\"transform\"), m.style.removeProperty(\"opacity\");\n        }\n    }\n    let h = {\n        onPointerDown: S,\n        onPointerUp: E\n    };\n    return d && c && (h.onMouseEnter = p, t.stacked || (h.onMouseLeave = f)), v && (h.onClick = (m)=>{\n        g && g(m), a.canCloseOnClick && T(!0);\n    }), {\n        playToast: f,\n        pauseToast: p,\n        isRunning: o,\n        preventExitTransition: r,\n        toastRef: l,\n        eventHandlers: h\n    };\n}\n_c14 = At;\n\nvar Ot = typeof window != \"undefined\" ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n\n\nvar G = (param)=>{\n    let { theme: t, type: o, isLoading: e, ...r } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n        viewBox: \"0 0 24 24\",\n        width: \"100%\",\n        height: \"100%\",\n        fill: t === \"colored\" ? \"currentColor\" : \"var(--toastify-icon-color-\".concat(o, \")\"),\n        ...r\n    });\n};\n_c15 = G;\nfunction ao(t) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(G, {\n        ...t\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z\"\n    }));\n}\nfunction so(t) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(G, {\n        ...t\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z\"\n    }));\n}\nfunction no(t) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(G, {\n        ...t\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z\"\n    }));\n}\nfunction ro(t) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(G, {\n        ...t\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z\"\n    }));\n}\nfunction io() {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"Toastify__spinner\"\n    });\n}\nvar W = {\n    info: so,\n    warning: ao,\n    success: no,\n    error: ro,\n    spinner: io\n}, lo = (t)=>t in W;\nfunction Nt(param) {\n    let { theme: t, type: o, isLoading: e, icon: r } = param;\n    let s = null, l = {\n        theme: t,\n        type: o\n    };\n    return r === !1 || (P(r) ? s = r({\n        ...l,\n        isLoading: e\n    }) : /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(r) ? s = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(r, l) : e ? s = W.spinner() : lo(o) && (s = W[o](l))), s;\n}\n_c16 = Nt;\nvar wt = (t)=>{\n    let { isRunning: o, preventExitTransition: e, toastRef: r, eventHandlers: s, playToast: l } = At(t), { closeButton: a, children: d, autoClose: c, onClick: T, type: g, hideProgressBar: v, closeToast: x, transition: C, position: S, className: E, style: f, progressClassName: p, updateId: b, role: i, progress: n, rtl: u, toastId: h, deleteToast: m, isIn: _, isLoading: k, closeOnClick: M, theme: A, ariaLabel: R } = t, D = (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"Toastify__toast\", \"Toastify__toast-theme--\".concat(A), \"Toastify__toast--\".concat(g), {\n        [\"Toastify__toast--rtl\"]: u\n    }, {\n        [\"Toastify__toast--close-on-click\"]: M\n    }), Y = P(E) ? E({\n        rtl: u,\n        position: S,\n        type: g,\n        defaultClassName: D\n    }) : (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(D, E), ft = Nt(t), dt = !!n || !c, j = {\n        closeToast: x,\n        type: g,\n        theme: A\n    }, H = null;\n    return a === !1 || (P(a) ? H = a(j) : /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(a) ? H = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(a, j) : H = yt(j)), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(C, {\n        isIn: _,\n        done: m,\n        position: S,\n        preventExitTransition: e,\n        nodeRef: r,\n        playToast: l\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        id: h,\n        tabIndex: 0,\n        onClick: T,\n        \"data-in\": _,\n        className: Y,\n        ...s,\n        style: f,\n        ref: r,\n        ..._ && {\n            role: i,\n            \"aria-label\": R\n        }\n    }, ft != null && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"Toastify__toast-icon\", {\n            [\"Toastify--animate-icon Toastify__zoom-enter\"]: !k\n        })\n    }, ft), tt(d, t, !o), H, !t.customProgressBar && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gt, {\n        ...b && !dt ? {\n            key: \"p-\".concat(b)\n        } : {},\n        rtl: u,\n        theme: A,\n        delay: c,\n        isRunning: o,\n        isIn: _,\n        closeToast: x,\n        hide: v,\n        type: g,\n        className: p,\n        controlledProgress: dt,\n        progress: n || 0\n    })));\n};\n_c17 = wt;\nvar K = function(t) {\n    let o = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : !1;\n    return {\n        enter: \"Toastify--animate Toastify__\".concat(t, \"-enter\"),\n        exit: \"Toastify--animate Toastify__\".concat(t, \"-exit\"),\n        appendPosition: o\n    };\n}, lt = $(K(\"bounce\", !0)), mo = $(K(\"slide\", !0)), po = $(K(\"zoom\")), uo = $(K(\"flip\"));\nvar _o = {\n    position: \"top-right\",\n    transition: lt,\n    autoClose: 5e3,\n    closeButton: !0,\n    pauseOnHover: !0,\n    pauseOnFocusLoss: !0,\n    draggable: \"touch\",\n    draggablePercent: 80,\n    draggableDirection: \"x\",\n    role: \"alert\",\n    theme: \"light\",\n    \"aria-label\": \"Notifications Alt+T\",\n    hotKeys: (t)=>t.altKey && t.code === \"KeyT\"\n};\nfunction Lt(t) {\n    let o = {\n        ..._o,\n        ...t\n    }, e = t.stacked, [r, s] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!0), l = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null), { getToastToRender: a, isToastActive: d, count: c } = It(o), { className: T, style: g, rtl: v, containerId: x, hotKeys: C } = o;\n    function S(f) {\n        let p = (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"Toastify__toast-container\", \"Toastify__toast-container--\".concat(f), {\n            [\"Toastify__toast-container--rtl\"]: v\n        });\n        return P(T) ? T({\n            position: f,\n            rtl: v,\n            defaultClassName: p\n        }) : (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(p, B(T));\n    }\n    function E() {\n        e && (s(!0), y.play());\n    }\n    return Ot(()=>{\n        var f;\n        if (e) {\n            let p = l.current.querySelectorAll('[data-in=\"true\"]'), b = 12, i = (f = o.position) == null ? void 0 : f.includes(\"top\"), n = 0, u = 0;\n            Array.from(p).reverse().forEach((h, m)=>{\n                let _ = h;\n                _.classList.add(\"Toastify__toast--stacked\"), m > 0 && (_.dataset.collapsed = \"\".concat(r)), _.dataset.pos || (_.dataset.pos = i ? \"top\" : \"bot\");\n                let k = n * (r ? .2 : 1) + (r ? 0 : b * m);\n                _.style.setProperty(\"--y\", \"\".concat(i ? k : k * -1, \"px\")), _.style.setProperty(\"--g\", \"\".concat(b)), _.style.setProperty(\"--s\", \"\".concat(1 - (r ? u : 0))), n += _.offsetHeight, u += .025;\n            });\n        }\n    }, [\n        r,\n        c,\n        e\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        function f(p) {\n            var i;\n            let b = l.current;\n            C(p) && ((i = b.querySelector('[tabIndex=\"0\"]')) == null || i.focus(), s(!1), y.pause()), p.key === \"Escape\" && (document.activeElement === b || b != null && b.contains(document.activeElement)) && (s(!0), y.play());\n        }\n        return document.addEventListener(\"keydown\", f), ()=>{\n            document.removeEventListener(\"keydown\", f);\n        };\n    }, [\n        C\n    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: l,\n        className: \"Toastify\",\n        id: x,\n        onMouseEnter: ()=>{\n            e && (s(!1), y.pause());\n        },\n        onMouseLeave: E,\n        \"aria-live\": \"polite\",\n        \"aria-atomic\": \"false\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-label\": o[\"aria-label\"]\n    }, a((f, p)=>{\n        let b = p.length ? {\n            ...g\n        } : {\n            ...g,\n            pointerEvents: \"none\"\n        };\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            tabIndex: -1,\n            className: S(f),\n            \"data-stacked\": e,\n            style: b,\n            key: \"c-\".concat(f)\n        }, p.map((param)=>{\n            let { content: i, props: n } = param;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(wt, {\n                ...n,\n                stacked: e,\n                collapseAll: E,\n                isIn: d(n.toastId, n.containerId),\n                key: \"t-\".concat(n.key)\n            }, i);\n        }));\n    }));\n}\n_c18 = Lt;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"Mt\");\n$RefreshReg$(_c1, \"Z\");\n$RefreshReg$(_c2, \"J\");\n$RefreshReg$(_c3, \"Qt\");\n$RefreshReg$(_c4, \"X\");\n$RefreshReg$(_c5, \"Ct\");\n$RefreshReg$(_c6, \"Et\");\n$RefreshReg$(_c7, \"Pt\");\n$RefreshReg$(_c8, \"Wt\");\n$RefreshReg$(_c9, \"U\");\n$RefreshReg$(_c10, \"V\");\n$RefreshReg$(_c11, \"Q\");\n$RefreshReg$(_c12, \"Gt\");\n$RefreshReg$(_c13, \"It\");\n$RefreshReg$(_c14, \"At\");\n$RefreshReg$(_c15, \"G\");\n$RefreshReg$(_c16, \"Nt\");\n$RefreshReg$(_c17, \"wt\");\n$RefreshReg$(_c18, \"Lt\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);