export function sendResponse(res, data = null, message = '', success = true, statusCode = 200) {
  return res.status(statusCode).json({
    success,
    message,
    data,
    timestamp: new Date().toISOString()
  });
}

export function sendSuccess(res, data = null, message = 'Success', statusCode = 200) {
  return sendResponse(res, data, message, true, statusCode);
}

export function sendError(res, message = 'An error occurred', statusCode = 500, data = null) {
  return sendResponse(res, data, message, false, statusCode);
}

export function sendValidationError(res, errors, message = 'Validation failed') {
  return sendResponse(res, { errors }, message, false, 400);
}

export function sendNotFound(res, message = 'Resource not found') {
  return sendError(res, message, 404);
}

export function sendUnauthorized(res, message = 'Unauthorized access') {
  return sendError(res, message, 401);
}

export function sendForbidden(res, message = 'Forbidden access') {
  return sendError(res, message, 403);
}

export function sendConflict(res, message = 'Resource conflict') {
  return sendError(res, message, 409);
}

export function sendTooManyRequests(res, message = 'Too many requests') {
  return sendError(res, message, 429);
}

export function handleApiError(res, error) {
  console.error('API Error:', error);
  
  // Mongoose validation error
  if (error.name === 'ValidationError') {
    const errors = Object.values(error.errors).map(err => ({
      field: err.path,
      message: err.message
    }));
    return sendValidationError(res, errors);
  }
  
  // Mongoose duplicate key error
  if (error.code === 11000) {
    const field = Object.keys(error.keyPattern)[0];
    return sendConflict(res, `${field} already exists`);
  }
  
  // JWT errors
  if (error.name === 'JsonWebTokenError') {
    return sendUnauthorized(res, 'Invalid token');
  }
  
  if (error.name === 'TokenExpiredError') {
    return sendUnauthorized(res, 'Token expired');
  }
  
  // Custom application errors
  if (error.statusCode) {
    return sendError(res, error.message, error.statusCode);
  }
  
  // Default server error
  return sendError(res, 'Internal server error', 500);
}

export class ApiError extends Error {
  constructor(message, statusCode = 500, data = null) {
    super(message);
    this.statusCode = statusCode;
    this.data = data;
    this.name = 'ApiError';
  }
}

export function createApiError(message, statusCode = 500, data = null) {
  return new ApiError(message, statusCode, data);
}

// Common error creators
export const errors = {
  notFound: (resource = 'Resource') => createApiError(`${resource} not found`, 404),
  unauthorized: (message = 'Unauthorized access') => createApiError(message, 401),
  forbidden: (message = 'Forbidden access') => createApiError(message, 403),
  badRequest: (message = 'Bad request') => createApiError(message, 400),
  conflict: (message = 'Resource conflict') => createApiError(message, 409),
  tooManyRequests: (message = 'Too many requests') => createApiError(message, 429),
  internal: (message = 'Internal server error') => createApiError(message, 500)
};

// Async handler wrapper to catch errors
export function asyncHandler(handler) {
  return async (req, res) => {
    try {
      await handler(req, res);
    } catch (error) {
      handleApiError(res, error);
    }
  };
}

// Method not allowed handler
export function methodNotAllowed(res, allowedMethods = []) {
  res.setHeader('Allow', allowedMethods.join(', '));
  return sendError(res, `Method ${res.req?.method} not allowed`, 405);
}
