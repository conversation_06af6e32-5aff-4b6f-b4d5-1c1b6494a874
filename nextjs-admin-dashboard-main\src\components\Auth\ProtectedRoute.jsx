"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

const ProtectedRoute = ({ 
  children, 
  allowedRoles = [], 
  redirectTo = '/auth/login',
  requireAuth = true 
}) => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    if (loading) return; // Wait for auth to load

    // If authentication is required but user is not logged in
    if (requireAuth && !user) {
      router.push(redirectTo);
      return;
    }

    // If user is logged in but doesn't have required role
    if (user && allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
      // Redirect based on user role
      if (user.role === 'business_owner') {
        router.push('/business/dashboard');
      } else if (user.role === 'admin' || user.role === 'super_admin') {
        router.push('/admin/dashboard');
      } else {
        router.push('/');
      }
      return;
    }

    // User is authorized
    setIsAuthorized(true);
  }, [user, loading, router, allowedRoles, redirectTo, requireAuth]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show nothing while redirecting
  if (!isAuthorized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Render children if authorized
  return children;
};

export default ProtectedRoute;
