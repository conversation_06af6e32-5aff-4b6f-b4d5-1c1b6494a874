{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Book%20my%20Service%20new/bookmyservice-nextjs/src/app/page.jsx"], "sourcesContent": ["\"use client\";\n\nimport { useAuth } from \"@/contexts/AuthContext\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect } from \"react\";\nimport Link from \"next/link\";\n\nexport default function HomePage() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    // Redirect authenticated users to appropriate dashboard\n    if (!loading && user) {\n      if (user.role === 'admin') {\n        router.push('/admin/dashboard');\n      } else if (user.role === 'business_owner') {\n        router.push('/business/dashboard');\n      } else {\n        router.push('/dashboard');\n      }\n    }\n  }, [user, loading, router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-lg\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex-shrink-0\">\n                <h1 className=\"text-2xl font-bold text-primary\">BookMyService</h1>\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/auth/login\"\n                className=\"text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Login\n              </Link>\n              <Link\n                href=\"/auth/register\"\n                className=\"bg-primary hover:bg-blue-dark text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Sign Up\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <div className=\"relative overflow-hidden\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"relative z-10 pb-8 bg-transparent sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32\">\n            <main className=\"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28\">\n              <div className=\"sm:text-center lg:text-left\">\n                <h1 className=\"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl\">\n                  <span className=\"block xl:inline\">Book Services</span>{' '}\n                  <span className=\"block text-primary xl:inline\">Made Simple</span>\n                </h1>\n                <p className=\"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0\">\n                  Connect with trusted service providers in your area. From home repairs to personal services, \n                  find and book the services you need with ease.\n                </p>\n                <div className=\"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start\">\n                  <div className=\"rounded-md shadow\">\n                    <Link\n                      href=\"/auth/register\"\n                      className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-blue-dark md:py-4 md:text-lg md:px-10\"\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                  <div className=\"mt-3 sm:mt-0 sm:ml-3\">\n                    <Link\n                      href=\"/services\"\n                      className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary bg-indigo-100 hover:bg-indigo-200 md:py-4 md:text-lg md:px-10\"\n                    >\n                      Browse Services\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </main>\n          </div>\n        </div>\n      </div>\n\n      {/* Features Section */}\n      <div className=\"py-12 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"lg:text-center\">\n            <h2 className=\"text-base text-primary font-semibold tracking-wide uppercase\">Features</h2>\n            <p className=\"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl\">\n              Everything you need to manage services\n            </p>\n          </div>\n\n          <div className=\"mt-10\">\n            <div className=\"space-y-10 md:space-y-0 md:grid md:grid-cols-3 md:gap-x-8 md:gap-y-10\">\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary text-white\">\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">Easy Discovery</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Find the perfect service provider for your needs with our advanced search and filtering options.\n                </p>\n              </div>\n\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary text-white\">\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">Simple Booking</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Book services with just a few clicks. Choose your preferred date and time, and you're all set.\n                </p>\n              </div>\n\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary text-white\">\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n                  </svg>\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">Secure Payments</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Pay securely with our integrated payment system. Your money is protected until the service is completed.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      <div className=\"bg-primary\">\n        <div className=\"max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8\">\n          <h2 className=\"text-3xl font-extrabold text-white sm:text-4xl\">\n            <span className=\"block\">Ready to get started?</span>\n            <span className=\"block\">Join thousands of satisfied customers.</span>\n          </h2>\n          <p className=\"mt-4 text-lg leading-6 text-indigo-200\">\n            Whether you're looking for services or want to offer your own, BookMyService is the perfect platform for you.\n          </p>\n          <Link\n            href=\"/auth/register\"\n            className=\"mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-primary bg-white hover:bg-indigo-50 sm:w-auto\"\n          >\n            Sign up for free\n          </Link>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"bg-white\">\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 md:flex md:items-center md:justify-between lg:px-8\">\n          <div className=\"flex justify-center space-x-6 md:order-2\">\n            <Link href=\"/privacy-policy\" className=\"text-gray-400 hover:text-gray-500\">\n              Privacy Policy\n            </Link>\n            <Link href=\"/terms-and-conditions\" className=\"text-gray-400 hover:text-gray-500\">\n              Terms & Conditions\n            </Link>\n            <Link href=\"/contact\" className=\"text-gray-400 hover:text-gray-500\">\n              Contact\n            </Link>\n          </div>\n          <div className=\"mt-8 md:mt-0 md:order-1\">\n            <p className=\"text-center text-base text-gray-400\">\n              &copy; 2024 BookMyService. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wDAAwD;QACxD,IAAI,CAAC,WAAW,MAAM;YACpB,IAAI,KAAK,IAAI,KAAK,SAAS;gBACzB,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,KAAK,IAAI,KAAK,kBAAkB;gBACzC,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;;;;;;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCACd,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAkB;;;;;;4CAAqB;0DACvD,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;kDAEjD,8OAAC;wCAAE,WAAU;kDAAoG;;;;;;kDAIjH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;0DAIH,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+D;;;;;;8CAC7E,8OAAC;oCAAE,WAAU;8CAAkF;;;;;;;;;;;;sCAKjG,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9D,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;kDAKpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9D,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;kDAKpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9D,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;;;;;;;sCAE1B,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;sCAGtD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAAoC;;;;;;8CAG3E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAAoC;;;;;;8CAGjF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAoC;;;;;;;;;;;;sCAItE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D", "debugId": null}}]}