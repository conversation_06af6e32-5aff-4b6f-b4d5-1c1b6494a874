"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Restore authUser from token on mount or when profile is updated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const restoreAuth = {\n                \"AuthProvider.useEffect.restoreAuth\": async ()=>{\n                    const token = localStorage.getItem(\"token\");\n                    if (token) {\n                        try {\n                            console.log(\"Fetching user data from server...\");\n                            // Fetch user data using the token\n                            const response = await fetch(\"/api/auth/me\", {\n                                method: \"GET\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\",\n                                    Authorization: \"Bearer \".concat(token)\n                                },\n                                credentials: \"include\"\n                            });\n                            if (!response.ok) {\n                                // If the token is invalid or expired, clear it\n                                if (response.status === 401) {\n                                    console.log(\"Token expired or invalid, clearing token\");\n                                    localStorage.removeItem(\"token\");\n                                }\n                                setUser(null);\n                                setLoading(false);\n                                return;\n                            }\n                            const data = await response.json();\n                            console.log(\"Auth data from server:\", data);\n                            if (data.success && data.data) {\n                                console.log(\"Setting auth user with role:\", data.data.role);\n                                console.log(\"Business logo URL:\", data.data.businessLogo);\n                                setUser(data.data); // Restore user data\n                            } else {\n                                console.log(\"Invalid response format from server\");\n                                setUser(null);\n                            }\n                        } catch (err) {\n                            console.error(\"Restore Auth Error:\", err);\n                            // Don't remove token on network errors, as it might be a temporary issue\n                            setUser(null);\n                        }\n                    }\n                    setLoading(false); // Done checking\n                }\n            }[\"AuthProvider.useEffect.restoreAuth\"];\n            restoreAuth();\n            // Listen for profile updates\n            const handleStorageChange = {\n                \"AuthProvider.useEffect.handleStorageChange\": (e)=>{\n                    // If this is a custom event (no key) or the updatedProfile key changed\n                    if (!e.key || e.key === 'updatedProfile') {\n                        console.log(\"Profile updated, refreshing user data\");\n                        restoreAuth();\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = (userData, token)=>{\n        console.log(\"Login with user data:\", userData);\n        console.log(\"User role:\", userData.role);\n        localStorage.setItem(\"token\", token);\n        setUser(userData);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Welcome back, \".concat(userData.firstName || userData.ownerFirstName || 'User', \"!\"));\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"token\");\n        setUser(null);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"You have been logged out\");\n    };\n    const updateUser = (userData)=>{\n        const updatedUser = {\n            ...user,\n            ...userData\n        };\n        setUser(updatedUser);\n        // Trigger storage event to refresh auth data\n        window.dispatchEvent(new StorageEvent('storage', {\n            key: 'updatedProfile'\n        }));\n    };\n    const value = {\n        user,\n        setUser,\n        loading,\n        login,\n        logout,\n        updateUser,\n        isAuthenticated: !!user,\n        isBusinessOwner: (user === null || user === void 0 ? void 0 : user.role) === 'business_owner',\n        isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\contexts\\\\AuthContext.jsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.jsx\n"));

/***/ })

});