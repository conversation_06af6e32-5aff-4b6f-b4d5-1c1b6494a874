import mongoose from 'mongoose';

const UserSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: [true, 'The first name is required.'],
    trim: true,
  },
  lastName: {
    type: String,
    default: null,
    trim: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    default: null,
  },
  phoneNumber: {
    type: String,
    unique: true,
    sparse: true, // Allow multiple null values (for social login users who don't provide phone)
    trim: true,
  },
  avatar: {
    type: String,
  },
  role: {
    type: String,
    default: 'user',
    enum: ['user', 'business_owner', 'admin', 'super_admin'],
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  isEmailVerified: {
    type: Boolean,
    default: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  bookedServiceIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service'
  }],
  serviceOtp: {
    otp: { type: Number },
    expiresAt: { type: Date },
  },
  // Social login fields
  googleId: {
    type: String,
    unique: true,
    sparse: true, // Allow multiple null values
  },
  facebookId: {
    type: String,
    unique: true,
    sparse: true, // Allow multiple null values
  },
  authProvider: {
    type: String,
    enum: ['local', 'google', 'facebook'],
    default: 'local'
  },
  // OTP for verification
  otp: String,
  otpExpires: Date
}, {
  timestamps: true,
});

// Index for better query performance
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ googleId: 1 });
UserSchema.index({ facebookId: 1 });

// Instance methods
UserSchema.methods.generateAuthToken = function () {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    { uId: this._id, role: this.role },
    process.env.JWT_SECRET || 'your-secret-key',
    {
      expiresIn: "24h",
    }
  );
};

UserSchema.methods.toSafeObject = function() {
  const userObject = this.toObject();
  delete userObject.password;
  delete userObject.otp;
  delete userObject.otpExpires;
  delete userObject.serviceOtp;
  return userObject;
};

export default mongoose.models.User || mongoose.model('User', UserSchema);
