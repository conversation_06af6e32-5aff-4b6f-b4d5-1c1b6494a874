import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { generateToken } from '@/middleware/auth';
import { validateEmail } from '@/lib/utils';

export async function POST(request) {
  try {
    await connectDB();
    
    const { email, otp } = await request.json();
    
    // Validate input
    if (!email || !otp) {
      return NextResponse.json({
        success: false,
        message: 'Email and OTP are required'
      }, { status: 400 });
    }
    
    if (!validateEmail(email)) {
      return NextResponse.json({
        success: false,
        message: 'Please provide a valid email address'
      }, { status: 400 });
    }
    
    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'User not found'
      }, { status: 404 });
    }
    
    // Check if <PERSON><PERSON> exists and is not expired
    if (!user.otp || !user.otpExpires) {
      return NextResponse.json({
        success: false,
        message: 'No OTP found. Please request a new one.'
      }, { status: 400 });
    }
    
    if (new Date() > user.otpExpires) {
      return NextResponse.json({
        success: false,
        message: 'OTP has expired. Please request a new one.'
      }, { status: 400 });
    }
    
    // Verify OTP
    if (user.otp !== otp) {
      return NextResponse.json({
        success: false,
        message: 'Invalid OTP. Please try again.'
      }, { status: 400 });
    }
    
    // Clear OTP and update last login
    user.otp = undefined;
    user.otpExpires = undefined;
    user.lastLogin = new Date();
    user.isEmailVerified = true;
    await user.save();
    
    // Generate JWT token
    const token = generateToken(user._id);
    
    // Return user data (safe object without sensitive info)
    const userData = user.toSafeObject();
    
    return NextResponse.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userData,
        token
      }
    });
    
  } catch (error) {
    console.error('OTP verification error:', error);
    return NextResponse.json({
      success: false,
      message: 'An error occurred during verification'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    success: false,
    message: 'Method not allowed'
  }, { status: 405 });
}
