"use client";

import { useState, useEffect } from 'react';

export default function ServicesPage() {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading services
    setTimeout(() => {
      setServices([
        {
          id: 1,
          name: "House Cleaning",
          description: "Professional house cleaning service",
          price: 120,
          category: "Cleaning",
          rating: 4.8,
          image: "/api/placeholder/300/200"
        },
        {
          id: 2,
          name: "Plumbing Repair",
          description: "Expert plumbing repair and maintenance",
          price: 85,
          category: "Repair & Maintenance",
          rating: 4.6,
          image: "/api/placeholder/300/200"
        },
        {
          id: 3,
          name: "Garden Maintenance",
          description: "Complete garden care and landscaping",
          price: 200,
          category: "Home & Garden",
          rating: 4.9,
          image: "/api/placeholder/300/200"
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-heading-3 font-bold text-dark dark:text-white">
            Available Services
          </h1>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <div key={item} className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
              <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-heading-3 font-bold text-dark dark:text-white">
          Available Services
        </h1>
        <div className="flex items-center gap-4">
          <select className="rounded-lg border border-gray-300 px-4 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-dark dark:text-white">
            <option value="">All Categories</option>
            <option value="cleaning">Cleaning</option>
            <option value="repair">Repair & Maintenance</option>
            <option value="garden">Home & Garden</option>
          </select>
          <input
            type="text"
            placeholder="Search services..."
            className="rounded-lg border border-gray-300 px-4 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-dark dark:text-white"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.map((service) => (
          <div key={service.id} className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card hover:shadow-2 transition-shadow">
            <div className="mb-4">
              <div className="h-48 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 text-sm">Service Image</span>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <h3 className="text-lg font-semibold text-dark dark:text-white">
                  {service.name}
                </h3>
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  {service.category}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {service.description}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <svg className="h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {service.rating}
                  </span>
                </div>
                <span className="text-lg font-bold text-primary">
                  ${service.price}
                </span>
              </div>
              
              <button className="w-full rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-blue-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors">
                Book Now
              </button>
            </div>
          </div>
        ))}
      </div>

      {services.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="mx-auto h-24 w-24 text-gray-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">No services found</h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Try adjusting your search criteria or check back later.
          </p>
        </div>
      )}
    </div>
  );
}
