# BookMyService Deployment Guide

This guide covers deploying the BookMyService platform to various hosting providers.

## 🚀 Quick Deployment Options

### 1. Vercel (Recommended)

Vercel is the easiest option for Next.js applications.

#### Steps:
1. **Connect Repository**
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Vercel will auto-detect Next.js settings

2. **Environment Variables**
   - In Vercel dashboard, go to Project Settings → Environment Variables
   - Add all variables from `.env.example`
   - **Important**: Set `NODE_ENV=production`

3. **Database Setup**
   - Use MongoDB Atlas (recommended for production)
   - Get connection string and add to `MONGODB_URI`

4. **Deploy**
   - Push to main branch
   - Vercel automatically deploys

#### Vercel Environment Variables:
```bash
MONGODB_URI=mongodb+srv://username:<EMAIL>/bookmyservice
JWT_SECRET=your-production-jwt-secret
EMAIL_SERVICE=gmail
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your-app-password
FROM_NAME=BookMyService
FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>
NODE_ENV=production
```

### 2. Netlify

#### Steps:
1. **Build Settings**
   - Build command: `npm run build`
   - Publish directory: `.next`

2. **Environment Variables**
   - Add in Netlify dashboard under Site Settings → Environment Variables

3. **Netlify Configuration**
   Create `netlify.toml`:
   ```toml
   [build]
     command = "npm run build"
     publish = ".next"

   [[redirects]]
     from = "/api/*"
     to = "/.netlify/functions/:splat"
     status = 200

   [[redirects]]
     from = "/*"
     to = "/index.html"
     status = 200
   ```

### 3. Railway

#### Steps:
1. **Connect Repository**
   - Go to [railway.app](https://railway.app)
   - Create new project from GitHub

2. **Environment Variables**
   - Add in Railway dashboard

3. **Database**
   - Railway provides MongoDB add-on
   - Or use external MongoDB Atlas

### 4. DigitalOcean App Platform

#### Steps:
1. **Create App**
   - Connect GitHub repository
   - Choose Node.js environment

2. **Build Settings**
   - Build command: `npm run build`
   - Run command: `npm start`

3. **Environment Variables**
   - Add in App Platform dashboard

## 🗄️ Database Setup

### MongoDB Atlas (Recommended)

1. **Create Cluster**
   - Go to [mongodb.com/atlas](https://mongodb.com/atlas)
   - Create free cluster
   - Choose region closest to your users

2. **Database User**
   - Create database user with read/write permissions
   - Use strong password

3. **Network Access**
   - Add IP address `0.0.0.0/0` for global access
   - Or restrict to specific IPs for security

4. **Connection String**
   ```
   mongodb+srv://username:<EMAIL>/bookmyservice?retryWrites=true&w=majority
   ```

### Self-Hosted MongoDB

If you prefer self-hosting:

1. **VPS Setup** (DigitalOcean, Linode, etc.)
2. **Install MongoDB**
3. **Configure Security**
4. **Backup Strategy**

## 📧 Email Configuration

### Gmail Setup (Development)

1. **Enable 2FA** on your Google account
2. **Generate App Password**
   - Google Account → Security → App Passwords
   - Generate password for "Mail"
3. **Use App Password** in `GMAIL_APP_PASSWORD`

### SendGrid (Production)

1. **Create Account** at [sendgrid.com](https://sendgrid.com)
2. **Verify Domain** for better deliverability
3. **Create API Key** with mail send permissions
4. **Configure Environment**:
   ```bash
   EMAIL_SERVICE=sendgrid
   SENDGRID_API_KEY=your-api-key
   ```

### Custom SMTP

For other providers (Mailgun, AWS SES, etc.):
```bash
EMAIL_SERVICE=smtp
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=your-username
SMTP_PASS=your-password
```

## 🔒 Security Checklist

### Environment Variables
- [ ] Strong JWT_SECRET (32+ characters)
- [ ] Secure database credentials
- [ ] Production email credentials
- [ ] No sensitive data in code

### Database Security
- [ ] Database user with minimal permissions
- [ ] Network access restrictions
- [ ] Regular backups
- [ ] Connection encryption

### Application Security
- [ ] HTTPS enabled
- [ ] Secure cookies in production
- [ ] Rate limiting configured
- [ ] Input validation
- [ ] Error handling (no sensitive info in errors)

## 🔧 Production Optimizations

### Performance
```bash
# Next.js optimizations
NEXT_TELEMETRY_DISABLED=1

# Database optimizations
MONGODB_URI=mongodb+srv://...?retryWrites=true&w=majority&maxPoolSize=10

# Caching
REDIS_URL=redis://your-redis-instance
```

### Monitoring
```bash
# Error tracking
SENTRY_DSN=your-sentry-dsn

# Analytics
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
```

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version (18+)
   - Verify all dependencies installed
   - Check for TypeScript errors

2. **Database Connection**
   - Verify connection string
   - Check network access settings
   - Ensure database user permissions

3. **Email Not Sending**
   - Verify email service configuration
   - Check spam folders
   - Test with simple SMTP first

4. **Authentication Issues**
   - Verify JWT_SECRET is set
   - Check token expiration
   - Ensure HTTPS in production

### Debug Mode

Enable debug logging:
```bash
DEBUG=true
NODE_ENV=development
```

## 📊 Monitoring & Maintenance

### Health Checks
Create `/api/health` endpoint:
```javascript
export async function GET() {
  return Response.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString() 
  });
}
```

### Backup Strategy
1. **Database Backups**
   - MongoDB Atlas: Automatic backups
   - Self-hosted: Regular mongodump

2. **Code Backups**
   - Git repository
   - Multiple remotes

### Updates
1. **Dependencies**
   ```bash
   npm audit
   npm update
   ```

2. **Security Patches**
   - Monitor security advisories
   - Update regularly

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - run: npm test
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## 📞 Support

If you encounter issues during deployment:

1. **Check Documentation**: Review this guide and README
2. **GitHub Issues**: Create an issue with deployment details
3. **Community**: Join our Discord/Slack for help
4. **Professional Support**: Contact for enterprise support

---

**Happy Deploying! 🚀**
