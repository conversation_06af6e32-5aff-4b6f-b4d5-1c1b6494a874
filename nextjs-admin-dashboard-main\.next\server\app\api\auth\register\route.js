/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/register/route";
exports.ids = ["app/api/auth/register/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Bhushan_patil_OneDrive_Desktop_Book_my_Service_new_nextjs_admin_dashboard_main_src_app_api_auth_register_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/register/route.js */ \"(rsc)/./src/app/api/auth/register/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/register/route\",\n        pathname: \"/api/auth/register\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\api\\\\auth\\\\register\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Bhushan_patil_OneDrive_Desktop_Book_my_Service_new_nextjs_admin_dashboard_main_src_app_api_auth_register_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/register/route.js":
/*!********************************************!*\
  !*** ./src/app/api/auth/register/route.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.js\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.js\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-utils */ \"(rsc)/./src/lib/auth-utils.js\");\n\n\n\n\nasync function POST(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const { firstName, lastName, email, password, phoneNumber, role, // Business owner specific fields\n        ownerFirstName, ownerLastName, businessName, businessCategory, businessDescription, businessAddress, city, state, zipCode, country } = await request.json();\n        console.log('Registration request:', {\n            email,\n            role,\n            businessName: businessName || 'N/A',\n            firstName: firstName || ownerFirstName,\n            lastName: lastName || ownerLastName\n        });\n        // Check if user already exists\n        const existingUser = await _models_User__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findOne({\n            email\n        });\n        if (existingUser) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Email already exists'\n            }, {\n                status: 400\n            });\n        }\n        // Determine user type and validate required fields\n        const isBusinessOwner = role === 'business_owner';\n        const userFirstName = isBusinessOwner ? ownerFirstName : firstName;\n        const userLastName = isBusinessOwner ? ownerLastName : lastName;\n        // Validate required fields\n        if (!userFirstName || !email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        if (isBusinessOwner && !businessName) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Business name is required for business owners'\n            }, {\n                status: 400\n            });\n        }\n        // Generate OTP\n        const otp = (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_3__.generateOtp)();\n        // Send OTP email\n        await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_3__.sendOtpMail)(email, userFirstName, '', otp);\n        // Store user data and OTP in cookies (temporary storage)\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'OTP sent successfully'\n        });\n        // Set cookies with user data and OTP\n        const userData = isBusinessOwner ? {\n            personalInfo: {\n                ownerFirstName,\n                ownerLastName,\n                email,\n                password,\n                phoneNumber\n            },\n            businessInfo: {\n                businessName,\n                businessCategory,\n                businessDescription,\n                businessAddress,\n                city,\n                state,\n                zipCode,\n                country\n            },\n            role: 'business_owner'\n        } : {\n            firstName,\n            lastName,\n            email,\n            password,\n            phoneNumber,\n            role: 'user'\n        };\n        const cookieName = isBusinessOwner ? 'business_owner_data' : 'user_data';\n        response.cookies.set(cookieName, JSON.stringify(userData), {\n            httpOnly: true,\n            secure: \"development\" === 'production',\n            maxAge: 5 * 60 * 1000,\n            sameSite: 'strict'\n        });\n        response.cookies.set('otp', otp, {\n            httpOnly: true,\n            secure: \"development\" === 'production',\n            maxAge: 5 * 60 * 1000,\n            sameSite: 'strict'\n        });\n        return response;\n    } catch (error) {\n        console.error('Register Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to send OTP'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL3JlZ2lzdGVyL3JvdXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTJDO0FBQ0w7QUFDTDtBQUN5QztBQUVuRSxlQUFlTSxLQUFLQyxPQUFPO0lBQ2hDLElBQUk7UUFDRixNQUFNTix3REFBU0E7UUFFZixNQUFNLEVBQ0pPLFNBQVMsRUFDVEMsUUFBUSxFQUNSQyxLQUFLLEVBQ0xDLFFBQVEsRUFDUkMsV0FBVyxFQUNYQyxJQUFJLEVBQ0osaUNBQWlDO1FBQ2pDQyxjQUFjLEVBQ2RDLGFBQWEsRUFDYkMsWUFBWSxFQUNaQyxnQkFBZ0IsRUFDaEJDLG1CQUFtQixFQUNuQkMsZUFBZSxFQUNmQyxJQUFJLEVBQ0pDLEtBQUssRUFDTEMsT0FBTyxFQUNQQyxPQUFPLEVBQ1IsR0FBRyxNQUFNaEIsUUFBUWlCLElBQUk7UUFFdEJDLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUI7WUFDbkNoQjtZQUNBRztZQUNBRyxjQUFjQSxnQkFBZ0I7WUFDOUJSLFdBQVdBLGFBQWFNO1lBQ3hCTCxVQUFVQSxZQUFZTTtRQUN4QjtRQUVBLCtCQUErQjtRQUMvQixNQUFNWSxlQUFlLE1BQU16QixvREFBSUEsQ0FBQzBCLE9BQU8sQ0FBQztZQUFFbEI7UUFBTTtRQUNoRCxJQUFJaUIsY0FBYztZQUNoQixPQUFPM0IscURBQVlBLENBQUN3QixJQUFJLENBQUM7Z0JBQ3ZCSyxTQUFTO2dCQUNUQyxTQUFTO1lBQ1gsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ25CO1FBRUEsbURBQW1EO1FBQ25ELE1BQU1DLGtCQUFrQm5CLFNBQVM7UUFDakMsTUFBTW9CLGdCQUFnQkQsa0JBQWtCbEIsaUJBQWlCTjtRQUN6RCxNQUFNMEIsZUFBZUYsa0JBQWtCakIsZ0JBQWdCTjtRQUV2RCwyQkFBMkI7UUFDM0IsSUFBSSxDQUFDd0IsaUJBQWlCLENBQUN2QixTQUFTLENBQUNDLFVBQVU7WUFDekMsT0FBT1gscURBQVlBLENBQUN3QixJQUFJLENBQUM7Z0JBQ3ZCSyxTQUFTO2dCQUNUQyxTQUFTO1lBQ1gsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ25CO1FBRUEsSUFBSUMsbUJBQW1CLENBQUNoQixjQUFjO1lBQ3BDLE9BQU9oQixxREFBWUEsQ0FBQ3dCLElBQUksQ0FBQztnQkFDdkJLLFNBQVM7Z0JBQ1RDLFNBQVM7WUFDWCxHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDbkI7UUFFQSxlQUFlO1FBQ2YsTUFBTUksTUFBTWhDLDREQUFXQTtRQUV2QixpQkFBaUI7UUFDakIsTUFBTUUsNERBQVdBLENBQUNLLE9BQU91QixlQUFlLElBQUlFO1FBRTVDLHlEQUF5RDtRQUN6RCxNQUFNQyxXQUFXcEMscURBQVlBLENBQUN3QixJQUFJLENBQUM7WUFDakNLLFNBQVM7WUFDVEMsU0FBUztRQUNYO1FBRUEscUNBQXFDO1FBQ3JDLE1BQU1PLFdBQVdMLGtCQUFrQjtZQUNqQ00sY0FBYztnQkFDWnhCO2dCQUNBQztnQkFDQUw7Z0JBQ0FDO2dCQUNBQztZQUNGO1lBQ0EyQixjQUFjO2dCQUNadkI7Z0JBQ0FDO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO1lBQ0Y7WUFDQVYsTUFBTTtRQUNSLElBQUk7WUFDRkw7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUMsTUFBTTtRQUNSO1FBRUEsTUFBTTJCLGFBQWFSLGtCQUFrQix3QkFBd0I7UUFFN0RJLFNBQVNLLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDRixZQUFZRyxLQUFLQyxTQUFTLENBQUNQLFdBQVc7WUFDekRRLFVBQVU7WUFDVkMsUUFBUUMsa0JBQXlCO1lBQ2pDQyxRQUFRLElBQUksS0FBSztZQUNqQkMsVUFBVTtRQUNaO1FBRUFiLFNBQVNLLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLE9BQU9QLEtBQUs7WUFDL0JVLFVBQVU7WUFDVkMsUUFBUUMsa0JBQXlCO1lBQ2pDQyxRQUFRLElBQUksS0FBSztZQUNqQkMsVUFBVTtRQUNaO1FBRUEsT0FBT2I7SUFFVCxFQUFFLE9BQU9jLE9BQU87UUFDZHpCLFFBQVF5QixLQUFLLENBQUMsbUJBQW1CQTtRQUNqQyxPQUFPbEQscURBQVlBLENBQUN3QixJQUFJLENBQUM7WUFDdkJLLFNBQVM7WUFDVEMsU0FBUztRQUNYLEdBQUc7WUFBRUMsUUFBUTtRQUFJO0lBQ25CO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmh1c2hhbiBwYXRpbFxcT25lRHJpdmVcXERlc2t0b3BcXEJvb2sgbXkgU2VydmljZSBuZXdcXG5leHRqcy1hZG1pbi1kYXNoYm9hcmQtbWFpblxcc3JjXFxhcHBcXGFwaVxcYXV0aFxccmVnaXN0ZXJcXHJvdXRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCBjb25uZWN0REIgZnJvbSAnQC9saWIvbW9uZ29kYic7XG5pbXBvcnQgVXNlciBmcm9tICdAL21vZGVscy9Vc2VyJztcbmltcG9ydCB7IGdlbmVyYXRlT3RwLCBoYXNoUGFzc3dvcmQsIHNlbmRPdHBNYWlsIH0gZnJvbSAnQC9saWIvYXV0aC11dGlscyc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBhd2FpdCBjb25uZWN0REIoKTtcbiAgICBcbiAgICBjb25zdCB7XG4gICAgICBmaXJzdE5hbWUsXG4gICAgICBsYXN0TmFtZSxcbiAgICAgIGVtYWlsLFxuICAgICAgcGFzc3dvcmQsXG4gICAgICBwaG9uZU51bWJlcixcbiAgICAgIHJvbGUsXG4gICAgICAvLyBCdXNpbmVzcyBvd25lciBzcGVjaWZpYyBmaWVsZHNcbiAgICAgIG93bmVyRmlyc3ROYW1lLFxuICAgICAgb3duZXJMYXN0TmFtZSxcbiAgICAgIGJ1c2luZXNzTmFtZSxcbiAgICAgIGJ1c2luZXNzQ2F0ZWdvcnksXG4gICAgICBidXNpbmVzc0Rlc2NyaXB0aW9uLFxuICAgICAgYnVzaW5lc3NBZGRyZXNzLFxuICAgICAgY2l0eSxcbiAgICAgIHN0YXRlLFxuICAgICAgemlwQ29kZSxcbiAgICAgIGNvdW50cnlcbiAgICB9ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG5cbiAgICBjb25zb2xlLmxvZygnUmVnaXN0cmF0aW9uIHJlcXVlc3Q6Jywge1xuICAgICAgZW1haWwsXG4gICAgICByb2xlLFxuICAgICAgYnVzaW5lc3NOYW1lOiBidXNpbmVzc05hbWUgfHwgJ04vQScsXG4gICAgICBmaXJzdE5hbWU6IGZpcnN0TmFtZSB8fCBvd25lckZpcnN0TmFtZSxcbiAgICAgIGxhc3ROYW1lOiBsYXN0TmFtZSB8fCBvd25lckxhc3ROYW1lXG4gICAgfSk7XG5cbiAgICAvLyBDaGVjayBpZiB1c2VyIGFscmVhZHkgZXhpc3RzXG4gICAgY29uc3QgZXhpc3RpbmdVc2VyID0gYXdhaXQgVXNlci5maW5kT25lKHsgZW1haWwgfSk7XG4gICAgaWYgKGV4aXN0aW5nVXNlcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6ICdFbWFpbCBhbHJlYWR5IGV4aXN0cydcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gICAgfVxuXG4gICAgLy8gRGV0ZXJtaW5lIHVzZXIgdHlwZSBhbmQgdmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzXG4gICAgY29uc3QgaXNCdXNpbmVzc093bmVyID0gcm9sZSA9PT0gJ2J1c2luZXNzX293bmVyJztcbiAgICBjb25zdCB1c2VyRmlyc3ROYW1lID0gaXNCdXNpbmVzc093bmVyID8gb3duZXJGaXJzdE5hbWUgOiBmaXJzdE5hbWU7XG4gICAgY29uc3QgdXNlckxhc3ROYW1lID0gaXNCdXNpbmVzc093bmVyID8gb3duZXJMYXN0TmFtZSA6IGxhc3ROYW1lO1xuXG4gICAgLy8gVmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzXG4gICAgaWYgKCF1c2VyRmlyc3ROYW1lIHx8ICFlbWFpbCB8fCAhcGFzc3dvcmQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiAnTWlzc2luZyByZXF1aXJlZCBmaWVsZHMnXG4gICAgICB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgIH1cblxuICAgIGlmIChpc0J1c2luZXNzT3duZXIgJiYgIWJ1c2luZXNzTmFtZSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6ICdCdXNpbmVzcyBuYW1lIGlzIHJlcXVpcmVkIGZvciBidXNpbmVzcyBvd25lcnMnXG4gICAgICB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgIH1cblxuICAgIC8vIEdlbmVyYXRlIE9UUFxuICAgIGNvbnN0IG90cCA9IGdlbmVyYXRlT3RwKCk7XG5cbiAgICAvLyBTZW5kIE9UUCBlbWFpbFxuICAgIGF3YWl0IHNlbmRPdHBNYWlsKGVtYWlsLCB1c2VyRmlyc3ROYW1lLCAnJywgb3RwKTtcblxuICAgIC8vIFN0b3JlIHVzZXIgZGF0YSBhbmQgT1RQIGluIGNvb2tpZXMgKHRlbXBvcmFyeSBzdG9yYWdlKVxuICAgIGNvbnN0IHJlc3BvbnNlID0gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6ICdPVFAgc2VudCBzdWNjZXNzZnVsbHknXG4gICAgfSk7XG5cbiAgICAvLyBTZXQgY29va2llcyB3aXRoIHVzZXIgZGF0YSBhbmQgT1RQXG4gICAgY29uc3QgdXNlckRhdGEgPSBpc0J1c2luZXNzT3duZXIgPyB7XG4gICAgICBwZXJzb25hbEluZm86IHtcbiAgICAgICAgb3duZXJGaXJzdE5hbWUsXG4gICAgICAgIG93bmVyTGFzdE5hbWUsXG4gICAgICAgIGVtYWlsLFxuICAgICAgICBwYXNzd29yZCxcbiAgICAgICAgcGhvbmVOdW1iZXIsXG4gICAgICB9LFxuICAgICAgYnVzaW5lc3NJbmZvOiB7XG4gICAgICAgIGJ1c2luZXNzTmFtZSxcbiAgICAgICAgYnVzaW5lc3NDYXRlZ29yeSxcbiAgICAgICAgYnVzaW5lc3NEZXNjcmlwdGlvbixcbiAgICAgICAgYnVzaW5lc3NBZGRyZXNzLFxuICAgICAgICBjaXR5LFxuICAgICAgICBzdGF0ZSxcbiAgICAgICAgemlwQ29kZSxcbiAgICAgICAgY291bnRyeSxcbiAgICAgIH0sXG4gICAgICByb2xlOiAnYnVzaW5lc3Nfb3duZXInXG4gICAgfSA6IHtcbiAgICAgIGZpcnN0TmFtZSxcbiAgICAgIGxhc3ROYW1lLFxuICAgICAgZW1haWwsXG4gICAgICBwYXNzd29yZCxcbiAgICAgIHBob25lTnVtYmVyLFxuICAgICAgcm9sZTogJ3VzZXInXG4gICAgfTtcblxuICAgIGNvbnN0IGNvb2tpZU5hbWUgPSBpc0J1c2luZXNzT3duZXIgPyAnYnVzaW5lc3Nfb3duZXJfZGF0YScgOiAndXNlcl9kYXRhJztcblxuICAgIHJlc3BvbnNlLmNvb2tpZXMuc2V0KGNvb2tpZU5hbWUsIEpTT04uc3RyaW5naWZ5KHVzZXJEYXRhKSwge1xuICAgICAgaHR0cE9ubHk6IHRydWUsXG4gICAgICBzZWN1cmU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicsXG4gICAgICBtYXhBZ2U6IDUgKiA2MCAqIDEwMDAsIC8vIDUgbWludXRlc1xuICAgICAgc2FtZVNpdGU6ICdzdHJpY3QnXG4gICAgfSk7XG5cbiAgICByZXNwb25zZS5jb29raWVzLnNldCgnb3RwJywgb3RwLCB7XG4gICAgICBodHRwT25seTogdHJ1ZSxcbiAgICAgIHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJyxcbiAgICAgIG1heEFnZTogNSAqIDYwICogMTAwMCwgLy8gNSBtaW51dGVzXG4gICAgICBzYW1lU2l0ZTogJ3N0cmljdCdcbiAgICB9KTtcblxuICAgIHJldHVybiByZXNwb25zZTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1JlZ2lzdGVyIEVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBtZXNzYWdlOiAnRmFpbGVkIHRvIHNlbmQgT1RQJ1xuICAgIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJjb25uZWN0REIiLCJVc2VyIiwiZ2VuZXJhdGVPdHAiLCJoYXNoUGFzc3dvcmQiLCJzZW5kT3RwTWFpbCIsIlBPU1QiLCJyZXF1ZXN0IiwiZmlyc3ROYW1lIiwibGFzdE5hbWUiLCJlbWFpbCIsInBhc3N3b3JkIiwicGhvbmVOdW1iZXIiLCJyb2xlIiwib3duZXJGaXJzdE5hbWUiLCJvd25lckxhc3ROYW1lIiwiYnVzaW5lc3NOYW1lIiwiYnVzaW5lc3NDYXRlZ29yeSIsImJ1c2luZXNzRGVzY3JpcHRpb24iLCJidXNpbmVzc0FkZHJlc3MiLCJjaXR5Iiwic3RhdGUiLCJ6aXBDb2RlIiwiY291bnRyeSIsImpzb24iLCJjb25zb2xlIiwibG9nIiwiZXhpc3RpbmdVc2VyIiwiZmluZE9uZSIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwic3RhdHVzIiwiaXNCdXNpbmVzc093bmVyIiwidXNlckZpcnN0TmFtZSIsInVzZXJMYXN0TmFtZSIsIm90cCIsInJlc3BvbnNlIiwidXNlckRhdGEiLCJwZXJzb25hbEluZm8iLCJidXNpbmVzc0luZm8iLCJjb29raWVOYW1lIiwiY29va2llcyIsInNldCIsIkpTT04iLCJzdHJpbmdpZnkiLCJodHRwT25seSIsInNlY3VyZSIsInByb2Nlc3MiLCJtYXhBZ2UiLCJzYW1lU2l0ZSIsImVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/register/route.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-utils.js":
/*!*******************************!*\
  !*** ./src/lib/auth-utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RESPONSE_CODE: () => (/* binding */ RESPONSE_CODE),\n/* harmony export */   RESPONSE_FAILURE: () => (/* binding */ RESPONSE_FAILURE),\n/* harmony export */   RESPONSE_SUCCESS: () => (/* binding */ RESPONSE_SUCCESS),\n/* harmony export */   comparePassword: () => (/* binding */ comparePassword),\n/* harmony export */   generateOtp: () => (/* binding */ generateOtp),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   sendOtpMail: () => (/* binding */ sendOtpMail),\n/* harmony export */   sendResponse: () => (/* binding */ sendResponse),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n\n\n\n// Generate random OTP\nfunction generateOtp() {\n    return Math.floor(100000 + Math.random() * 900000).toString();\n}\n// Hash password\nasync function hashPassword(password) {\n    const saltRounds = 10;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, saltRounds);\n}\n// Compare password\nasync function comparePassword(password, hashedPassword) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\n// Sign JWT token\nfunction signToken(payload) {\n    const secret = process.env.JWT_SECRET || 'your-secret-key';\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n        expiresIn: '7d'\n    });\n}\n// Verify JWT token\nfunction verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET || 'your-secret-key';\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n    } catch (error) {\n        console.error('Token verification error:', error.message);\n        return null;\n    }\n}\n// Create email transporter\nfunction createTransporter() {\n    return nodemailer__WEBPACK_IMPORTED_MODULE_2__.createTransport({\n        service: 'gmail',\n        auth: {\n            user: process.env.EMAIL_USER,\n            pass: process.env.EMAIL_PASS\n        }\n    });\n}\n// Send OTP email\nasync function sendOtpMail(email, firstName, lastName, otp) {\n    try {\n        // For development, just log the OTP\n        if (true) {\n            console.log(`OTP for ${email}: ${otp}`);\n            return;\n        }\n        const transporter = createTransporter();\n        const mailOptions = {\n            from: process.env.EMAIL_USER,\n            to: email,\n            subject: 'BookMyService - Email Verification',\n            html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h2 style=\"color: #3C50E0;\">BookMyService Email Verification</h2>\n          <p>Hello ${firstName},</p>\n          <p>Thank you for registering with BookMyService. Please use the following OTP to verify your email address:</p>\n          <div style=\"background-color: #f8f9fa; padding: 20px; text-align: center; margin: 20px 0;\">\n            <h1 style=\"color: #3C50E0; font-size: 32px; margin: 0;\">${otp}</h1>\n          </div>\n          <p>This OTP will expire in 5 minutes.</p>\n          <p>If you didn't request this verification, please ignore this email.</p>\n          <p>Best regards,<br>BookMyService Team</p>\n        </div>\n      `\n        };\n        await transporter.sendMail(mailOptions);\n        console.log('OTP email sent successfully');\n    } catch (error) {\n        console.error('Error sending OTP email:', error);\n        throw new Error('Failed to send OTP email');\n    }\n}\n// Send response helper\nfunction sendResponse(res, data, message, success, statusCode) {\n    return res.status(statusCode).json({\n        success,\n        message,\n        data\n    });\n}\n// Response constants\nconst RESPONSE_SUCCESS = true;\nconst RESPONSE_FAILURE = false;\nconst RESPONSE_CODE = {\n    SUCCESS: 200,\n    CREATED: 201,\n    BAD_REQUEST: 400,\n    UNAUTHORISED: 401,\n    FORBIDDEN: 403,\n    NOT_FOUND: 404,\n    INTERNAL_SERVER_ERROR: 500\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-utils.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.js":
/*!****************************!*\
  !*** ./src/lib/mongodb.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.js\n");

/***/ }),

/***/ "(rsc)/./src/models/User.js":
/*!****************************!*\
  !*** ./src/models/User.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    firstName: {\n        type: String,\n        required: [\n            true,\n            'The first name is required.'\n        ],\n        trim: true\n    },\n    lastName: {\n        type: String,\n        default: null,\n        trim: true\n    },\n    email: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    password: {\n        type: String,\n        default: null\n    },\n    phoneNumber: {\n        type: String,\n        unique: true,\n        sparse: true,\n        trim: true\n    },\n    avatar: {\n        type: String\n    },\n    role: {\n        type: String,\n        default: 'user',\n        enum: [\n            'user',\n            'business_owner',\n            'admin',\n            'super_admin'\n        ]\n    },\n    isVerified: {\n        type: Boolean,\n        default: false\n    },\n    isEmailVerified: {\n        type: Boolean,\n        default: false\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    // Business owner specific fields\n    businessName: {\n        type: String,\n        required: function() {\n            return this.role === 'business_owner';\n        },\n        trim: true\n    },\n    businessDescription: {\n        type: String,\n        trim: true\n    },\n    businessAddress: {\n        type: String,\n        trim: true\n    },\n    businessPhone: {\n        type: String,\n        trim: true\n    },\n    businessCategory: {\n        type: String,\n        trim: true\n    },\n    city: {\n        type: String,\n        trim: true\n    },\n    state: {\n        type: String,\n        trim: true\n    },\n    zipCode: {\n        type: String,\n        trim: true\n    },\n    country: {\n        type: String,\n        trim: true\n    },\n    bookedServiceIds: [\n        {\n            type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n            ref: 'Service'\n        }\n    ],\n    serviceOtp: {\n        otp: {\n            type: Number\n        },\n        expiresAt: {\n            type: Date\n        }\n    },\n    // Social login fields\n    googleId: {\n        type: String,\n        unique: true,\n        sparse: true\n    },\n    facebookId: {\n        type: String,\n        unique: true,\n        sparse: true\n    },\n    authProvider: {\n        type: String,\n        enum: [\n            'local',\n            'google',\n            'facebook'\n        ],\n        default: 'local'\n    },\n    // OTP for verification\n    otp: String,\n    otpExpires: Date\n}, {\n    timestamps: true\n});\n// Index for better query performance\nUserSchema.index({\n    email: 1\n});\nUserSchema.index({\n    role: 1\n});\nUserSchema.index({\n    googleId: 1\n});\nUserSchema.index({\n    facebookId: 1\n});\n// Instance methods\nUserSchema.methods.generateAuthToken = function() {\n    const jwt = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n    return jwt.sign({\n        uId: this._id,\n        role: this.role\n    }, process.env.JWT_SECRET || 'your-secret-key', {\n        expiresIn: \"24h\"\n    });\n};\nUserSchema.methods.toSafeObject = function() {\n    const userObject = this.toObject();\n    delete userObject.password;\n    delete userObject.otp;\n    delete userObject.otpExpires;\n    delete userObject.serviceOtp;\n    return userObject;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('User', UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/nodemailer","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();