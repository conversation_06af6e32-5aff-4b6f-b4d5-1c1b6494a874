/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Cnextjs-toploader%5C%5Cdist%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Cflatpickr%5C%5Cdist%5C%5Cflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Cjsvectormap%5C%5Cdist%5C%5Cjsvectormap.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Capp%5C%5Cproviders.jsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Cheader%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Csidebar%5C%5CSidebarWrapper.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccss%5C%5Csatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccss%5C%5Cstyle.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Cnextjs-toploader%5C%5Cdist%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Cflatpickr%5C%5Cdist%5C%5Cflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Cjsvectormap%5C%5Cdist%5C%5Cjsvectormap.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Capp%5C%5Cproviders.jsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Cheader%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Csidebar%5C%5CSidebarWrapper.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccss%5C%5Csatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccss%5C%5Cstyle.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/nextjs-toploader/dist/index.js */ \"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-toastify/dist/ReactToastify.css */ \"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/flatpickr/dist/flatpickr.min.css */ \"(app-pages-browser)/./node_modules/flatpickr/dist/flatpickr.min.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/jsvectormap/dist/jsvectormap.css */ \"(app-pages-browser)/./node_modules/jsvectormap/dist/jsvectormap.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.jsx */ \"(app-pages-browser)/./src/app/providers.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layouts/header/index.tsx */ \"(app-pages-browser)/./src/components/Layouts/header/index.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layouts/sidebar/SidebarWrapper.jsx */ \"(app-pages-browser)/./src/components/Layouts/sidebar/SidebarWrapper.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/css/satoshi.css */ \"(app-pages-browser)/./src/css/satoshi.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/css/style.css */ \"(app-pages-browser)/./src/css/style.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Cnextjs-toploader%5C%5Cdist%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Cflatpickr%5C%5Cdist%5C%5Cflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Cnode_modules%5C%5Cjsvectormap%5C%5Cdist%5C%5Cjsvectormap.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Capp%5C%5Cproviders.jsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Cheader%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Csidebar%5C%5CSidebarWrapper.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccss%5C%5Csatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Ccss%5C%5Cstyle.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/css/style.css":
/*!***************************!*\
  !*** ./src/css/style.css ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"74b41b181221\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jc3Mvc3R5bGUuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxCaHVzaGFuIHBhdGlsXFxPbmVEcml2ZVxcRGVza3RvcFxcQm9vayBteSBTZXJ2aWNlIG5ld1xcbmV4dGpzLWFkbWluLWRhc2hib2FyZC1tYWluXFxzcmNcXGNzc1xcc3R5bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzRiNDFiMTgxMjIxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/css/style.css\n"));

/***/ })

});