import type { Metadata } from "next";
import "./globals.css";
import { Providers } from "./providers";

export const metadata: Metadata = {
  title: {
    template: "%s | BookMyService - Service Booking Platform",
    default: "BookMyService - Service Booking Platform",
  },
  description:
    "BookMyService is a comprehensive service booking platform that connects service providers with customers. Book services, manage bookings, and grow your business.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased">
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
