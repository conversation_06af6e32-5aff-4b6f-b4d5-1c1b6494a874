import jwt from 'jsonwebtoken';
import User from '@/models/User';
import connectDB from '@/lib/mongodb';

export async function verifyToken(token) {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    return decoded;
  } catch (error) {
    throw new Error('Invalid token');
  }
}

export async function authenticateUser(req) {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('No token provided');
    }

    const token = authHeader.substring(7);
    const decoded = await verifyToken(token);
    
    await connectDB();
    const user = await User.findById(decoded.userId).select('-otp -otpExpires -resetPasswordToken -resetPasswordExpires');
    
    if (!user) {
      throw new Error('User not found');
    }

    if (!user.isActive) {
      throw new Error('User account is deactivated');
    }

    return user;
  } catch (error) {
    throw error;
  }
}

export function requireAuth(handler) {
  return async (req, res) => {
    try {
      const user = await authenticateUser(req);
      req.user = user;
      return handler(req, res);
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: error.message || 'Authentication required'
      });
    }
  };
}

export function requireRole(roles) {
  return (handler) => {
    return requireAuth(async (req, res) => {
      const userRole = req.user.role;
      
      if (!roles.includes(userRole)) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
      }
      
      return handler(req, res);
    });
  };
}

export function requireBusinessOwner(handler) {
  return requireRole(['business_owner', 'admin'])(handler);
}

export function requireAdmin(handler) {
  return requireRole(['admin'])(handler);
}

// Middleware for optional authentication (doesn't fail if no token)
export function optionalAuth(handler) {
  return async (req, res) => {
    try {
      const user = await authenticateUser(req);
      req.user = user;
    } catch (error) {
      // Continue without user if authentication fails
      req.user = null;
    }
    return handler(req, res);
  };
}

export function generateToken(userId) {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRE || '7d' }
  );
}

export function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

export function isTokenExpired(token) {
  try {
    const decoded = jwt.decode(token);
    const currentTime = Date.now() / 1000;
    return decoded.exp < currentTime;
  } catch (error) {
    return true;
  }
}
