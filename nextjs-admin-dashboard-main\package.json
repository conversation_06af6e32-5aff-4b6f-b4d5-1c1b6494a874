{"name": "bookmyservice-platform", "version": "1.0.0", "private": true, "description": "A comprehensive service booking platform with role-based access control", "keywords": ["nextjs", "booking", "services", "admin-dashboard", "mongodb", "react"], "author": "BookMyService Team", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "apexcharts": "^4.5.0", "axios": "^1.10.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "flatpickr": "^4.6.13", "framer-motion": "^12.23.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jsvectormap": "^1.6.0", "lucide-react": "^0.475.0", "mongoose": "^8.16.1", "multer": "^1.4.5-lts.1", "next": "15.1.6", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "nodemailer": "^6.10.1", "react": "19.0.0", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.0.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "stripe": "^18.3.0", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@types/node": "^22", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.16", "typescript": "^5"}}