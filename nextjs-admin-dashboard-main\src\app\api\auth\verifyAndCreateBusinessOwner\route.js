import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import connectDB from '@/lib/mongodb';
import BusinessOwner from '@/models/BusinessOwner';
import { hashPassword, signToken } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();
    
    const { otp } = await request.json();
    const cookieStore = cookies();
    
    // Get stored OTP and business owner data from cookies
    const storedOtp = cookieStore.get('otp')?.value;
    const businessOwnerData = cookieStore.get('business_owner_data')?.value;

    if (!storedOtp || !businessOwnerData) {
      return NextResponse.json({
        success: false,
        message: 'Session expired. Please register again.'
      }, { status: 400 });
    }

    // Verify OTP
    if (otp !== storedOtp) {
      return NextResponse.json({
        success: false,
        message: 'Invalid OTP'
      }, { status: 400 });
    }

    // Parse business owner data
    const parsedData = JSON.parse(businessOwnerData);
    
    // Hash password
    const hashedPassword = await hashPassword(parsedData.personalInfo.password);

    // Create business owner
    const businessOwner = await BusinessOwner.create({
      ...parsedData.personalInfo,
      ...parsedData.businessInfo,
      password: hashedPassword,
      isEmailVerified: true,
      role: 'business_owner',
    });

    // Generate JWT token
    const payload = {
      businessOwner: {
        id: businessOwner._id,
        role: businessOwner.role,
        ownerFirstName: businessOwner.ownerFirstName,
        ownerLastName: businessOwner.ownerLastName,
        email: businessOwner.email,
      },
    };
    const token = signToken(payload);

    // Clear cookies
    const response = NextResponse.json({
      success: true,
      message: 'Business owner registered successfully',
      data: { businessOwner, token }
    });

    response.cookies.delete('business_owner_data');
    response.cookies.delete('otp');

    return response;

  } catch (error) {
    console.error('Business Owner Creation Error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to create business owner'
    }, { status: 500 });
  }
}
