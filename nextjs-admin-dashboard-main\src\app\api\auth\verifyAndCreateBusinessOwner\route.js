// import { NextResponse } from 'next/server';
// import { cookies } from 'next/headers';
// import connectDB from '@/lib/mongodb';
// import User from '@/models/User';
// import { hashPassword, signToken } from '@/lib/auth-utils';

// export async function POST(request) {
//   try {
//     await connectDB();
    
//     const { otp } = await request.json();
//     const cookieStore = await cookies();

//     // Get stored OTP and business owner data from cookies
//     const storedOtp = cookieStore.get('otp')?.value;
//     const businessOwnerData = cookieStore.get('business_owner_data')?.value;

//     if (!storedOtp || !businessOwnerData) {
//       return NextResponse.json({
//         success: false,
//         message: 'Session expired. Please register again.'
//       }, { status: 400 });
//     }

//     // Verify OTP
//     if (otp !== storedOtp) {
//       return NextResponse.json({
//         success: false,
//         message: 'Invalid OTP'
//       }, { status: 400 });
//     }

//     // Parse business owner data
//     const parsedData = JSON.parse(businessOwnerData);
    
//     // Hash password
//     const hashedPassword = await hashPassword(parsedData.personalInfo.password);

//     // Create business owner as a User with business_owner role
//     const businessOwner = await User.create({
//       firstName: parsedData.personalInfo.ownerFirstName,
//       lastName: parsedData.personalInfo.ownerLastName,
//       email: parsedData.personalInfo.email,
//       password: hashedPassword,
//       phoneNumber: parsedData.personalInfo.phoneNumber,
//       businessName: parsedData.businessInfo.businessName,
//       businessDescription: parsedData.businessInfo.businessDescription,
//       businessAddress: parsedData.businessInfo.businessAddress,
//       businessPhone: parsedData.personalInfo.phoneNumber,
//       businessCategory: parsedData.businessInfo.businessCategory,
//       isEmailVerified: true,
//       role: 'business_owner',
//       isActive: true,
//     });

//     // Generate JWT token
//     const payload = {
//       userId: businessOwner._id,
//       email: businessOwner.email,
//       role: businessOwner.role,
//     };
//     const token = signToken(payload);

//     // Clear cookies
//     const response = NextResponse.json({
//       success: true,
//       message: 'Business owner registered successfully',
//       user: {
//         id: businessOwner._id,
//         firstName: businessOwner.firstName,
//         lastName: businessOwner.lastName,
//         email: businessOwner.email,
//         businessName: businessOwner.businessName,
//         role: businessOwner.role,
//       },
//       token
//     });

//     response.cookies.delete('business_owner_data');
//     response.cookies.delete('otp');

//     return response;

//   } catch (error) {
//     console.error('Business Owner Creation Error:', error);
//     return NextResponse.json({
//       success: false,
//       message: 'Failed to create business owner'
//     }, { status: 500 });
//   }
// }
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import connectDB from '@/lib/mongodb';
import BusinessOwner from '@/models/BusinessOwner'; // Use BusinessOwner model
import { hashPassword, signToken } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();
    
    const { otp } = await request.json();
    const cookieStore = await cookies();

    const storedOtp = cookieStore.get('otp')?.value;
    const businessOwnerData = cookieStore.get('business_owner_data')?.value;

    console.log('Cookies:', { storedOtp, businessOwnerData }); // Debug cookies
    if (!storedOtp || !businessOwnerData) {
      return NextResponse.json({
        success: false,
        message: 'Session expired. Please register again.'
      }, { status: 400 });
    }

    if (otp !== storedOtp) {
      return NextResponse.json({
        success: false,
        message: 'Invalid OTP'
      }, { status: 400 });
    }

    const parsedData = JSON.parse(businessOwnerData);
    console.log('Parsed Data:', parsedData); // Debug parsed data

    const hashedPassword = await hashPassword(parsedData.personalInfo.password);

    const businessOwner = await BusinessOwner.create({
      ownerFirstName: parsedData.personalInfo.ownerFirstName,
      ownerLastName: parsedData.personalInfo.ownerLastName,
      email: parsedData.personalInfo.email,
      password: hashedPassword,
      phoneNumber: parsedData.personalInfo.phoneNumber,
      businessName: parsedData.businessInfo.businessName,
      businessDescription: parsedData.businessInfo.businessDescription,
      businessAddress: parsedData.businessInfo.businessAddress,
      city: parsedData.businessInfo.city,
      state: parsedData.businessInfo.state,
      zipCode: parsedData.businessInfo.zipCode,
      country: parsedData.businessInfo.country,
      isEmailVerified: true,
      role: 'business_owner',
      isActive: true,
    });

    const payload = {
      businessOwnerId: businessOwner._id,
      email: businessOwner.email,
      role: businessOwner.role,
    };
    const token = signToken(payload);

    const response = NextResponse.json({
      success: true,
      message: 'Business owner registered successfully',
      user: {
        id: businessOwner._id,
        firstName: businessOwner.ownerFirstName,
        lastName: businessOwner.ownerLastName,
        email: businessOwner.email,
        businessName: businessOwner.businessName,
        role: businessOwner.role,
      },
      token
    });

    response.cookies.delete('business_owner_data');
    response.cookies.delete('otp');

    return response;
  } catch (error) {
    console.error('Business Owner Creation Error:', error);
    if (error.code === 11000) {
      return NextResponse.json({
        success: false,
        message: 'Email or phone number already exists',
      }, { status: 400 });
    }
    if (error.name === 'ValidationError') {
      return NextResponse.json({
        success: false,
        message: `Validation failed: ${Object.values(error.errors).map(e => e.message).join(', ')}`,
      }, { status: 400 });
    }
    return NextResponse.json({
      success: false,
      message: 'Failed to create business owner'
    }, { status: 500 });
  }
}