/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/me/route";
exports.ids = ["app/api/auth/me/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Bhushan_patil_OneDrive_Desktop_Book_my_Service_new_nextjs_admin_dashboard_main_src_app_api_auth_me_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/me/route.js */ \"(rsc)/./src/app/api/auth/me/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/me/route\",\n        pathname: \"/api/auth/me\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/me/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\api\\\\auth\\\\me\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Bhushan_patil_OneDrive_Desktop_Book_my_Service_new_nextjs_admin_dashboard_main_src_app_api_auth_me_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/me/route.js":
/*!**************************************!*\
  !*** ./src/app/api/auth/me/route.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.js\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.js\");\n/* harmony import */ var _models_BusinessOwner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/models/BusinessOwner */ \"(rsc)/./src/models/BusinessOwner.js\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/auth-utils */ \"(rsc)/./src/lib/auth-utils.js\");\n\n\n\n\n\n\nasync function GET() {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const headersList = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n        const authorization = headersList.get('authorization');\n        if (!authorization || !authorization.startsWith('Bearer ')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'No token provided'\n            }, {\n                status: 401\n            });\n        }\n        const token = authorization.split(' ')[1];\n        try {\n            const decoded = (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_5__.verifyToken)(token);\n            console.log('Decoded token:', decoded);\n            let userData = null;\n            // Check if it's a regular user\n            if (decoded.user) {\n                userData = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findById(decoded.user.id).select('-password -otp -otpExpires');\n                if (userData) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        data: userData.toSafeObject()\n                    });\n                }\n            }\n            // Check if it's a business owner\n            if (decoded.businessOwner) {\n                userData = await _models_BusinessOwner__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findById(decoded.businessOwner.id).select('-password -otp -otpExpires');\n                if (userData) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        data: userData.toSafeObject()\n                    });\n                }\n            }\n            // If no user found with the token\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'User not found'\n            }, {\n                status: 404\n            });\n        } catch (tokenError) {\n            console.error('Token verification error:', tokenError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid token'\n            }, {\n                status: 401\n            });\n        }\n    } catch (error) {\n        console.error('Auth verification error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/me/route.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-utils.js":
/*!*******************************!*\
  !*** ./src/lib/auth-utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RESPONSE_CODE: () => (/* binding */ RESPONSE_CODE),\n/* harmony export */   RESPONSE_FAILURE: () => (/* binding */ RESPONSE_FAILURE),\n/* harmony export */   RESPONSE_SUCCESS: () => (/* binding */ RESPONSE_SUCCESS),\n/* harmony export */   comparePassword: () => (/* binding */ comparePassword),\n/* harmony export */   generateOtp: () => (/* binding */ generateOtp),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   sendOtpMail: () => (/* binding */ sendOtpMail),\n/* harmony export */   sendResponse: () => (/* binding */ sendResponse),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n\n\n\n// Generate random OTP\nfunction generateOtp() {\n    return Math.floor(100000 + Math.random() * 900000).toString();\n}\n// Hash password\nasync function hashPassword(password) {\n    const saltRounds = 10;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, saltRounds);\n}\n// Compare password\nasync function comparePassword(password, hashedPassword) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\n// Sign JWT token\nfunction signToken(payload) {\n    const secret = process.env.JWT_SECRET || 'your-secret-key';\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n        expiresIn: '7d'\n    });\n}\n// Verify JWT token\nfunction verifyToken(token) {\n    const secret = process.env.JWT_SECRET || 'your-secret-key';\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n}\n// Create email transporter\nfunction createTransporter() {\n    return nodemailer__WEBPACK_IMPORTED_MODULE_2__.createTransporter({\n        service: 'gmail',\n        auth: {\n            user: process.env.EMAIL_USER,\n            pass: process.env.EMAIL_PASS\n        }\n    });\n}\n// Send OTP email\nasync function sendOtpMail(email, firstName, lastName, otp) {\n    try {\n        // For development, just log the OTP\n        if (true) {\n            console.log(`OTP for ${email}: ${otp}`);\n            return;\n        }\n        const transporter = createTransporter();\n        const mailOptions = {\n            from: process.env.EMAIL_USER,\n            to: email,\n            subject: 'BookMyService - Email Verification',\n            html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h2 style=\"color: #3C50E0;\">BookMyService Email Verification</h2>\n          <p>Hello ${firstName},</p>\n          <p>Thank you for registering with BookMyService. Please use the following OTP to verify your email address:</p>\n          <div style=\"background-color: #f8f9fa; padding: 20px; text-align: center; margin: 20px 0;\">\n            <h1 style=\"color: #3C50E0; font-size: 32px; margin: 0;\">${otp}</h1>\n          </div>\n          <p>This OTP will expire in 5 minutes.</p>\n          <p>If you didn't request this verification, please ignore this email.</p>\n          <p>Best regards,<br>BookMyService Team</p>\n        </div>\n      `\n        };\n        await transporter.sendMail(mailOptions);\n        console.log('OTP email sent successfully');\n    } catch (error) {\n        console.error('Error sending OTP email:', error);\n        throw new Error('Failed to send OTP email');\n    }\n}\n// Send response helper\nfunction sendResponse(res, data, message, success, statusCode) {\n    return res.status(statusCode).json({\n        success,\n        message,\n        data\n    });\n}\n// Response constants\nconst RESPONSE_SUCCESS = true;\nconst RESPONSE_FAILURE = false;\nconst RESPONSE_CODE = {\n    SUCCESS: 200,\n    CREATED: 201,\n    BAD_REQUEST: 400,\n    UNAUTHORISED: 401,\n    FORBIDDEN: 403,\n    NOT_FOUND: 404,\n    INTERNAL_SERVER_ERROR: 500\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-utils.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.js":
/*!****************************!*\
  !*** ./src/lib/mongodb.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.js\n");

/***/ }),

/***/ "(rsc)/./src/models/BusinessOwner.js":
/*!*************************************!*\
  !*** ./src/models/BusinessOwner.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst BusinessSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    ownerFirstName: {\n        type: String,\n        required: [\n            true,\n            \"The first name is required.\"\n        ]\n    },\n    ownerLastName: {\n        type: String,\n        required: [\n            true,\n            \"The last name is required.\"\n        ]\n    },\n    email: {\n        type: String,\n        required: [\n            true,\n            \"Email is required.\"\n        ],\n        unique: true,\n        lowercase: true\n    },\n    password: {\n        type: String,\n        required: [\n            true,\n            \"Password is required.\"\n        ]\n    },\n    phoneNumber: {\n        type: String,\n        required: [\n            true,\n            \"Phone number is required.\"\n        ],\n        unique: true,\n        validate: {\n            validator: function(v) {\n                return /\\d{10}/.test(v);\n            },\n            message: (props)=>`${props.value} is not a valid phone number!`\n        }\n    },\n    businessName: {\n        type: String,\n        required: [\n            true,\n            \"Business name is required.\"\n        ]\n    },\n    businessCategory: {\n        type: String,\n        required: [\n            true,\n            \"Business category is required.\"\n        ]\n    },\n    businessDescription: {\n        type: String,\n        default: \"\"\n    },\n    businessAddress: {\n        type: String,\n        required: true\n    },\n    city: {\n        type: String,\n        required: true\n    },\n    state: {\n        type: String,\n        required: true\n    },\n    zipCode: {\n        type: String,\n        required: true\n    },\n    country: {\n        type: String,\n        required: true\n    },\n    businessWebsite: {\n        type: String,\n        default: null\n    },\n    businessLogo: {\n        type: String,\n        default: \"https://example.com/default-business-logo.png\"\n    },\n    role: {\n        type: String,\n        default: \"business_owner\"\n    },\n    servicesOffered: [\n        {\n            type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n            ref: \"Service\"\n        }\n    ],\n    workingHours: {\n        type: String,\n        required: false\n    },\n    businessRegistrationNumber: {\n        type: String,\n        default: null\n    },\n    taxId: {\n        type: String,\n        default: null\n    },\n    businessLicense: {\n        type: String,\n        default: null\n    },\n    idProof: {\n        type: String,\n        default: null\n    },\n    paymentMethod: {\n        type: String,\n        required: false,\n        enum: [\n            \"Bank Transfer\",\n            \"PayPal\",\n            \"Stripe\"\n        ]\n    },\n    payoutDetails: {\n        type: String,\n        required: false\n    },\n    isVerified: {\n        type: Boolean,\n        default: false\n    },\n    isEmailVerified: {\n        type: Boolean,\n        default: false\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    termsAccepted: {\n        type: Boolean,\n        required: false\n    },\n    // Social login fields\n    googleId: {\n        type: String,\n        unique: true,\n        sparse: true\n    },\n    facebookId: {\n        type: String,\n        unique: true,\n        sparse: true\n    },\n    authProvider: {\n        type: String,\n        enum: [\n            'local',\n            'google',\n            'facebook'\n        ],\n        default: 'local'\n    },\n    // OTP for verification\n    otp: String,\n    otpExpires: Date\n}, {\n    timestamps: true\n});\n// Indexes\nBusinessSchema.index({\n    email: 1\n});\nBusinessSchema.index({\n    businessCategory: 1\n});\nBusinessSchema.index({\n    city: 1\n});\nBusinessSchema.index({\n    state: 1\n});\nBusinessSchema.index({\n    googleId: 1\n});\nBusinessSchema.index({\n    facebookId: 1\n});\n// Instance methods\nBusinessSchema.methods.generateAuthToken = function() {\n    const jwt = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n    return jwt.sign({\n        businessOwnerId: this._id,\n        role: this.role\n    }, process.env.JWT_SECRET || 'your-secret-key', {\n        expiresIn: \"24h\"\n    });\n};\nBusinessSchema.methods.toSafeObject = function() {\n    const businessOwnerObject = this.toObject();\n    delete businessOwnerObject.password;\n    delete businessOwnerObject.otp;\n    delete businessOwnerObject.otpExpires;\n    return businessOwnerObject;\n};\n// Virtual for full name\nBusinessSchema.virtual('ownerFullName').get(function() {\n    return `${this.ownerFirstName} ${this.ownerLastName}`;\n});\n// Virtual for full address\nBusinessSchema.virtual('fullAddress').get(function() {\n    return `${this.businessAddress}, ${this.city}, ${this.state} ${this.zipCode}, ${this.country}`;\n});\n// Ensure virtual fields are serialized\nBusinessSchema.set('toJSON', {\n    virtuals: true\n});\nBusinessSchema.set('toObject', {\n    virtuals: true\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).BusinessOwner || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"BusinessOwner\", BusinessSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/BusinessOwner.js\n");

/***/ }),

/***/ "(rsc)/./src/models/User.js":
/*!****************************!*\
  !*** ./src/models/User.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    firstName: {\n        type: String,\n        required: [\n            true,\n            'The first name is required.'\n        ],\n        trim: true\n    },\n    lastName: {\n        type: String,\n        default: null,\n        trim: true\n    },\n    email: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    password: {\n        type: String,\n        default: null\n    },\n    phoneNumber: {\n        type: String,\n        unique: true,\n        sparse: true,\n        trim: true\n    },\n    avatar: {\n        type: String\n    },\n    role: {\n        type: String,\n        default: 'user',\n        enum: [\n            'user',\n            'business_owner',\n            'admin'\n        ]\n    },\n    isVerified: {\n        type: Boolean,\n        default: false\n    },\n    isEmailVerified: {\n        type: Boolean,\n        default: false\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    bookedServiceIds: [\n        {\n            type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n            ref: 'Service'\n        }\n    ],\n    serviceOtp: {\n        otp: {\n            type: Number\n        },\n        expiresAt: {\n            type: Date\n        }\n    },\n    // Social login fields\n    googleId: {\n        type: String,\n        unique: true,\n        sparse: true\n    },\n    facebookId: {\n        type: String,\n        unique: true,\n        sparse: true\n    },\n    authProvider: {\n        type: String,\n        enum: [\n            'local',\n            'google',\n            'facebook'\n        ],\n        default: 'local'\n    },\n    // OTP for verification\n    otp: String,\n    otpExpires: Date\n}, {\n    timestamps: true\n});\n// Index for better query performance\nUserSchema.index({\n    email: 1\n});\nUserSchema.index({\n    role: 1\n});\nUserSchema.index({\n    googleId: 1\n});\nUserSchema.index({\n    facebookId: 1\n});\n// Instance methods\nUserSchema.methods.generateAuthToken = function() {\n    const jwt = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n    return jwt.sign({\n        uId: this._id,\n        role: this.role\n    }, process.env.JWT_SECRET || 'your-secret-key', {\n        expiresIn: \"24h\"\n    });\n};\nUserSchema.methods.toSafeObject = function() {\n    const userObject = this.toObject();\n    delete userObject.password;\n    delete userObject.otp;\n    delete userObject.otpExpires;\n    delete userObject.serviceOtp;\n    return userObject;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('User', UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/nodemailer","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();