# BookMyService Environment Variables
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB Connection String
# For local MongoDB: mongodb://localhost:27017/bookmyservice
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/bookmyservice
MONGODB_URI=mongodb://localhost:27017/bookmyservice

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# JWT Secret Key (IMPORTANT: Change this to a secure random string in production)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# JWT Token Expiration (default: 7d)
JWT_EXPIRES_IN=7d

# NextAuth Configuration (if using NextAuth)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# Email Service Provider (gmail, sendgrid, or smtp)
EMAIL_SERVICE=gmail

# Gmail Configuration (if using Gmail)
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your-gmail-app-password

# SendGrid Configuration (if using SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key

# Generic SMTP Configuration (if using other providers)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password

# Email Sender Information
FROM_NAME=BookMyService
FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Application Environment
NODE_ENV=development

# Application URL
APP_URL=http://localhost:3000

# Application Name
APP_NAME=BookMyService

# =============================================================================
# PAYMENT INTEGRATION (Optional)
# =============================================================================

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# =============================================================================
# FILE UPLOAD & STORAGE (Optional)
# =============================================================================

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# =============================================================================
# EXTERNAL SERVICES (Optional)
# =============================================================================

# Google Maps API (for location services)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Twilio (for SMS notifications)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# =============================================================================
# ANALYTICS & MONITORING (Optional)
# =============================================================================

# Google Analytics
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID

# Sentry (Error Monitoring)
SENTRY_DSN=your_sentry_dsn

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable debug logging in development
DEBUG=true

# Disable email sending in development (emails will be logged instead)
DISABLE_EMAIL_SENDING=false

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# Set to true in production for enhanced security
SECURE_COOKIES=false

# Set to true to enforce HTTPS
FORCE_HTTPS=false

# =============================================================================
# NOTES
# =============================================================================

# 1. Never commit .env.local to version control
# 2. Use strong, unique passwords and secrets
# 3. For Gmail, use App Passwords instead of your regular password
# 4. For production, use environment-specific values
# 5. Regularly rotate secrets and API keys
# 6. Use a password manager to generate secure secrets

# =============================================================================
# QUICK SETUP GUIDE
# =============================================================================

# 1. Copy this file to .env.local
# 2. Fill in your MongoDB connection string
# 3. Generate a secure JWT_SECRET (you can use: openssl rand -base64 32)
# 4. Configure your email service (Gmail is easiest for development)
# 5. Start the development server: npm run dev
# 6. Visit http://localhost:3000/api/test-users to create sample users
