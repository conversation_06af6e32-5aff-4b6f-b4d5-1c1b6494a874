import { NextResponse } from 'next/server';
import { authenticateUser } from '@/middleware/auth';

export async function GET(request) {
  try {
    const user = await authenticateUser(request);
    
    return NextResponse.json({
      success: true,
      message: 'User data retrieved successfully',
      data: user.toSafeObject()
    });
    
  } catch (error) {
    console.error('Get user error:', error);
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Authentication failed'
    }, { status: 401 });
  }
}

export async function POST() {
  return NextResponse.json({
    success: false,
    message: 'Method not allowed'
  }, { status: 405 });
}
