import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import BusinessOwner from '@/models/BusinessOwner';
import { verifyToken } from '@/lib/auth-utils';

export async function GET() {
  try {
    await connectDB();

    const headersList = await headers();
    const authorization = headersList.get('authorization');

    if (!authorization || !authorization.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        message: 'No token provided'
      }, { status: 401 });
    }

    const token = authorization.split(' ')[1];

    try {
      const decoded = verifyToken(token);
      console.log('Decoded token:', decoded);

      let userData = null;

      // Check if it's a regular user
      if (decoded.user) {
        userData = await User.findById(decoded.user.id).select('-password -otp -otpExpires');
        if (userData) {
          return NextResponse.json({
            success: true,
            data: userData.toSafeObject()
          });
        }
      }

      // Check if it's a business owner
      if (decoded.businessOwner) {
        userData = await BusinessOwner.findById(decoded.businessOwner.id).select('-password -otp -otpExpires');
        if (userData) {
          return NextResponse.json({
            success: true,
            data: userData.toSafeObject()
          });
        }
      }

      // If no user found with the token
      return NextResponse.json({
        success: false,
        message: 'User not found'
      }, { status: 404 });

    } catch (tokenError) {
      console.error('Token verification error:', tokenError);
      return NextResponse.json({
        success: false,
        message: 'Invalid token'
      }, { status: 401 });
    }

  } catch (error) {
    console.error('Auth verification error:', error);
    return NextResponse.json({
      success: false,
      message: 'Server error'
    }, { status: 500 });
  }
}
