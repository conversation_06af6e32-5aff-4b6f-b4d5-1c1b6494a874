import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Booking from '@/models/Booking';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';

export async function GET(request) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'business_owner') {
      return apiResponse.error('Unauthorized access', 403);
    }
    
    const businessOwnerId = decoded.userId;
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit')) || 5;
    
    // Get recent bookings for this business owner
    const recentBookings = await Booking.find({ 
      businessOwner: businessOwnerId 
    })
    .populate('user', 'firstName lastName email')
    .populate('service', 'title price')
    .sort({ createdAt: -1 })
    .limit(limit);
    
    // Format the data for the dashboard
    const formattedBookings = recentBookings.map(booking => ({
      id: booking._id,
      service: booking.service?.title || 'Unknown Service',
      customer: `${booking.user?.firstName || ''} ${booking.user?.lastName || ''}`.trim() || 'Unknown Customer',
      customerEmail: booking.user?.email || '',
      date: booking.preferredDate,
      createdAt: booking.createdAt,
      status: booking.status,
      amount: booking.service?.price || 0,
      message: booking.message,
      preferredTime: booking.preferredTime
    }));
    
    return apiResponse.success(formattedBookings, 'Recent bookings fetched successfully');
    
  } catch (error) {
    console.error('Error fetching recent bookings:', error);
    return apiResponse.error('Failed to fetch recent bookings', 500);
  }
}
