// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    interface SubscriptionCreateParams {
      /**
       * The identifier of the customer to subscribe.
       */
      customer: string;

      /**
       * A list of prices and quantities that will generate invoice items appended to the next invoice for this subscription. You may pass up to 20 items.
       */
      add_invoice_items?: Array<SubscriptionCreateParams.AddInvoiceItem>;

      /**
       * A non-negative decimal between 0 and 100, with at most two decimal places. This represents the percentage of the subscription invoice total that will be transferred to the application owner's Stripe account. The request must be made by a platform account on a connected account in order to set an application fee percentage. For more information, see the application fees [documentation](https://stripe.com/docs/connect/subscriptions#collecting-fees-on-subscriptions).
       */
      application_fee_percent?: Stripe.Emptyable<number>;

      /**
       * Automatic tax settings for this subscription.
       */
      automatic_tax?: SubscriptionCreateParams.AutomaticTax;

      /**
       * A past timestamp to backdate the subscription's start date to. If set, the first invoice will contain line items for the timespan between the start date and the current time. Can be combined with trials and the billing cycle anchor.
       */
      backdate_start_date?: number;

      /**
       * A future timestamp in UTC format to anchor the subscription's [billing cycle](https://stripe.com/docs/subscriptions/billing-cycle). The anchor is the reference point that aligns future billing cycle dates. It sets the day of week for `week` intervals, the day of month for `month` and `year` intervals, and the month of year for `year` intervals.
       */
      billing_cycle_anchor?: number;

      /**
       * Mutually exclusive with billing_cycle_anchor and only valid with monthly and yearly price intervals. When provided, the billing_cycle_anchor is set to the next occurence of the day_of_month at the hour, minute, and second UTC.
       */
      billing_cycle_anchor_config?: SubscriptionCreateParams.BillingCycleAnchorConfig;

      /**
       * Controls how prorations and invoices for subscriptions are calculated and orchestrated.
       */
      billing_mode?: SubscriptionCreateParams.BillingMode;

      /**
       * Define thresholds at which an invoice will be sent, and the subscription advanced to a new billing period. When updating, pass an empty string to remove previously-defined thresholds.
       */
      billing_thresholds?: Stripe.Emptyable<
        SubscriptionCreateParams.BillingThresholds
      >;

      /**
       * A timestamp at which the subscription should cancel. If set to a date before the current period ends, this will cause a proration if prorations have been enabled using `proration_behavior`. If set during a future period, this will always cause a proration for that period.
       */
      cancel_at?: number;

      /**
       * Indicate whether this subscription should cancel at the end of the current period (`current_period_end`). Defaults to `false`.
       */
      cancel_at_period_end?: boolean;

      /**
       * Either `charge_automatically`, or `send_invoice`. When charging automatically, Stripe will attempt to pay this subscription at the end of the cycle using the default source attached to the customer. When sending an invoice, Stripe will email your customer an invoice with payment instructions and mark the subscription as `active`. Defaults to `charge_automatically`.
       */
      collection_method?: SubscriptionCreateParams.CollectionMethod;

      /**
       * Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).
       */
      currency?: string;

      /**
       * Number of days a customer has to pay invoices generated by this subscription. Valid only for subscriptions where `collection_method` is set to `send_invoice`.
       */
      days_until_due?: number;

      /**
       * ID of the default payment method for the subscription. It must belong to the customer associated with the subscription. This takes precedence over `default_source`. If neither are set, invoices will use the customer's [invoice_settings.default_payment_method](https://stripe.com/docs/api/customers/object#customer_object-invoice_settings-default_payment_method) or [default_source](https://stripe.com/docs/api/customers/object#customer_object-default_source).
       */
      default_payment_method?: string;

      /**
       * ID of the default payment source for the subscription. It must belong to the customer associated with the subscription and be in a chargeable state. If `default_payment_method` is also set, `default_payment_method` will take precedence. If neither are set, invoices will use the customer's [invoice_settings.default_payment_method](https://stripe.com/docs/api/customers/object#customer_object-invoice_settings-default_payment_method) or [default_source](https://stripe.com/docs/api/customers/object#customer_object-default_source).
       */
      default_source?: string;

      /**
       * The tax rates that will apply to any subscription item that does not have `tax_rates` set. Invoices created will have their `default_tax_rates` populated from the subscription.
       */
      default_tax_rates?: Stripe.Emptyable<Array<string>>;

      /**
       * The subscription's description, meant to be displayable to the customer. Use this field to optionally store an explanation of the subscription for rendering in Stripe surfaces and certain local payment methods UIs.
       */
      description?: string;

      /**
       * The coupons to redeem into discounts for the subscription. If not specified or empty, inherits the discount from the subscription's customer.
       */
      discounts?: Stripe.Emptyable<Array<SubscriptionCreateParams.Discount>>;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * All invoices will be billed using the specified settings.
       */
      invoice_settings?: SubscriptionCreateParams.InvoiceSettings;

      /**
       * A list of up to 20 subscription items, each with an attached price.
       */
      items?: Array<SubscriptionCreateParams.Item>;

      /**
       * Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Individual keys can be unset by posting an empty value to them. All keys can be unset by posting an empty value to `metadata`.
       */
      metadata?: Stripe.Emptyable<Stripe.MetadataParam>;

      /**
       * Indicates if a customer is on or off-session while an invoice payment is attempted. Defaults to `false` (on-session).
       */
      off_session?: boolean;

      /**
       * The account on behalf of which to charge, for each of the subscription's invoices.
       */
      on_behalf_of?: Stripe.Emptyable<string>;

      /**
       * Only applies to subscriptions with `collection_method=charge_automatically`.
       *
       * Use `allow_incomplete` to create Subscriptions with `status=incomplete` if the first invoice can't be paid. Creating Subscriptions with this status allows you to manage scenarios where additional customer actions are needed to pay a subscription's invoice. For example, SCA regulation may require 3DS authentication to complete payment. See the [SCA Migration Guide](https://stripe.com/docs/billing/migration/strong-customer-authentication) for Billing to learn more. This is the default behavior.
       *
       * Use `default_incomplete` to create Subscriptions with `status=incomplete` when the first invoice requires payment, otherwise start as active. Subscriptions transition to `status=active` when successfully confirming the PaymentIntent on the first invoice. This allows simpler management of scenarios where additional customer actions are needed to pay a subscription's invoice, such as failed payments, [SCA regulation](https://stripe.com/docs/billing/migration/strong-customer-authentication), or collecting a mandate for a bank debit payment method. If the PaymentIntent is not confirmed within 23 hours Subscriptions transition to `status=incomplete_expired`, which is a terminal state.
       *
       * Use `error_if_incomplete` if you want Stripe to return an HTTP 402 status code if a subscription's first invoice can't be paid. For example, if a payment method requires 3DS authentication due to SCA regulation and further customer action is needed, this parameter doesn't create a Subscription and returns an error instead. This was the default behavior for API versions prior to 2019-03-14. See the [changelog](https://stripe.com/docs/upgrades#2019-03-14) to learn more.
       *
       * `pending_if_incomplete` is only used with updates and cannot be passed when creating a Subscription.
       *
       * Subscriptions with `collection_method=send_invoice` are automatically activated regardless of the first Invoice status.
       */
      payment_behavior?: SubscriptionCreateParams.PaymentBehavior;

      /**
       * Payment settings to pass to invoices created by the subscription.
       */
      payment_settings?: SubscriptionCreateParams.PaymentSettings;

      /**
       * Specifies an interval for how often to bill for any pending invoice items. It is analogous to calling [Create an invoice](https://stripe.com/docs/api#create_invoice) for the given subscription at the specified interval.
       */
      pending_invoice_item_interval?: Stripe.Emptyable<
        SubscriptionCreateParams.PendingInvoiceItemInterval
      >;

      /**
       * Determines how to handle [prorations](https://stripe.com/docs/billing/subscriptions/prorations) resulting from the `billing_cycle_anchor`. If no value is passed, the default is `create_prorations`.
       */
      proration_behavior?: SubscriptionCreateParams.ProrationBehavior;

      /**
       * If specified, the funds from the subscription's invoices will be transferred to the destination and the ID of the resulting transfers will be found on the resulting charges.
       */
      transfer_data?: SubscriptionCreateParams.TransferData;

      /**
       * Unix timestamp representing the end of the trial period the customer will get before being charged for the first time. If set, trial_end will override the default trial period of the plan the customer is being subscribed to. The special value `now` can be provided to end the customer's trial immediately. Can be at most two years from `billing_cycle_anchor`. See [Using trial periods on subscriptions](https://stripe.com/docs/billing/subscriptions/trials) to learn more.
       */
      trial_end?: 'now' | number;

      /**
       * Indicates if a plan's `trial_period_days` should be applied to the subscription. Setting `trial_end` per subscription is preferred, and this defaults to `false`. Setting this flag to `true` together with `trial_end` is not allowed. See [Using trial periods on subscriptions](https://stripe.com/docs/billing/subscriptions/trials) to learn more.
       */
      trial_from_plan?: boolean;

      /**
       * Integer representing the number of trial period days before the customer is charged for the first time. This will always overwrite any trials that might apply via a subscribed plan. See [Using trial periods on subscriptions](https://stripe.com/docs/billing/subscriptions/trials) to learn more.
       */
      trial_period_days?: number;

      /**
       * Settings related to subscription trials.
       */
      trial_settings?: SubscriptionCreateParams.TrialSettings;
    }

    namespace SubscriptionCreateParams {
      interface AddInvoiceItem {
        /**
         * The coupons to redeem into discounts for the item.
         */
        discounts?: Array<AddInvoiceItem.Discount>;

        /**
         * The ID of the price object. One of `price` or `price_data` is required.
         */
        price?: string;

        /**
         * Data used to generate a new [Price](https://stripe.com/docs/api/prices) object inline. One of `price` or `price_data` is required.
         */
        price_data?: AddInvoiceItem.PriceData;

        /**
         * Quantity for this item. Defaults to 1.
         */
        quantity?: number;

        /**
         * The tax rates which apply to the item. When set, the `default_tax_rates` do not apply to this item.
         */
        tax_rates?: Stripe.Emptyable<Array<string>>;
      }

      namespace AddInvoiceItem {
        interface Discount {
          /**
           * ID of the coupon to create a new discount for.
           */
          coupon?: string;

          /**
           * ID of an existing discount on the object (or one of its ancestors) to reuse.
           */
          discount?: string;

          /**
           * ID of the promotion code to create a new discount for.
           */
          promotion_code?: string;
        }

        interface PriceData {
          /**
           * Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).
           */
          currency: string;

          /**
           * The ID of the [Product](https://docs.stripe.com/api/products) that this [Price](https://docs.stripe.com/api/prices) will belong to.
           */
          product: string;

          /**
           * Only required if a [default tax behavior](https://stripe.com/docs/tax/products-prices-tax-categories-tax-behavior#setting-a-default-tax-behavior-(recommended)) was not provided in the Stripe Tax settings. Specifies whether the price is considered inclusive of taxes or exclusive of taxes. One of `inclusive`, `exclusive`, or `unspecified`. Once specified as either `inclusive` or `exclusive`, it cannot be changed.
           */
          tax_behavior?: PriceData.TaxBehavior;

          /**
           * A positive integer in cents (or local equivalent) (or 0 for a free price) representing how much to charge or a negative integer representing the amount to credit to the customer.
           */
          unit_amount?: number;

          /**
           * Same as `unit_amount`, but accepts a decimal value in cents (or local equivalent) with at most 12 decimal places. Only one of `unit_amount` and `unit_amount_decimal` can be set.
           */
          unit_amount_decimal?: string;
        }

        namespace PriceData {
          type TaxBehavior = 'exclusive' | 'inclusive' | 'unspecified';
        }
      }

      interface AutomaticTax {
        /**
         * Enabled automatic tax calculation which will automatically compute tax rates on all invoices generated by the subscription.
         */
        enabled: boolean;

        /**
         * The account that's liable for tax. If set, the business address and tax registrations required to perform the tax calculation are loaded from this account. The tax transaction is returned in the report of the connected account.
         */
        liability?: AutomaticTax.Liability;
      }

      namespace AutomaticTax {
        interface Liability {
          /**
           * The connected account being referenced when `type` is `account`.
           */
          account?: string;

          /**
           * Type of the account referenced in the request.
           */
          type: Liability.Type;
        }

        namespace Liability {
          type Type = 'account' | 'self';
        }
      }

      interface BillingCycleAnchorConfig {
        /**
         * The day of the month the billing_cycle_anchor should be. Ranges from 1 to 31.
         */
        day_of_month: number;

        /**
         * The hour of the day the billing_cycle_anchor should be. Ranges from 0 to 23.
         */
        hour?: number;

        /**
         * The minute of the hour the billing_cycle_anchor should be. Ranges from 0 to 59.
         */
        minute?: number;

        /**
         * The month to start full cycle billing periods. Ranges from 1 to 12.
         */
        month?: number;

        /**
         * The second of the minute the billing_cycle_anchor should be. Ranges from 0 to 59.
         */
        second?: number;
      }

      interface BillingMode {
        type: BillingMode.Type;
      }

      namespace BillingMode {
        type Type = 'classic' | 'flexible';
      }

      interface BillingThresholds {
        /**
         * Monetary threshold that triggers the subscription to advance to a new billing period
         */
        amount_gte?: number;

        /**
         * Indicates if the `billing_cycle_anchor` should be reset when a threshold is reached. If true, `billing_cycle_anchor` will be updated to the date/time the threshold was last reached; otherwise, the value will remain unchanged.
         */
        reset_billing_cycle_anchor?: boolean;
      }

      type CollectionMethod = 'charge_automatically' | 'send_invoice';

      interface Discount {
        /**
         * ID of the coupon to create a new discount for.
         */
        coupon?: string;

        /**
         * ID of an existing discount on the object (or one of its ancestors) to reuse.
         */
        discount?: string;

        /**
         * ID of the promotion code to create a new discount for.
         */
        promotion_code?: string;
      }

      interface InvoiceSettings {
        /**
         * The account tax IDs associated with the subscription. Will be set on invoices generated by the subscription.
         */
        account_tax_ids?: Stripe.Emptyable<Array<string>>;

        /**
         * The connected account that issues the invoice. The invoice is presented with the branding and support information of the specified account.
         */
        issuer?: InvoiceSettings.Issuer;
      }

      namespace InvoiceSettings {
        interface Issuer {
          /**
           * The connected account being referenced when `type` is `account`.
           */
          account?: string;

          /**
           * Type of the account referenced in the request.
           */
          type: Issuer.Type;
        }

        namespace Issuer {
          type Type = 'account' | 'self';
        }
      }

      interface Item {
        /**
         * Define thresholds at which an invoice will be sent, and the subscription advanced to a new billing period. Pass an empty string to remove previously-defined thresholds.
         */
        billing_thresholds?: Stripe.Emptyable<Item.BillingThresholds>;

        /**
         * The coupons to redeem into discounts for the subscription item.
         */
        discounts?: Stripe.Emptyable<Array<Item.Discount>>;

        /**
         * Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Individual keys can be unset by posting an empty value to them. All keys can be unset by posting an empty value to `metadata`.
         */
        metadata?: Stripe.MetadataParam;

        /**
         * Plan ID for this item, as a string.
         */
        plan?: string;

        /**
         * The ID of the price object.
         */
        price?: string;

        /**
         * Data used to generate a new [Price](https://stripe.com/docs/api/prices) object inline.
         */
        price_data?: Item.PriceData;

        /**
         * Quantity for this item.
         */
        quantity?: number;

        /**
         * A list of [Tax Rate](https://stripe.com/docs/api/tax_rates) ids. These Tax Rates will override the [`default_tax_rates`](https://stripe.com/docs/api/subscriptions/create#create_subscription-default_tax_rates) on the Subscription. When updating, pass an empty string to remove previously-defined tax rates.
         */
        tax_rates?: Stripe.Emptyable<Array<string>>;
      }

      namespace Item {
        interface BillingThresholds {
          /**
           * Number of units that meets the billing threshold to advance the subscription to a new billing period (e.g., it takes 10 $5 units to meet a $50 [monetary threshold](https://stripe.com/docs/api/subscriptions/update#update_subscription-billing_thresholds-amount_gte))
           */
          usage_gte: number;
        }

        interface Discount {
          /**
           * ID of the coupon to create a new discount for.
           */
          coupon?: string;

          /**
           * ID of an existing discount on the object (or one of its ancestors) to reuse.
           */
          discount?: string;

          /**
           * ID of the promotion code to create a new discount for.
           */
          promotion_code?: string;
        }

        interface PriceData {
          /**
           * Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).
           */
          currency: string;

          /**
           * The ID of the [Product](https://docs.stripe.com/api/products) that this [Price](https://docs.stripe.com/api/prices) will belong to.
           */
          product: string;

          /**
           * The recurring components of a price such as `interval` and `interval_count`.
           */
          recurring: PriceData.Recurring;

          /**
           * Only required if a [default tax behavior](https://stripe.com/docs/tax/products-prices-tax-categories-tax-behavior#setting-a-default-tax-behavior-(recommended)) was not provided in the Stripe Tax settings. Specifies whether the price is considered inclusive of taxes or exclusive of taxes. One of `inclusive`, `exclusive`, or `unspecified`. Once specified as either `inclusive` or `exclusive`, it cannot be changed.
           */
          tax_behavior?: PriceData.TaxBehavior;

          /**
           * A positive integer in cents (or local equivalent) (or 0 for a free price) representing how much to charge.
           */
          unit_amount?: number;

          /**
           * Same as `unit_amount`, but accepts a decimal value in cents (or local equivalent) with at most 12 decimal places. Only one of `unit_amount` and `unit_amount_decimal` can be set.
           */
          unit_amount_decimal?: string;
        }

        namespace PriceData {
          interface Recurring {
            /**
             * Specifies billing frequency. Either `day`, `week`, `month` or `year`.
             */
            interval: Recurring.Interval;

            /**
             * The number of intervals between subscription billings. For example, `interval=month` and `interval_count=3` bills every 3 months. Maximum of three years interval allowed (3 years, 36 months, or 156 weeks).
             */
            interval_count?: number;
          }

          namespace Recurring {
            type Interval = 'day' | 'month' | 'week' | 'year';
          }

          type TaxBehavior = 'exclusive' | 'inclusive' | 'unspecified';
        }
      }

      type PaymentBehavior =
        | 'allow_incomplete'
        | 'default_incomplete'
        | 'error_if_incomplete'
        | 'pending_if_incomplete';

      interface PaymentSettings {
        /**
         * Payment-method-specific configuration to provide to invoices created by the subscription.
         */
        payment_method_options?: PaymentSettings.PaymentMethodOptions;

        /**
         * The list of payment method types (e.g. card) to provide to the invoice's PaymentIntent. If not set, Stripe attempts to automatically determine the types to use by looking at the invoice's default payment method, the subscription's default payment method, the customer's default payment method, and your [invoice template settings](https://dashboard.stripe.com/settings/billing/invoice). Should not be specified with payment_method_configuration
         */
        payment_method_types?: Stripe.Emptyable<
          Array<PaymentSettings.PaymentMethodType>
        >;

        /**
         * Configure whether Stripe updates `subscription.default_payment_method` when payment succeeds. Defaults to `off` if unspecified.
         */
        save_default_payment_method?: PaymentSettings.SaveDefaultPaymentMethod;
      }

      namespace PaymentSettings {
        interface PaymentMethodOptions {
          /**
           * This sub-hash contains details about the Canadian pre-authorized debit payment method options to pass to the invoice's PaymentIntent.
           */
          acss_debit?: Stripe.Emptyable<PaymentMethodOptions.AcssDebit>;

          /**
           * This sub-hash contains details about the Bancontact payment method options to pass to the invoice's PaymentIntent.
           */
          bancontact?: Stripe.Emptyable<PaymentMethodOptions.Bancontact>;

          /**
           * This sub-hash contains details about the Card payment method options to pass to the invoice's PaymentIntent.
           */
          card?: Stripe.Emptyable<PaymentMethodOptions.Card>;

          /**
           * This sub-hash contains details about the Bank transfer payment method options to pass to the invoice's PaymentIntent.
           */
          customer_balance?: Stripe.Emptyable<
            PaymentMethodOptions.CustomerBalance
          >;

          /**
           * This sub-hash contains details about the Konbini payment method options to pass to the invoice's PaymentIntent.
           */
          konbini?: Stripe.Emptyable<PaymentMethodOptions.Konbini>;

          /**
           * This sub-hash contains details about the SEPA Direct Debit payment method options to pass to the invoice's PaymentIntent.
           */
          sepa_debit?: Stripe.Emptyable<PaymentMethodOptions.SepaDebit>;

          /**
           * This sub-hash contains details about the ACH direct debit payment method options to pass to the invoice's PaymentIntent.
           */
          us_bank_account?: Stripe.Emptyable<
            PaymentMethodOptions.UsBankAccount
          >;
        }

        namespace PaymentMethodOptions {
          interface AcssDebit {
            /**
             * Additional fields for Mandate creation
             */
            mandate_options?: AcssDebit.MandateOptions;

            /**
             * Verification method for the intent
             */
            verification_method?: AcssDebit.VerificationMethod;
          }

          namespace AcssDebit {
            interface MandateOptions {
              /**
               * Transaction type of the mandate.
               */
              transaction_type?: MandateOptions.TransactionType;
            }

            namespace MandateOptions {
              type TransactionType = 'business' | 'personal';
            }

            type VerificationMethod = 'automatic' | 'instant' | 'microdeposits';
          }

          interface Bancontact {
            /**
             * Preferred language of the Bancontact authorization page that the customer is redirected to.
             */
            preferred_language?: Bancontact.PreferredLanguage;
          }

          namespace Bancontact {
            type PreferredLanguage = 'de' | 'en' | 'fr' | 'nl';
          }

          interface Card {
            /**
             * Configuration options for setting up an eMandate for cards issued in India.
             */
            mandate_options?: Card.MandateOptions;

            /**
             * Selected network to process this Subscription on. Depends on the available networks of the card attached to the Subscription. Can be only set confirm-time.
             */
            network?: Card.Network;

            /**
             * We strongly recommend that you rely on our SCA Engine to automatically prompt your customers for authentication based on risk level and [other requirements](https://stripe.com/docs/strong-customer-authentication). However, if you wish to request 3D Secure based on logic from your own fraud engine, provide this option. Read our guide on [manually requesting 3D Secure](https://stripe.com/docs/payments/3d-secure/authentication-flow#manual-three-ds) for more information on how this configuration interacts with Radar and our SCA Engine.
             */
            request_three_d_secure?: Card.RequestThreeDSecure;
          }

          namespace Card {
            interface MandateOptions {
              /**
               * Amount to be charged for future payments.
               */
              amount?: number;

              /**
               * One of `fixed` or `maximum`. If `fixed`, the `amount` param refers to the exact amount to be charged in future payments. If `maximum`, the amount charged can be up to the value passed for the `amount` param.
               */
              amount_type?: MandateOptions.AmountType;

              /**
               * A description of the mandate or subscription that is meant to be displayed to the customer.
               */
              description?: string;
            }

            namespace MandateOptions {
              type AmountType = 'fixed' | 'maximum';
            }

            type Network =
              | 'amex'
              | 'cartes_bancaires'
              | 'diners'
              | 'discover'
              | 'eftpos_au'
              | 'girocard'
              | 'interac'
              | 'jcb'
              | 'link'
              | 'mastercard'
              | 'unionpay'
              | 'unknown'
              | 'visa';

            type RequestThreeDSecure = 'any' | 'automatic' | 'challenge';
          }

          interface CustomerBalance {
            /**
             * Configuration for the bank transfer funding type, if the `funding_type` is set to `bank_transfer`.
             */
            bank_transfer?: CustomerBalance.BankTransfer;

            /**
             * The funding method type to be used when there are not enough funds in the customer balance. Permitted values include: `bank_transfer`.
             */
            funding_type?: string;
          }

          namespace CustomerBalance {
            interface BankTransfer {
              /**
               * Configuration for eu_bank_transfer funding type.
               */
              eu_bank_transfer?: BankTransfer.EuBankTransfer;

              /**
               * The bank transfer type that can be used for funding. Permitted values include: `eu_bank_transfer`, `gb_bank_transfer`, `jp_bank_transfer`, `mx_bank_transfer`, or `us_bank_transfer`.
               */
              type?: string;
            }

            namespace BankTransfer {
              interface EuBankTransfer {
                /**
                 * The desired country code of the bank account information. Permitted values include: `BE`, `DE`, `ES`, `FR`, `IE`, or `NL`.
                 */
                country: string;
              }
            }
          }

          interface Konbini {}

          interface SepaDebit {}

          interface UsBankAccount {
            /**
             * Additional fields for Financial Connections Session creation
             */
            financial_connections?: UsBankAccount.FinancialConnections;

            /**
             * Verification method for the intent
             */
            verification_method?: UsBankAccount.VerificationMethod;
          }

          namespace UsBankAccount {
            interface FinancialConnections {
              /**
               * Provide filters for the linked accounts that the customer can select for the payment method.
               */
              filters?: FinancialConnections.Filters;

              /**
               * The list of permissions to request. If this parameter is passed, the `payment_method` permission must be included. Valid permissions include: `balances`, `ownership`, `payment_method`, and `transactions`.
               */
              permissions?: Array<FinancialConnections.Permission>;

              /**
               * List of data features that you would like to retrieve upon account creation.
               */
              prefetch?: Array<FinancialConnections.Prefetch>;
            }

            namespace FinancialConnections {
              interface Filters {
                /**
                 * The account subcategories to use to filter for selectable accounts. Valid subcategories are `checking` and `savings`.
                 */
                account_subcategories?: Array<Filters.AccountSubcategory>;
              }

              namespace Filters {
                type AccountSubcategory = 'checking' | 'savings';
              }

              type Permission =
                | 'balances'
                | 'ownership'
                | 'payment_method'
                | 'transactions';

              type Prefetch = 'balances' | 'ownership' | 'transactions';
            }

            type VerificationMethod = 'automatic' | 'instant' | 'microdeposits';
          }
        }

        type PaymentMethodType =
          | 'ach_credit_transfer'
          | 'ach_debit'
          | 'acss_debit'
          | 'affirm'
          | 'amazon_pay'
          | 'au_becs_debit'
          | 'bacs_debit'
          | 'bancontact'
          | 'boleto'
          | 'card'
          | 'cashapp'
          | 'crypto'
          | 'customer_balance'
          | 'eps'
          | 'fpx'
          | 'giropay'
          | 'grabpay'
          | 'ideal'
          | 'jp_credit_transfer'
          | 'kakao_pay'
          | 'klarna'
          | 'konbini'
          | 'kr_card'
          | 'link'
          | 'multibanco'
          | 'naver_pay'
          | 'nz_bank_account'
          | 'p24'
          | 'payco'
          | 'paynow'
          | 'paypal'
          | 'promptpay'
          | 'revolut_pay'
          | 'sepa_credit_transfer'
          | 'sepa_debit'
          | 'sofort'
          | 'swish'
          | 'us_bank_account'
          | 'wechat_pay';

        type SaveDefaultPaymentMethod = 'off' | 'on_subscription';
      }

      interface PendingInvoiceItemInterval {
        /**
         * Specifies invoicing frequency. Either `day`, `week`, `month` or `year`.
         */
        interval: PendingInvoiceItemInterval.Interval;

        /**
         * The number of intervals between invoices. For example, `interval=month` and `interval_count=3` bills every 3 months. Maximum of one year interval allowed (1 year, 12 months, or 52 weeks).
         */
        interval_count?: number;
      }

      namespace PendingInvoiceItemInterval {
        type Interval = 'day' | 'month' | 'week' | 'year';
      }

      type ProrationBehavior = 'always_invoice' | 'create_prorations' | 'none';

      interface TransferData {
        /**
         * A non-negative decimal between 0 and 100, with at most two decimal places. This represents the percentage of the subscription invoice total that will be transferred to the destination account. By default, the entire amount is transferred to the destination.
         */
        amount_percent?: number;

        /**
         * ID of an existing, connected Stripe account.
         */
        destination: string;
      }

      interface TrialSettings {
        /**
         * Defines how the subscription should behave when the user's free trial ends.
         */
        end_behavior: TrialSettings.EndBehavior;
      }

      namespace TrialSettings {
        interface EndBehavior {
          /**
           * Indicates how the subscription should change when the trial ends if the user did not provide a payment method.
           */
          missing_payment_method: EndBehavior.MissingPaymentMethod;
        }

        namespace EndBehavior {
          type MissingPaymentMethod = 'cancel' | 'create_invoice' | 'pause';
        }
      }
    }

    interface SubscriptionRetrieveParams {
      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    interface SubscriptionUpdateParams {
      /**
       * A list of prices and quantities that will generate invoice items appended to the next invoice for this subscription. You may pass up to 20 items.
       */
      add_invoice_items?: Array<SubscriptionUpdateParams.AddInvoiceItem>;

      /**
       * A non-negative decimal between 0 and 100, with at most two decimal places. This represents the percentage of the subscription invoice total that will be transferred to the application owner's Stripe account. The request must be made by a platform account on a connected account in order to set an application fee percentage. For more information, see the application fees [documentation](https://stripe.com/docs/connect/subscriptions#collecting-fees-on-subscriptions).
       */
      application_fee_percent?: Stripe.Emptyable<number>;

      /**
       * Automatic tax settings for this subscription. We recommend you only include this parameter when the existing value is being changed.
       */
      automatic_tax?: SubscriptionUpdateParams.AutomaticTax;

      /**
       * Either `now` or `unchanged`. Setting the value to `now` resets the subscription's billing cycle anchor to the current time (in UTC). For more information, see the billing cycle [documentation](https://stripe.com/docs/billing/subscriptions/billing-cycle).
       */
      billing_cycle_anchor?: SubscriptionUpdateParams.BillingCycleAnchor;

      /**
       * Define thresholds at which an invoice will be sent, and the subscription advanced to a new billing period. When updating, pass an empty string to remove previously-defined thresholds.
       */
      billing_thresholds?: Stripe.Emptyable<
        SubscriptionUpdateParams.BillingThresholds
      >;

      /**
       * A timestamp at which the subscription should cancel. If set to a date before the current period ends, this will cause a proration if prorations have been enabled using `proration_behavior`. If set during a future period, this will always cause a proration for that period.
       */
      cancel_at?: Stripe.Emptyable<number>;

      /**
       * Indicate whether this subscription should cancel at the end of the current period (`current_period_end`). Defaults to `false`.
       */
      cancel_at_period_end?: boolean;

      /**
       * Details about why this subscription was cancelled
       */
      cancellation_details?: SubscriptionUpdateParams.CancellationDetails;

      /**
       * Either `charge_automatically`, or `send_invoice`. When charging automatically, Stripe will attempt to pay this subscription at the end of the cycle using the default source attached to the customer. When sending an invoice, Stripe will email your customer an invoice with payment instructions and mark the subscription as `active`. Defaults to `charge_automatically`.
       */
      collection_method?: SubscriptionUpdateParams.CollectionMethod;

      /**
       * Number of days a customer has to pay invoices generated by this subscription. Valid only for subscriptions where `collection_method` is set to `send_invoice`.
       */
      days_until_due?: number;

      /**
       * ID of the default payment method for the subscription. It must belong to the customer associated with the subscription. This takes precedence over `default_source`. If neither are set, invoices will use the customer's [invoice_settings.default_payment_method](https://stripe.com/docs/api/customers/object#customer_object-invoice_settings-default_payment_method) or [default_source](https://stripe.com/docs/api/customers/object#customer_object-default_source).
       */
      default_payment_method?: string;

      /**
       * ID of the default payment source for the subscription. It must belong to the customer associated with the subscription and be in a chargeable state. If `default_payment_method` is also set, `default_payment_method` will take precedence. If neither are set, invoices will use the customer's [invoice_settings.default_payment_method](https://stripe.com/docs/api/customers/object#customer_object-invoice_settings-default_payment_method) or [default_source](https://stripe.com/docs/api/customers/object#customer_object-default_source).
       */
      default_source?: Stripe.Emptyable<string>;

      /**
       * The tax rates that will apply to any subscription item that does not have `tax_rates` set. Invoices created will have their `default_tax_rates` populated from the subscription. Pass an empty string to remove previously-defined tax rates.
       */
      default_tax_rates?: Stripe.Emptyable<Array<string>>;

      /**
       * The subscription's description, meant to be displayable to the customer. Use this field to optionally store an explanation of the subscription for rendering in Stripe surfaces and certain local payment methods UIs.
       */
      description?: Stripe.Emptyable<string>;

      /**
       * The coupons to redeem into discounts for the subscription. If not specified or empty, inherits the discount from the subscription's customer.
       */
      discounts?: Stripe.Emptyable<Array<SubscriptionUpdateParams.Discount>>;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * All invoices will be billed using the specified settings.
       */
      invoice_settings?: SubscriptionUpdateParams.InvoiceSettings;

      /**
       * A list of up to 20 subscription items, each with an attached price.
       */
      items?: Array<SubscriptionUpdateParams.Item>;

      /**
       * Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Individual keys can be unset by posting an empty value to them. All keys can be unset by posting an empty value to `metadata`.
       */
      metadata?: Stripe.Emptyable<Stripe.MetadataParam>;

      /**
       * Indicates if a customer is on or off-session while an invoice payment is attempted. Defaults to `false` (on-session).
       */
      off_session?: boolean;

      /**
       * The account on behalf of which to charge, for each of the subscription's invoices.
       */
      on_behalf_of?: Stripe.Emptyable<string>;

      /**
       * If specified, payment collection for this subscription will be paused. Note that the subscription status will be unchanged and will not be updated to `paused`. Learn more about [pausing collection](https://stripe.com/docs/billing/subscriptions/pause-payment).
       */
      pause_collection?: Stripe.Emptyable<
        SubscriptionUpdateParams.PauseCollection
      >;

      /**
       * Use `allow_incomplete` to transition the subscription to `status=past_due` if a payment is required but cannot be paid. This allows you to manage scenarios where additional user actions are needed to pay a subscription's invoice. For example, SCA regulation may require 3DS authentication to complete payment. See the [SCA Migration Guide](https://stripe.com/docs/billing/migration/strong-customer-authentication) for Billing to learn more. This is the default behavior.
       *
       * Use `default_incomplete` to transition the subscription to `status=past_due` when payment is required and await explicit confirmation of the invoice's payment intent. This allows simpler management of scenarios where additional user actions are needed to pay a subscription's invoice. Such as failed payments, [SCA regulation](https://stripe.com/docs/billing/migration/strong-customer-authentication), or collecting a mandate for a bank debit payment method.
       *
       * Use `pending_if_incomplete` to update the subscription using [pending updates](https://stripe.com/docs/billing/subscriptions/pending-updates). When you use `pending_if_incomplete` you can only pass the parameters [supported by pending updates](https://stripe.com/docs/billing/pending-updates-reference#supported-attributes).
       *
       * Use `error_if_incomplete` if you want Stripe to return an HTTP 402 status code if a subscription's invoice cannot be paid. For example, if a payment method requires 3DS authentication due to SCA regulation and further user action is needed, this parameter does not update the subscription and returns an error instead. This was the default behavior for API versions prior to 2019-03-14. See the [changelog](https://stripe.com/docs/upgrades#2019-03-14) to learn more.
       */
      payment_behavior?: SubscriptionUpdateParams.PaymentBehavior;

      /**
       * Payment settings to pass to invoices created by the subscription.
       */
      payment_settings?: SubscriptionUpdateParams.PaymentSettings;

      /**
       * Specifies an interval for how often to bill for any pending invoice items. It is analogous to calling [Create an invoice](https://stripe.com/docs/api#create_invoice) for the given subscription at the specified interval.
       */
      pending_invoice_item_interval?: Stripe.Emptyable<
        SubscriptionUpdateParams.PendingInvoiceItemInterval
      >;

      /**
       * Determines how to handle [prorations](https://stripe.com/docs/billing/subscriptions/prorations) when the billing cycle changes (e.g., when switching plans, resetting `billing_cycle_anchor=now`, or starting a trial), or if an item's `quantity` changes. The default value is `create_prorations`.
       */
      proration_behavior?: SubscriptionUpdateParams.ProrationBehavior;

      /**
       * If set, prorations will be calculated as though the subscription was updated at the given time. This can be used to apply exactly the same prorations that were previewed with the [create preview](https://stripe.com/docs/api/invoices/create_preview) endpoint. `proration_date` can also be used to implement custom proration logic, such as prorating by day instead of by second, by providing the time that you wish to use for proration calculations.
       */
      proration_date?: number;

      /**
       * If specified, the funds from the subscription's invoices will be transferred to the destination and the ID of the resulting transfers will be found on the resulting charges. This will be unset if you POST an empty value.
       */
      transfer_data?: Stripe.Emptyable<SubscriptionUpdateParams.TransferData>;

      /**
       * Unix timestamp representing the end of the trial period the customer will get before being charged for the first time. This will always overwrite any trials that might apply via a subscribed plan. If set, `trial_end` will override the default trial period of the plan the customer is being subscribed to. The `billing_cycle_anchor` will be updated to the `trial_end` value. The special value `now` can be provided to end the customer's trial immediately. Can be at most two years from `billing_cycle_anchor`.
       */
      trial_end?: 'now' | number;

      /**
       * Indicates if a plan's `trial_period_days` should be applied to the subscription. Setting `trial_end` per subscription is preferred, and this defaults to `false`. Setting this flag to `true` together with `trial_end` is not allowed. See [Using trial periods on subscriptions](https://stripe.com/docs/billing/subscriptions/trials) to learn more.
       */
      trial_from_plan?: boolean;

      /**
       * Settings related to subscription trials.
       */
      trial_settings?: SubscriptionUpdateParams.TrialSettings;
    }

    namespace SubscriptionUpdateParams {
      interface AddInvoiceItem {
        /**
         * The coupons to redeem into discounts for the item.
         */
        discounts?: Array<AddInvoiceItem.Discount>;

        /**
         * The ID of the price object. One of `price` or `price_data` is required.
         */
        price?: string;

        /**
         * Data used to generate a new [Price](https://stripe.com/docs/api/prices) object inline. One of `price` or `price_data` is required.
         */
        price_data?: AddInvoiceItem.PriceData;

        /**
         * Quantity for this item. Defaults to 1.
         */
        quantity?: number;

        /**
         * The tax rates which apply to the item. When set, the `default_tax_rates` do not apply to this item.
         */
        tax_rates?: Stripe.Emptyable<Array<string>>;
      }

      namespace AddInvoiceItem {
        interface Discount {
          /**
           * ID of the coupon to create a new discount for.
           */
          coupon?: string;

          /**
           * ID of an existing discount on the object (or one of its ancestors) to reuse.
           */
          discount?: string;

          /**
           * ID of the promotion code to create a new discount for.
           */
          promotion_code?: string;
        }

        interface PriceData {
          /**
           * Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).
           */
          currency: string;

          /**
           * The ID of the [Product](https://docs.stripe.com/api/products) that this [Price](https://docs.stripe.com/api/prices) will belong to.
           */
          product: string;

          /**
           * Only required if a [default tax behavior](https://stripe.com/docs/tax/products-prices-tax-categories-tax-behavior#setting-a-default-tax-behavior-(recommended)) was not provided in the Stripe Tax settings. Specifies whether the price is considered inclusive of taxes or exclusive of taxes. One of `inclusive`, `exclusive`, or `unspecified`. Once specified as either `inclusive` or `exclusive`, it cannot be changed.
           */
          tax_behavior?: PriceData.TaxBehavior;

          /**
           * A positive integer in cents (or local equivalent) (or 0 for a free price) representing how much to charge or a negative integer representing the amount to credit to the customer.
           */
          unit_amount?: number;

          /**
           * Same as `unit_amount`, but accepts a decimal value in cents (or local equivalent) with at most 12 decimal places. Only one of `unit_amount` and `unit_amount_decimal` can be set.
           */
          unit_amount_decimal?: string;
        }

        namespace PriceData {
          type TaxBehavior = 'exclusive' | 'inclusive' | 'unspecified';
        }
      }

      interface AutomaticTax {
        /**
         * Enabled automatic tax calculation which will automatically compute tax rates on all invoices generated by the subscription.
         */
        enabled: boolean;

        /**
         * The account that's liable for tax. If set, the business address and tax registrations required to perform the tax calculation are loaded from this account. The tax transaction is returned in the report of the connected account.
         */
        liability?: AutomaticTax.Liability;
      }

      namespace AutomaticTax {
        interface Liability {
          /**
           * The connected account being referenced when `type` is `account`.
           */
          account?: string;

          /**
           * Type of the account referenced in the request.
           */
          type: Liability.Type;
        }

        namespace Liability {
          type Type = 'account' | 'self';
        }
      }

      type BillingCycleAnchor = 'now' | 'unchanged';

      interface BillingThresholds {
        /**
         * Monetary threshold that triggers the subscription to advance to a new billing period
         */
        amount_gte?: number;

        /**
         * Indicates if the `billing_cycle_anchor` should be reset when a threshold is reached. If true, `billing_cycle_anchor` will be updated to the date/time the threshold was last reached; otherwise, the value will remain unchanged.
         */
        reset_billing_cycle_anchor?: boolean;
      }

      interface CancellationDetails {
        /**
         * Additional comments about why the user canceled the subscription, if the subscription was canceled explicitly by the user.
         */
        comment?: Stripe.Emptyable<string>;

        /**
         * The customer submitted reason for why they canceled, if the subscription was canceled explicitly by the user.
         */
        feedback?: Stripe.Emptyable<CancellationDetails.Feedback>;
      }

      namespace CancellationDetails {
        type Feedback =
          | 'customer_service'
          | 'low_quality'
          | 'missing_features'
          | 'other'
          | 'switched_service'
          | 'too_complex'
          | 'too_expensive'
          | 'unused';
      }

      type CollectionMethod = 'charge_automatically' | 'send_invoice';

      interface Discount {
        /**
         * ID of the coupon to create a new discount for.
         */
        coupon?: string;

        /**
         * ID of an existing discount on the object (or one of its ancestors) to reuse.
         */
        discount?: string;

        /**
         * ID of the promotion code to create a new discount for.
         */
        promotion_code?: string;
      }

      interface InvoiceSettings {
        /**
         * The account tax IDs associated with the subscription. Will be set on invoices generated by the subscription.
         */
        account_tax_ids?: Stripe.Emptyable<Array<string>>;

        /**
         * The connected account that issues the invoice. The invoice is presented with the branding and support information of the specified account.
         */
        issuer?: InvoiceSettings.Issuer;
      }

      namespace InvoiceSettings {
        interface Issuer {
          /**
           * The connected account being referenced when `type` is `account`.
           */
          account?: string;

          /**
           * Type of the account referenced in the request.
           */
          type: Issuer.Type;
        }

        namespace Issuer {
          type Type = 'account' | 'self';
        }
      }

      interface Item {
        /**
         * Define thresholds at which an invoice will be sent, and the subscription advanced to a new billing period. Pass an empty string to remove previously-defined thresholds.
         */
        billing_thresholds?: Stripe.Emptyable<Item.BillingThresholds>;

        /**
         * Delete all usage for a given subscription item. You must pass this when deleting a usage records subscription item. `clear_usage` has no effect if the plan has a billing meter attached.
         */
        clear_usage?: boolean;

        /**
         * A flag that, if set to `true`, will delete the specified item.
         */
        deleted?: boolean;

        /**
         * The coupons to redeem into discounts for the subscription item.
         */
        discounts?: Stripe.Emptyable<Array<Item.Discount>>;

        /**
         * Subscription item to update.
         */
        id?: string;

        /**
         * Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Individual keys can be unset by posting an empty value to them. All keys can be unset by posting an empty value to `metadata`.
         */
        metadata?: Stripe.Emptyable<Stripe.MetadataParam>;

        /**
         * Plan ID for this item, as a string.
         */
        plan?: string;

        /**
         * The ID of the price object. One of `price` or `price_data` is required. When changing a subscription item's price, `quantity` is set to 1 unless a `quantity` parameter is provided.
         */
        price?: string;

        /**
         * Data used to generate a new [Price](https://stripe.com/docs/api/prices) object inline. One of `price` or `price_data` is required.
         */
        price_data?: Item.PriceData;

        /**
         * Quantity for this item.
         */
        quantity?: number;

        /**
         * A list of [Tax Rate](https://stripe.com/docs/api/tax_rates) ids. These Tax Rates will override the [`default_tax_rates`](https://stripe.com/docs/api/subscriptions/create#create_subscription-default_tax_rates) on the Subscription. When updating, pass an empty string to remove previously-defined tax rates.
         */
        tax_rates?: Stripe.Emptyable<Array<string>>;
      }

      namespace Item {
        interface BillingThresholds {
          /**
           * Number of units that meets the billing threshold to advance the subscription to a new billing period (e.g., it takes 10 $5 units to meet a $50 [monetary threshold](https://stripe.com/docs/api/subscriptions/update#update_subscription-billing_thresholds-amount_gte))
           */
          usage_gte: number;
        }

        interface Discount {
          /**
           * ID of the coupon to create a new discount for.
           */
          coupon?: string;

          /**
           * ID of an existing discount on the object (or one of its ancestors) to reuse.
           */
          discount?: string;

          /**
           * ID of the promotion code to create a new discount for.
           */
          promotion_code?: string;
        }

        interface PriceData {
          /**
           * Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).
           */
          currency: string;

          /**
           * The ID of the [Product](https://docs.stripe.com/api/products) that this [Price](https://docs.stripe.com/api/prices) will belong to.
           */
          product: string;

          /**
           * The recurring components of a price such as `interval` and `interval_count`.
           */
          recurring: PriceData.Recurring;

          /**
           * Only required if a [default tax behavior](https://stripe.com/docs/tax/products-prices-tax-categories-tax-behavior#setting-a-default-tax-behavior-(recommended)) was not provided in the Stripe Tax settings. Specifies whether the price is considered inclusive of taxes or exclusive of taxes. One of `inclusive`, `exclusive`, or `unspecified`. Once specified as either `inclusive` or `exclusive`, it cannot be changed.
           */
          tax_behavior?: PriceData.TaxBehavior;

          /**
           * A positive integer in cents (or local equivalent) (or 0 for a free price) representing how much to charge.
           */
          unit_amount?: number;

          /**
           * Same as `unit_amount`, but accepts a decimal value in cents (or local equivalent) with at most 12 decimal places. Only one of `unit_amount` and `unit_amount_decimal` can be set.
           */
          unit_amount_decimal?: string;
        }

        namespace PriceData {
          interface Recurring {
            /**
             * Specifies billing frequency. Either `day`, `week`, `month` or `year`.
             */
            interval: Recurring.Interval;

            /**
             * The number of intervals between subscription billings. For example, `interval=month` and `interval_count=3` bills every 3 months. Maximum of three years interval allowed (3 years, 36 months, or 156 weeks).
             */
            interval_count?: number;
          }

          namespace Recurring {
            type Interval = 'day' | 'month' | 'week' | 'year';
          }

          type TaxBehavior = 'exclusive' | 'inclusive' | 'unspecified';
        }
      }

      interface PauseCollection {
        /**
         * The payment collection behavior for this subscription while paused. One of `keep_as_draft`, `mark_uncollectible`, or `void`.
         */
        behavior: PauseCollection.Behavior;

        /**
         * The time after which the subscription will resume collecting payments.
         */
        resumes_at?: number;
      }

      namespace PauseCollection {
        type Behavior = 'keep_as_draft' | 'mark_uncollectible' | 'void';
      }

      type PaymentBehavior =
        | 'allow_incomplete'
        | 'default_incomplete'
        | 'error_if_incomplete'
        | 'pending_if_incomplete';

      interface PaymentSettings {
        /**
         * Payment-method-specific configuration to provide to invoices created by the subscription.
         */
        payment_method_options?: PaymentSettings.PaymentMethodOptions;

        /**
         * The list of payment method types (e.g. card) to provide to the invoice's PaymentIntent. If not set, Stripe attempts to automatically determine the types to use by looking at the invoice's default payment method, the subscription's default payment method, the customer's default payment method, and your [invoice template settings](https://dashboard.stripe.com/settings/billing/invoice). Should not be specified with payment_method_configuration
         */
        payment_method_types?: Stripe.Emptyable<
          Array<PaymentSettings.PaymentMethodType>
        >;

        /**
         * Configure whether Stripe updates `subscription.default_payment_method` when payment succeeds. Defaults to `off` if unspecified.
         */
        save_default_payment_method?: PaymentSettings.SaveDefaultPaymentMethod;
      }

      namespace PaymentSettings {
        interface PaymentMethodOptions {
          /**
           * This sub-hash contains details about the Canadian pre-authorized debit payment method options to pass to the invoice's PaymentIntent.
           */
          acss_debit?: Stripe.Emptyable<PaymentMethodOptions.AcssDebit>;

          /**
           * This sub-hash contains details about the Bancontact payment method options to pass to the invoice's PaymentIntent.
           */
          bancontact?: Stripe.Emptyable<PaymentMethodOptions.Bancontact>;

          /**
           * This sub-hash contains details about the Card payment method options to pass to the invoice's PaymentIntent.
           */
          card?: Stripe.Emptyable<PaymentMethodOptions.Card>;

          /**
           * This sub-hash contains details about the Bank transfer payment method options to pass to the invoice's PaymentIntent.
           */
          customer_balance?: Stripe.Emptyable<
            PaymentMethodOptions.CustomerBalance
          >;

          /**
           * This sub-hash contains details about the Konbini payment method options to pass to the invoice's PaymentIntent.
           */
          konbini?: Stripe.Emptyable<PaymentMethodOptions.Konbini>;

          /**
           * This sub-hash contains details about the SEPA Direct Debit payment method options to pass to the invoice's PaymentIntent.
           */
          sepa_debit?: Stripe.Emptyable<PaymentMethodOptions.SepaDebit>;

          /**
           * This sub-hash contains details about the ACH direct debit payment method options to pass to the invoice's PaymentIntent.
           */
          us_bank_account?: Stripe.Emptyable<
            PaymentMethodOptions.UsBankAccount
          >;
        }

        namespace PaymentMethodOptions {
          interface AcssDebit {
            /**
             * Additional fields for Mandate creation
             */
            mandate_options?: AcssDebit.MandateOptions;

            /**
             * Verification method for the intent
             */
            verification_method?: AcssDebit.VerificationMethod;
          }

          namespace AcssDebit {
            interface MandateOptions {
              /**
               * Transaction type of the mandate.
               */
              transaction_type?: MandateOptions.TransactionType;
            }

            namespace MandateOptions {
              type TransactionType = 'business' | 'personal';
            }

            type VerificationMethod = 'automatic' | 'instant' | 'microdeposits';
          }

          interface Bancontact {
            /**
             * Preferred language of the Bancontact authorization page that the customer is redirected to.
             */
            preferred_language?: Bancontact.PreferredLanguage;
          }

          namespace Bancontact {
            type PreferredLanguage = 'de' | 'en' | 'fr' | 'nl';
          }

          interface Card {
            /**
             * Configuration options for setting up an eMandate for cards issued in India.
             */
            mandate_options?: Card.MandateOptions;

            /**
             * Selected network to process this Subscription on. Depends on the available networks of the card attached to the Subscription. Can be only set confirm-time.
             */
            network?: Card.Network;

            /**
             * We strongly recommend that you rely on our SCA Engine to automatically prompt your customers for authentication based on risk level and [other requirements](https://stripe.com/docs/strong-customer-authentication). However, if you wish to request 3D Secure based on logic from your own fraud engine, provide this option. Read our guide on [manually requesting 3D Secure](https://stripe.com/docs/payments/3d-secure/authentication-flow#manual-three-ds) for more information on how this configuration interacts with Radar and our SCA Engine.
             */
            request_three_d_secure?: Card.RequestThreeDSecure;
          }

          namespace Card {
            interface MandateOptions {
              /**
               * Amount to be charged for future payments.
               */
              amount?: number;

              /**
               * One of `fixed` or `maximum`. If `fixed`, the `amount` param refers to the exact amount to be charged in future payments. If `maximum`, the amount charged can be up to the value passed for the `amount` param.
               */
              amount_type?: MandateOptions.AmountType;

              /**
               * A description of the mandate or subscription that is meant to be displayed to the customer.
               */
              description?: string;
            }

            namespace MandateOptions {
              type AmountType = 'fixed' | 'maximum';
            }

            type Network =
              | 'amex'
              | 'cartes_bancaires'
              | 'diners'
              | 'discover'
              | 'eftpos_au'
              | 'girocard'
              | 'interac'
              | 'jcb'
              | 'link'
              | 'mastercard'
              | 'unionpay'
              | 'unknown'
              | 'visa';

            type RequestThreeDSecure = 'any' | 'automatic' | 'challenge';
          }

          interface CustomerBalance {
            /**
             * Configuration for the bank transfer funding type, if the `funding_type` is set to `bank_transfer`.
             */
            bank_transfer?: CustomerBalance.BankTransfer;

            /**
             * The funding method type to be used when there are not enough funds in the customer balance. Permitted values include: `bank_transfer`.
             */
            funding_type?: string;
          }

          namespace CustomerBalance {
            interface BankTransfer {
              /**
               * Configuration for eu_bank_transfer funding type.
               */
              eu_bank_transfer?: BankTransfer.EuBankTransfer;

              /**
               * The bank transfer type that can be used for funding. Permitted values include: `eu_bank_transfer`, `gb_bank_transfer`, `jp_bank_transfer`, `mx_bank_transfer`, or `us_bank_transfer`.
               */
              type?: string;
            }

            namespace BankTransfer {
              interface EuBankTransfer {
                /**
                 * The desired country code of the bank account information. Permitted values include: `BE`, `DE`, `ES`, `FR`, `IE`, or `NL`.
                 */
                country: string;
              }
            }
          }

          interface Konbini {}

          interface SepaDebit {}

          interface UsBankAccount {
            /**
             * Additional fields for Financial Connections Session creation
             */
            financial_connections?: UsBankAccount.FinancialConnections;

            /**
             * Verification method for the intent
             */
            verification_method?: UsBankAccount.VerificationMethod;
          }

          namespace UsBankAccount {
            interface FinancialConnections {
              /**
               * Provide filters for the linked accounts that the customer can select for the payment method.
               */
              filters?: FinancialConnections.Filters;

              /**
               * The list of permissions to request. If this parameter is passed, the `payment_method` permission must be included. Valid permissions include: `balances`, `ownership`, `payment_method`, and `transactions`.
               */
              permissions?: Array<FinancialConnections.Permission>;

              /**
               * List of data features that you would like to retrieve upon account creation.
               */
              prefetch?: Array<FinancialConnections.Prefetch>;
            }

            namespace FinancialConnections {
              interface Filters {
                /**
                 * The account subcategories to use to filter for selectable accounts. Valid subcategories are `checking` and `savings`.
                 */
                account_subcategories?: Array<Filters.AccountSubcategory>;
              }

              namespace Filters {
                type AccountSubcategory = 'checking' | 'savings';
              }

              type Permission =
                | 'balances'
                | 'ownership'
                | 'payment_method'
                | 'transactions';

              type Prefetch = 'balances' | 'ownership' | 'transactions';
            }

            type VerificationMethod = 'automatic' | 'instant' | 'microdeposits';
          }
        }

        type PaymentMethodType =
          | 'ach_credit_transfer'
          | 'ach_debit'
          | 'acss_debit'
          | 'affirm'
          | 'amazon_pay'
          | 'au_becs_debit'
          | 'bacs_debit'
          | 'bancontact'
          | 'boleto'
          | 'card'
          | 'cashapp'
          | 'crypto'
          | 'customer_balance'
          | 'eps'
          | 'fpx'
          | 'giropay'
          | 'grabpay'
          | 'ideal'
          | 'jp_credit_transfer'
          | 'kakao_pay'
          | 'klarna'
          | 'konbini'
          | 'kr_card'
          | 'link'
          | 'multibanco'
          | 'naver_pay'
          | 'nz_bank_account'
          | 'p24'
          | 'payco'
          | 'paynow'
          | 'paypal'
          | 'promptpay'
          | 'revolut_pay'
          | 'sepa_credit_transfer'
          | 'sepa_debit'
          | 'sofort'
          | 'swish'
          | 'us_bank_account'
          | 'wechat_pay';

        type SaveDefaultPaymentMethod = 'off' | 'on_subscription';
      }

      interface PendingInvoiceItemInterval {
        /**
         * Specifies invoicing frequency. Either `day`, `week`, `month` or `year`.
         */
        interval: PendingInvoiceItemInterval.Interval;

        /**
         * The number of intervals between invoices. For example, `interval=month` and `interval_count=3` bills every 3 months. Maximum of one year interval allowed (1 year, 12 months, or 52 weeks).
         */
        interval_count?: number;
      }

      namespace PendingInvoiceItemInterval {
        type Interval = 'day' | 'month' | 'week' | 'year';
      }

      type ProrationBehavior = 'always_invoice' | 'create_prorations' | 'none';

      interface TransferData {
        /**
         * A non-negative decimal between 0 and 100, with at most two decimal places. This represents the percentage of the subscription invoice total that will be transferred to the destination account. By default, the entire amount is transferred to the destination.
         */
        amount_percent?: number;

        /**
         * ID of an existing, connected Stripe account.
         */
        destination: string;
      }

      interface TrialSettings {
        /**
         * Defines how the subscription should behave when the user's free trial ends.
         */
        end_behavior: TrialSettings.EndBehavior;
      }

      namespace TrialSettings {
        interface EndBehavior {
          /**
           * Indicates how the subscription should change when the trial ends if the user did not provide a payment method.
           */
          missing_payment_method: EndBehavior.MissingPaymentMethod;
        }

        namespace EndBehavior {
          type MissingPaymentMethod = 'cancel' | 'create_invoice' | 'pause';
        }
      }
    }

    interface SubscriptionListParams extends PaginationParams {
      /**
       * Filter subscriptions by their automatic tax settings.
       */
      automatic_tax?: SubscriptionListParams.AutomaticTax;

      /**
       * The collection method of the subscriptions to retrieve. Either `charge_automatically` or `send_invoice`.
       */
      collection_method?: SubscriptionListParams.CollectionMethod;

      /**
       * Only return subscriptions that were created during the given date interval.
       */
      created?: Stripe.RangeQueryParam | number;

      /**
       * Only return subscriptions whose current_period_end falls within the given date interval.
       */
      current_period_end?: Stripe.RangeQueryParam | number;

      /**
       * Only return subscriptions whose current_period_start falls within the given date interval.
       */
      current_period_start?: Stripe.RangeQueryParam | number;

      /**
       * The ID of the customer whose subscriptions will be retrieved.
       */
      customer?: string;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * The ID of the plan whose subscriptions will be retrieved.
       */
      plan?: string;

      /**
       * Filter for subscriptions that contain this recurring price ID.
       */
      price?: string;

      /**
       * The status of the subscriptions to retrieve. Passing in a value of `canceled` will return all canceled subscriptions, including those belonging to deleted customers. Pass `ended` to find subscriptions that are canceled and subscriptions that are expired due to [incomplete payment](https://stripe.com/docs/billing/subscriptions/overview#subscription-statuses). Passing in a value of `all` will return subscriptions of all statuses. If no value is supplied, all subscriptions that have not been canceled are returned.
       */
      status?: SubscriptionListParams.Status;

      /**
       * Filter for subscriptions that are associated with the specified test clock. The response will not include subscriptions with test clocks if this and the customer parameter is not set.
       */
      test_clock?: string;
    }

    namespace SubscriptionListParams {
      interface AutomaticTax {
        /**
         * Enabled automatic tax calculation which will automatically compute tax rates on all invoices generated by the subscription.
         */
        enabled: boolean;
      }

      type CollectionMethod = 'charge_automatically' | 'send_invoice';

      type Status =
        | 'active'
        | 'all'
        | 'canceled'
        | 'ended'
        | 'incomplete'
        | 'incomplete_expired'
        | 'past_due'
        | 'paused'
        | 'trialing'
        | 'unpaid';
    }

    interface SubscriptionCancelParams {
      /**
       * Details about why this subscription was cancelled
       */
      cancellation_details?: SubscriptionCancelParams.CancellationDetails;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * Will generate a final invoice that invoices for any un-invoiced metered usage and new/pending proration invoice items. Defaults to `false`.
       */
      invoice_now?: boolean;

      /**
       * Will generate a proration invoice item that credits remaining unused time until the subscription period end. Defaults to `false`.
       */
      prorate?: boolean;
    }

    namespace SubscriptionCancelParams {
      interface CancellationDetails {
        /**
         * Additional comments about why the user canceled the subscription, if the subscription was canceled explicitly by the user.
         */
        comment?: Stripe.Emptyable<string>;

        /**
         * The customer submitted reason for why they canceled, if the subscription was canceled explicitly by the user.
         */
        feedback?: Stripe.Emptyable<CancellationDetails.Feedback>;
      }

      namespace CancellationDetails {
        type Feedback =
          | 'customer_service'
          | 'low_quality'
          | 'missing_features'
          | 'other'
          | 'switched_service'
          | 'too_complex'
          | 'too_expensive'
          | 'unused';
      }
    }

    interface SubscriptionDeleteDiscountParams {}

    interface SubscriptionMigrateParams {
      /**
       * Controls how prorations and invoices for subscriptions are calculated and orchestrated.
       */
      billing_mode: SubscriptionMigrateParams.BillingMode;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    namespace SubscriptionMigrateParams {
      interface BillingMode {
        type: 'flexible';
      }
    }

    interface SubscriptionResumeParams {
      /**
       * The billing cycle anchor that applies when the subscription is resumed. Either `now` or `unchanged`. The default is `now`. For more information, see the billing cycle [documentation](https://stripe.com/docs/billing/subscriptions/billing-cycle).
       */
      billing_cycle_anchor?: SubscriptionResumeParams.BillingCycleAnchor;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * Determines how to handle [prorations](https://stripe.com/docs/billing/subscriptions/prorations) resulting from the `billing_cycle_anchor` being `unchanged`. When the `billing_cycle_anchor` is set to `now` (default value), no prorations are generated. If no value is passed, the default is `create_prorations`.
       */
      proration_behavior?: SubscriptionResumeParams.ProrationBehavior;

      /**
       * If set, prorations will be calculated as though the subscription was resumed at the given time. This can be used to apply exactly the same prorations that were previewed with the [create preview](https://stripe.com/docs/api/invoices/create_preview) endpoint.
       */
      proration_date?: number;
    }

    namespace SubscriptionResumeParams {
      type BillingCycleAnchor = 'now' | 'unchanged';

      type ProrationBehavior = 'always_invoice' | 'create_prorations' | 'none';
    }

    interface SubscriptionSearchParams {
      /**
       * The search query string. See [search query language](https://stripe.com/docs/search#search-query-language) and the list of supported [query fields for subscriptions](https://stripe.com/docs/search#query-fields-for-subscriptions).
       */
      query: string;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is 10.
       */
      limit?: number;

      /**
       * A cursor for pagination across multiple pages of results. Don't include this parameter on the first call. Use the next_page value returned in a previous response to request subsequent results.
       */
      page?: string;
    }

    class SubscriptionsResource {
      /**
       * Creates a new subscription on an existing customer. Each customer can have up to 500 active or scheduled subscriptions.
       *
       * When you create a subscription with collection_method=charge_automatically, the first invoice is finalized as part of the request.
       * The payment_behavior parameter determines the exact behavior of the initial payment.
       *
       * To start subscriptions where the first invoice always begins in a draft status, use [subscription schedules](https://docs.stripe.com/docs/billing/subscriptions/subscription-schedules#managing) instead.
       * Schedules provide the flexibility to model more complex billing configurations that change over time.
       */
      create(
        params: SubscriptionCreateParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Subscription>>;

      /**
       * Retrieves the subscription with the given ID.
       */
      retrieve(
        id: string,
        params?: SubscriptionRetrieveParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Subscription>>;
      retrieve(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Subscription>>;

      /**
       * Updates an existing subscription to match the specified parameters.
       * When changing prices or quantities, we optionally prorate the price we charge next month to make up for any price changes.
       * To preview how the proration is calculated, use the [create preview](https://docs.stripe.com/docs/api/invoices/create_preview) endpoint.
       *
       * By default, we prorate subscription changes. For example, if a customer signs up on May 1 for a 100 price, they'll be billed 100 immediately. If on May 15 they switch to a 200 price, then on June 1 they'll be billed 250 (200 for a renewal of her subscription, plus a 50 prorating adjustment for half of the previous month's 100 difference). Similarly, a downgrade generates a credit that is applied to the next invoice. We also prorate when you make quantity changes.
       *
       * Switching prices does not normally change the billing date or generate an immediate charge unless:
       *
       *
       * The billing interval is changed (for example, from monthly to yearly).
       * The subscription moves from free to paid.
       * A trial starts or ends.
       *
       *
       * In these cases, we apply a credit for the unused time on the previous price, immediately charge the customer using the new price, and reset the billing date. Learn about how [Stripe immediately attempts payment for subscription changes](https://docs.stripe.com/docs/billing/subscriptions/upgrade-downgrade#immediate-payment).
       *
       * If you want to charge for an upgrade immediately, pass proration_behavior as always_invoice to create prorations, automatically invoice the customer for those proration adjustments, and attempt to collect payment. If you pass create_prorations, the prorations are created but not automatically invoiced. If you want to bill the customer for the prorations before the subscription's renewal date, you need to manually [invoice the customer](https://docs.stripe.com/docs/api/invoices/create).
       *
       * If you don't want to prorate, set the proration_behavior option to none. With this option, the customer is billed 100 on May 1 and 200 on June 1. Similarly, if you set proration_behavior to none when switching between different billing intervals (for example, from monthly to yearly), we don't generate any credits for the old subscription's unused time. We still reset the billing date and bill immediately for the new subscription.
       *
       * Updating the quantity on a subscription many times in an hour may result in [rate limiting. If you need to bill for a frequently changing quantity, consider integrating <a href="/docs/billing/subscriptions/usage-based">usage-based billing](https://docs.stripe.com/docs/rate-limits) instead.
       */
      update(
        id: string,
        params?: SubscriptionUpdateParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Subscription>>;

      /**
       * By default, returns a list of subscriptions that have not been canceled. In order to list canceled subscriptions, specify status=canceled.
       */
      list(
        params?: SubscriptionListParams,
        options?: RequestOptions
      ): ApiListPromise<Stripe.Subscription>;
      list(options?: RequestOptions): ApiListPromise<Stripe.Subscription>;

      /**
       * Cancels a customer's subscription immediately. The customer won't be charged again for the subscription. After it's canceled, you can no longer update the subscription or its [metadata](https://docs.stripe.com/metadata).
       *
       * Any pending invoice items that you've created are still charged at the end of the period, unless manually [deleted](https://docs.stripe.com/api#delete_invoiceitem). If you've set the subscription to cancel at the end of the period, any pending prorations are also left in place and collected at the end of the period. But if the subscription is set to cancel immediately, pending prorations are removed if invoice_now and prorate are both set to true.
       *
       * By default, upon subscription cancellation, Stripe stops automatic collection of all finalized invoices for the customer. This is intended to prevent unexpected payment attempts after the customer has canceled a subscription. However, you can resume automatic collection of the invoices manually after subscription cancellation to have us proceed. Or, you could check for unpaid invoices before allowing the customer to cancel the subscription at all.
       */
      cancel(
        id: string,
        params?: SubscriptionCancelParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Subscription>>;
      cancel(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Subscription>>;

      /**
       * Removes the currently applied discount on a subscription.
       */
      deleteDiscount(
        id: string,
        params?: SubscriptionDeleteDiscountParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.DeletedDiscount>>;
      deleteDiscount(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.DeletedDiscount>>;

      /**
       * Upgrade the billing_mode of an existing subscription.
       */
      migrate(
        id: string,
        params: SubscriptionMigrateParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Subscription>>;

      /**
       * Initiates resumption of a paused subscription, optionally resetting the billing cycle anchor and creating prorations. If a resumption invoice is generated, it must be paid or marked uncollectible before the subscription will be unpaused. If payment succeeds the subscription will become active, and if payment fails the subscription will be past_due. The resumption invoice will void automatically if not paid by the expiration date.
       */
      resume(
        id: string,
        params?: SubscriptionResumeParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Subscription>>;
      resume(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Subscription>>;

      /**
       * Search for subscriptions you've previously created using Stripe's [Search Query Language](https://docs.stripe.com/docs/search#search-query-language).
       * Don't use search in read-after-write flows where strict consistency is necessary. Under normal operating
       * conditions, data is searchable in less than a minute. Occasionally, propagation of new or updated data can be up
       * to an hour behind during outages. Search functionality is not available to merchants in India.
       */
      search(
        params: SubscriptionSearchParams,
        options?: RequestOptions
      ): ApiSearchResultPromise<Stripe.Subscription>;
    }
  }
}
