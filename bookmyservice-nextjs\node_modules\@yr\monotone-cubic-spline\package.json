{"name": "@yr/monotone-cubic-spline", "description": "Convert a series of points to a monotone cubic spline", "version": "1.0.3", "author": "<PERSON> <<EMAIL>>", "dependencies": {}, "devDependencies": {"babel-plugin-syntax-trailing-function-commas": "6.22.0", "babel-plugin-transform-async-generator-functions": "6.24.1", "babel-plugin-transform-async-to-generator": "6.24.1", "babel-plugin-transform-es2015-arrow-functions": "6.22.0", "babel-plugin-transform-es2015-block-scoped-functions": "6.22.0", "babel-plugin-transform-es2015-block-scoping": "6.24.1", "babel-plugin-transform-es2015-classes": "6.24.1", "babel-plugin-transform-es2015-computed-properties": "6.24.1", "babel-plugin-transform-es2015-destructuring": "6.23.0", "babel-plugin-transform-es2015-duplicate-keys": "6.24.1", "babel-plugin-transform-es2015-for-of": "6.23.0", "babel-plugin-transform-es2015-function-name": "6.24.1", "babel-plugin-transform-es2015-literals": "6.22.0", "babel-plugin-transform-es2015-object-super": "6.24.1", "babel-plugin-transform-es2015-parameters": "6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "6.24.1", "babel-plugin-transform-es2015-spread": "6.22.0", "babel-plugin-transform-es2015-sticky-regex": "6.24.1", "babel-plugin-transform-es2015-template-literals": "6.22.0", "babel-plugin-transform-es2015-unicode-regex": "6.24.1", "babel-plugin-transform-es5-property-mutators": "6.24.1", "babel-plugin-transform-exponentiation-operator": "6.24.1", "babel-plugin-transform-object-rest-spread": "6.23.0", "buddy": "6.x.x", "expect.js": "*", "mocha": "*"}, "main": "src/index.js", "repository": "https://github.com/YR/monotone-cubic-spline.git", "license": "MIT", "scripts": {"prepublish": "buddy build", "test": "NODE_ENV=test mocha test/lib-test.js --reporter spec"}, "browser": "index.js", "buddy": {"build": [{"input": "src/", "output": ".", "bundle": false, "version": "es5"}, {"input": "src/index.js", "output": "test/lib.js"}]}}