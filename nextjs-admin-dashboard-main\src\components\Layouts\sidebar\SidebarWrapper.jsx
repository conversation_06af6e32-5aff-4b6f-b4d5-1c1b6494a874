"use client";

import { useAuth } from "@/contexts/AuthContext";
import { UserSidebar } from "./UserSidebar";
import { BusinessOwnerSidebar } from "./BusinessOwnerSidebar";
import { AdminSidebar } from "./AdminSidebar";
import { useEffect, useState } from "react";

// Loading skeleton for sidebar
function SidebarSkeleton() {
  return (
    <aside className="max-w-[290px] w-full overflow-hidden border-r border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-dark sticky top-0 h-screen">
      <div className="flex h-full flex-col py-10 pl-[25px] pr-[7px]">
        {/* Logo skeleton */}
        <div className="relative pr-4.5 mb-6">
          <div className="h-8 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>

        {/* Navigation skeleton */}
        <div className="flex-1 space-y-6">
          {/* Section 1 */}
          <div>
            <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
            <div className="space-y-2">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="flex items-center gap-3 p-3">
                  <div className="h-6 w-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Section 2 */}
          <div>
            <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
            <div className="space-y-2">
              {[1, 2].map((item) => (
                <div key={item} className="flex items-center gap-3 p-3">
                  <div className="h-6 w-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
}

export function SidebarWrapper() {
  const { user, loading } = useAuth();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Show skeleton while loading or not mounted (SSR)
  if (!mounted || loading) {
    return <SidebarSkeleton />;
  }

  // If no user is authenticated, don't show sidebar
  if (!user) {
    return null;
  }

  // Render sidebar based on user role
  switch (user.role) {
    case 'admin':
    case 'super_admin':
      return <AdminSidebar />;
    case 'business_owner':
      return <BusinessOwnerSidebar />;
    case 'user':
    default:
      return <UserSidebar />;
  }
}
