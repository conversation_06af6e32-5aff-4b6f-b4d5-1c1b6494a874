import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Service from '@/models/Service';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';
import { sendEmail } from '@/lib/email';

export async function PATCH(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication and admin role
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || (decoded.role !== 'admin' && decoded.role !== 'super_admin')) {
      return apiResponse.error('Admin access required', 403);
    }
    
    const serviceId = params.id;
    const { status } = await request.json();
    
    // Validate status
    if (!['active', 'inactive'].includes(status)) {
      return apiResponse.error('Invalid status. Must be "active" or "inactive"', 400);
    }
    
    // Find the service
    const service = await Service.findById(serviceId)
      .populate('businessOwner', 'businessName ownerFirstName ownerLastName email');
    
    if (!service) {
      return apiResponse.error('Service not found', 404);
    }
    
    // Update service status
    service.status = status;
    service.updatedAt = new Date();
    
    await service.save();
    
    // Send email notification to business owner
    try {
      const statusMessage = status === 'active' 
        ? 'Your service has been approved and is now active on the platform.'
        : 'Your service has been temporarily deactivated by our admin team.';
      
      await sendEmail({
        to: service.businessOwner.email,
        subject: `Service ${status === 'active' ? 'Approved' : 'Deactivated'} - ${service.title}`,
        html: `
          <h2>Service Status Update</h2>
          <p>Dear ${service.businessOwner.ownerFirstName},</p>
          <p>${statusMessage}</p>
          
          <h3>Service Details:</h3>
          <p><strong>Service:</strong> ${service.title}</p>
          <p><strong>Category:</strong> ${service.category}</p>
          <p><strong>Price:</strong> $${service.price}</p>
          <p><strong>Status:</strong> ${status.toUpperCase()}</p>
          
          ${status === 'active' ? `
            <p>Your service is now visible to customers and available for booking.</p>
          ` : `
            <p>If you have questions about this decision, please contact our support team.</p>
          `}
          
          <p>You can manage your services in your business dashboard.</p>
          
          <p>Best regards,<br>The BookMyService Team</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send service status update email:', emailError);
      // Don't fail the request if email fails
    }
    
    return apiResponse.success(
      service, 
      `Service ${status === 'active' ? 'activated' : 'deactivated'} successfully`
    );
    
  } catch (error) {
    console.error('Error updating service status:', error);
    return apiResponse.error('Failed to update service status', 500);
  }
}
