"use client";

import { useAuth } from "@/contexts/AuthContext";
import { SidebarWrapper } from "./sidebar/SidebarWrapper";
import { Header } from "./header";
import { PublicHeader } from "./PublicHeader";
import { useEffect, useState } from "react";

export function ConditionalLayout({ children }) {
  const { user, loading } = useAuth();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Show loading state while checking authentication
  if (!mounted || loading) {
    return (
      <div className="flex min-h-screen">
        {/* Sidebar skeleton */}
        <aside className="max-w-[290px] w-full overflow-hidden border-r border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-dark sticky top-0 h-screen">
          <div className="flex h-full flex-col py-10 pl-[25px] pr-[7px]">
            <div className="relative pr-4.5 mb-6">
              <div className="h-8 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
            <div className="flex-1 space-y-6">
              <div>
                <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
                <div className="space-y-2">
                  {[1, 2, 3, 4].map((item) => (
                    <div key={item} className="flex items-center gap-3 p-3">
                      <div className="h-6 w-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </aside>

        {/* Main content skeleton */}
        <div className="w-full bg-gray-2 dark:bg-[#020d1a]">
          <div className="h-16 bg-white dark:bg-gray-dark border-b border-gray-200 dark:border-gray-700 animate-pulse"></div>
          <main className="isolate mx-auto w-full max-w-screen-2xl overflow-hidden p-4 md:p-6 2xl:p-10">
            <div className="space-y-6">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 animate-pulse"></div>
              <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  // If user is authenticated, show the full dashboard layout
  if (user) {
    return (
      <div className="flex min-h-screen">
        <SidebarWrapper />
        <div className="w-full bg-gray-2 dark:bg-[#020d1a]">
          <Header />
          <main className="isolate mx-auto w-full max-w-screen-2xl overflow-hidden p-4 md:p-6 2xl:p-10">
            {children}
          </main>
        </div>
      </div>
    );
  }

  // If user is not authenticated, show public layout (no sidebar)
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <PublicHeader />
      <main className="mx-auto w-full max-w-screen-2xl">
        {children}
      </main>
    </div>
  );
}
