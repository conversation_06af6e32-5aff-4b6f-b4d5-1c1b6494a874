/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/react-toastify/dist/ReactToastify.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
:root {
  --toastify-color-light: #fff;
  --toastify-color-dark: #121212;
  --toastify-color-info: #3498db;
  --toastify-color-success: #07bc0c;
  --toastify-color-warning: #f1c40f;
  --toastify-color-error: hsl(6, 78%, 57%);
  --toastify-color-transparent: rgba(255, 255, 255, 0.7);

  --toastify-icon-color-info: var(--toastify-color-info);
  --toastify-icon-color-success: var(--toastify-color-success);
  --toastify-icon-color-warning: var(--toastify-color-warning);
  --toastify-icon-color-error: var(--toastify-color-error);

  --toastify-container-width: fit-content;
  --toastify-toast-width: 320px;
  --toastify-toast-offset: 16px;
  --toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));
  --toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));
  --toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));
  --toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));
  --toastify-toast-background: #fff;
  --toastify-toast-padding: 14px;
  --toastify-toast-min-height: 64px;
  --toastify-toast-max-height: 800px;
  --toastify-toast-bd-radius: 6px;
  --toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  --toastify-font-family: sans-serif;
  --toastify-z-index: 9999;
  --toastify-text-color-light: #757575;
  --toastify-text-color-dark: #fff;

  /* Used only for colored theme */
  --toastify-text-color-info: #fff;
  --toastify-text-color-success: #fff;
  --toastify-text-color-warning: #fff;
  --toastify-text-color-error: #fff;

  --toastify-spinner-color: #616161;
  --toastify-spinner-color-empty-area: #e0e0e0;
  --toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);
  --toastify-color-progress-dark: #bb86fc;
  --toastify-color-progress-info: var(--toastify-color-info);
  --toastify-color-progress-success: var(--toastify-color-success);
  --toastify-color-progress-warning: var(--toastify-color-warning);
  --toastify-color-progress-error: var(--toastify-color-error);
  /* used to control the opacity of the progress trail */
  --toastify-color-progress-bgo: 0.2;
}

.Toastify__toast-container {
  z-index: var(--toastify-z-index);
  -webkit-transform: translate3d(0, 0, var(--toastify-z-index));
  position: fixed;
  width: var(--toastify-container-width);
  box-sizing: border-box;
  color: #fff;
  display: flex;
  flex-direction: column;
}

.Toastify__toast-container--top-left {
  top: var(--toastify-toast-top);
  left: var(--toastify-toast-left);
}
.Toastify__toast-container--top-center {
  top: var(--toastify-toast-top);
  left: 50%;
  transform: translateX(-50%);
  align-items: center;
}
.Toastify__toast-container--top-right {
  top: var(--toastify-toast-top);
  right: var(--toastify-toast-right);
  align-items: end;
}
.Toastify__toast-container--bottom-left {
  bottom: var(--toastify-toast-bottom);
  left: var(--toastify-toast-left);
}
.Toastify__toast-container--bottom-center {
  bottom: var(--toastify-toast-bottom);
  left: 50%;
  transform: translateX(-50%);
  align-items: center;
}
.Toastify__toast-container--bottom-right {
  bottom: var(--toastify-toast-bottom);
  right: var(--toastify-toast-right);
  align-items: end;
}

.Toastify__toast {
  --y: 0;
  position: relative;
  touch-action: none;
  width: var(--toastify-toast-width);
  min-height: var(--toastify-toast-min-height);
  box-sizing: border-box;
  margin-bottom: 1rem;
  padding: var(--toastify-toast-padding);
  border-radius: var(--toastify-toast-bd-radius);
  box-shadow: var(--toastify-toast-shadow);
  max-height: var(--toastify-toast-max-height);
  font-family: var(--toastify-font-family);
  /* webkit only issue #791 */
  z-index: 0;
  /* inner swag */
  display: flex;
  flex: 1 auto;
  align-items: center;
  word-break: break-word;
}

@media only screen and (max-width: 480px) {
  .Toastify__toast-container {
    width: 100vw;
    left: env(safe-area-inset-left);
    margin: 0;
  }
  .Toastify__toast-container--top-left,
  .Toastify__toast-container--top-center,
  .Toastify__toast-container--top-right {
    top: env(safe-area-inset-top);
    transform: translateX(0);
  }
  .Toastify__toast-container--bottom-left,
  .Toastify__toast-container--bottom-center,
  .Toastify__toast-container--bottom-right {
    bottom: env(safe-area-inset-bottom);
    transform: translateX(0);
  }
  .Toastify__toast-container--rtl {
    right: env(safe-area-inset-right);
    left: initial;
  }
  .Toastify__toast {
    --toastify-toast-width: 100%;
    margin-bottom: 0;
    border-radius: 0;
  }
}

.Toastify__toast-container[data-stacked='true'] {
  width: var(--toastify-toast-width);
}

.Toastify__toast--stacked {
  position: absolute;
  width: 100%;
  transform: translate3d(0, var(--y), 0) scale(var(--s));
  transition: transform 0.3s;
}

.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,
.Toastify__toast--stacked[data-collapsed] .Toastify__close-button {
  transition: opacity 0.1s;
}

.Toastify__toast--stacked[data-collapsed='false'] {
  overflow: visible;
}

.Toastify__toast--stacked[data-collapsed='true']:not(:last-child) > * {
  opacity: 0;
}

.Toastify__toast--stacked:after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: calc(var(--g) * 1px);
  bottom: 100%;
}

.Toastify__toast--stacked[data-pos='top'] {
  top: 0;
}

.Toastify__toast--stacked[data-pos='bot'] {
  bottom: 0;
}

.Toastify__toast--stacked[data-pos='bot'].Toastify__toast--stacked:before {
  transform-origin: top;
}

.Toastify__toast--stacked[data-pos='top'].Toastify__toast--stacked:before {
  transform-origin: bottom;
}

.Toastify__toast--stacked:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  transform: scaleY(3);
  z-index: -1;
}

.Toastify__toast--rtl {
  direction: rtl;
}

.Toastify__toast--close-on-click {
  cursor: pointer;
}

.Toastify__toast-icon {
  margin-inline-end: 10px;
  width: 22px;
  flex-shrink: 0;
  display: flex;
}

.Toastify--animate {
  animation-fill-mode: both;
  animation-duration: 0.5s;
}

.Toastify--animate-icon {
  animation-fill-mode: both;
  animation-duration: 0.3s;
}

.Toastify__toast-theme--dark {
  background: var(--toastify-color-dark);
  color: var(--toastify-text-color-dark);
}

.Toastify__toast-theme--light {
  background: var(--toastify-color-light);
  color: var(--toastify-text-color-light);
}

.Toastify__toast-theme--colored.Toastify__toast--default {
  background: var(--toastify-color-light);
  color: var(--toastify-text-color-light);
}

.Toastify__toast-theme--colored.Toastify__toast--info {
  color: var(--toastify-text-color-info);
  background: var(--toastify-color-info);
}

.Toastify__toast-theme--colored.Toastify__toast--success {
  color: var(--toastify-text-color-success);
  background: var(--toastify-color-success);
}

.Toastify__toast-theme--colored.Toastify__toast--warning {
  color: var(--toastify-text-color-warning);
  background: var(--toastify-color-warning);
}

.Toastify__toast-theme--colored.Toastify__toast--error {
  color: var(--toastify-text-color-error);
  background: var(--toastify-color-error);
}

.Toastify__progress-bar-theme--light {
  background: var(--toastify-color-progress-light);
}

.Toastify__progress-bar-theme--dark {
  background: var(--toastify-color-progress-dark);
}

.Toastify__progress-bar--info {
  background: var(--toastify-color-progress-info);
}

.Toastify__progress-bar--success {
  background: var(--toastify-color-progress-success);
}

.Toastify__progress-bar--warning {
  background: var(--toastify-color-progress-warning);
}

.Toastify__progress-bar--error {
  background: var(--toastify-color-progress-error);
}

.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,
.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,
.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,
.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error {
  background: var(--toastify-color-transparent);
}

.Toastify__close-button {
  color: #fff;
  position: absolute;
  top: 6px;
  right: 6px;
  background: transparent;
  outline: none;
  border: none;
  padding: 0;
  cursor: pointer;
  opacity: 0.7;
  transition: 0.3s ease;
  z-index: 1;
}

.Toastify__toast--rtl .Toastify__close-button {
  left: 6px;
  right: unset;
}

.Toastify__close-button--light {
  color: #000;
  opacity: 0.3;
}

.Toastify__close-button > svg {
  fill: currentColor;
  height: 16px;
  width: 14px;
}

.Toastify__close-button:hover,
.Toastify__close-button:focus {
  opacity: 1;
}

@keyframes Toastify__trackProgress {
  0% {
    transform: scaleX(1);
  }
  100% {
    transform: scaleX(0);
  }
}

.Toastify__progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0.7;
  transform-origin: left;
}

.Toastify__progress-bar--animated {
  animation: Toastify__trackProgress linear 1 forwards;
}

.Toastify__progress-bar--controlled {
  transition: transform 0.2s;
}

.Toastify__progress-bar--rtl {
  right: 0;
  left: initial;
  transform-origin: right;
  border-bottom-left-radius: initial;
}

.Toastify__progress-bar--wrp {
  position: absolute;
  overflow: hidden;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  border-bottom-left-radius: var(--toastify-toast-bd-radius);
  border-bottom-right-radius: var(--toastify-toast-bd-radius);
}

.Toastify__progress-bar--wrp[data-hidden='true'] {
  opacity: 0;
}

.Toastify__progress-bar--bg {
  opacity: var(--toastify-color-progress-bgo);
  width: 100%;
  height: 100%;
}

.Toastify__spinner {
  width: 20px;
  height: 20px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: var(--toastify-spinner-color-empty-area);
  border-right-color: var(--toastify-spinner-color);
  animation: Toastify__spin 0.65s linear infinite;
}

@keyframes Toastify__bounceInRight {
  from,
  60%,
  75%,
  90%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    transform: translate3d(3000px, 0, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(-25px, 0, 0);
  }
  75% {
    transform: translate3d(10px, 0, 0);
  }
  90% {
    transform: translate3d(-5px, 0, 0);
  }
  to {
    transform: none;
  }
}

@keyframes Toastify__bounceOutRight {
  20% {
    opacity: 1;
    transform: translate3d(-20px, var(--y), 0);
  }
  to {
    opacity: 0;
    transform: translate3d(2000px, var(--y), 0);
  }
}

@keyframes Toastify__bounceInLeft {
  from,
  60%,
  75%,
  90%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: translate3d(-3000px, 0, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(25px, 0, 0);
  }
  75% {
    transform: translate3d(-10px, 0, 0);
  }
  90% {
    transform: translate3d(5px, 0, 0);
  }
  to {
    transform: none;
  }
}

@keyframes Toastify__bounceOutLeft {
  20% {
    opacity: 1;
    transform: translate3d(20px, var(--y), 0);
  }
  to {
    opacity: 0;
    transform: translate3d(-2000px, var(--y), 0);
  }
}

@keyframes Toastify__bounceInUp {
  from,
  60%,
  75%,
  90%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    transform: translate3d(0, 3000px, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(0, -20px, 0);
  }
  75% {
    transform: translate3d(0, 10px, 0);
  }
  90% {
    transform: translate3d(0, -5px, 0);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes Toastify__bounceOutUp {
  20% {
    transform: translate3d(0, calc(var(--y) - 10px), 0);
  }
  40%,
  45% {
    opacity: 1;
    transform: translate3d(0, calc(var(--y) + 20px), 0);
  }
  to {
    opacity: 0;
    transform: translate3d(0, -2000px, 0);
  }
}

@keyframes Toastify__bounceInDown {
  from,
  60%,
  75%,
  90%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: translate3d(0, -3000px, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(0, 25px, 0);
  }
  75% {
    transform: translate3d(0, -10px, 0);
  }
  90% {
    transform: translate3d(0, 5px, 0);
  }
  to {
    transform: none;
  }
}

@keyframes Toastify__bounceOutDown {
  20% {
    transform: translate3d(0, calc(var(--y) - 10px), 0);
  }
  40%,
  45% {
    opacity: 1;
    transform: translate3d(0, calc(var(--y) + 20px), 0);
  }
  to {
    opacity: 0;
    transform: translate3d(0, 2000px, 0);
  }
}

.Toastify__bounce-enter--top-left,
.Toastify__bounce-enter--bottom-left {
  animation-name: Toastify__bounceInLeft;
}

.Toastify__bounce-enter--top-right,
.Toastify__bounce-enter--bottom-right {
  animation-name: Toastify__bounceInRight;
}

.Toastify__bounce-enter--top-center {
  animation-name: Toastify__bounceInDown;
}

.Toastify__bounce-enter--bottom-center {
  animation-name: Toastify__bounceInUp;
}

.Toastify__bounce-exit--top-left,
.Toastify__bounce-exit--bottom-left {
  animation-name: Toastify__bounceOutLeft;
}

.Toastify__bounce-exit--top-right,
.Toastify__bounce-exit--bottom-right {
  animation-name: Toastify__bounceOutRight;
}

.Toastify__bounce-exit--top-center {
  animation-name: Toastify__bounceOutUp;
}

.Toastify__bounce-exit--bottom-center {
  animation-name: Toastify__bounceOutDown;
}

@keyframes Toastify__zoomIn {
  from {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}

@keyframes Toastify__zoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    transform: translate3d(0, var(--y), 0) scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}

.Toastify__zoom-enter {
  animation-name: Toastify__zoomIn;
}

.Toastify__zoom-exit {
  animation-name: Toastify__zoomOut;
}

@keyframes Toastify__flipIn {
  from {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    animation-timing-function: ease-in;
  }
  60% {
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  to {
    transform: perspective(400px);
  }
}

@keyframes Toastify__flipOut {
  from {
    transform: translate3d(0, var(--y), 0) perspective(400px);
  }
  30% {
    transform: translate3d(0, var(--y), 0) perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    transform: translate3d(0, var(--y), 0) perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}

.Toastify__flip-enter {
  animation-name: Toastify__flipIn;
}

.Toastify__flip-exit {
  animation-name: Toastify__flipOut;
}

@keyframes Toastify__slideInRight {
  from {
    transform: translate3d(110%, 0, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, var(--y), 0);
  }
}

@keyframes Toastify__slideInLeft {
  from {
    transform: translate3d(-110%, 0, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, var(--y), 0);
  }
}

@keyframes Toastify__slideInUp {
  from {
    transform: translate3d(0, 110%, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, var(--y), 0);
  }
}

@keyframes Toastify__slideInDown {
  from {
    transform: translate3d(0, -110%, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, var(--y), 0);
  }
}

@keyframes Toastify__slideOutRight {
  from {
    transform: translate3d(0, var(--y), 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(110%, var(--y), 0);
  }
}

@keyframes Toastify__slideOutLeft {
  from {
    transform: translate3d(0, var(--y), 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(-110%, var(--y), 0);
  }
}

@keyframes Toastify__slideOutDown {
  from {
    transform: translate3d(0, var(--y), 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(0, 500px, 0);
  }
}

@keyframes Toastify__slideOutUp {
  from {
    transform: translate3d(0, var(--y), 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(0, -500px, 0);
  }
}

.Toastify__slide-enter--top-left,
.Toastify__slide-enter--bottom-left {
  animation-name: Toastify__slideInLeft;
}

.Toastify__slide-enter--top-right,
.Toastify__slide-enter--bottom-right {
  animation-name: Toastify__slideInRight;
}

.Toastify__slide-enter--top-center {
  animation-name: Toastify__slideInDown;
}

.Toastify__slide-enter--bottom-center {
  animation-name: Toastify__slideInUp;
}

.Toastify__slide-exit--top-left,
.Toastify__slide-exit--bottom-left {
  animation-name: Toastify__slideOutLeft;
  animation-timing-function: ease-in;
  animation-duration: 0.3s;
}

.Toastify__slide-exit--top-right,
.Toastify__slide-exit--bottom-right {
  animation-name: Toastify__slideOutRight;
  animation-timing-function: ease-in;
  animation-duration: 0.3s;
}

.Toastify__slide-exit--top-center {
  animation-name: Toastify__slideOutUp;
  animation-timing-function: ease-in;
  animation-duration: 0.3s;
}

.Toastify__slide-exit--bottom-center {
  animation-name: Toastify__slideOutDown;
  animation-timing-function: ease-in;
  animation-duration: 0.3s;
}

@keyframes Toastify__spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/flatpickr/dist/flatpickr.min.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,0.08);box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,0.08)}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible}.flatpickr-calendar.open{display:inline-block;z-index:99999}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown 300ms cubic-bezier(.23,1,.32,1);animation:fpFadeInDown 300ms cubic-bezier(.23,1,.32,1)}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{z-index:999;display:block}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none !important;box-shadow:none !important}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:'';height:0;width:0;left:22px}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%}.flatpickr-calendar:before{border-width:5px;margin:0 -5px}.flatpickr-calendar:after{border-width:4px;margin:0 -4px}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff}.flatpickr-calendar:focus{outline:0}.flatpickr-wrapper{position:relative;display:inline-block}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-months .flatpickr-month{background:transparent;color:rgba(0,0,0,0.9);fill:rgba(0,0,0,0.9);height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:rgba(0,0,0,0.9);fill:rgba(0,0,0,0.9)}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{/*
      /*rtl:begin:ignore*/left:0/*
      /*rtl:end:ignore*/}/*
      /*rtl:begin:ignore*/
/*
      /*rtl:end:ignore*/
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{/*
      /*rtl:begin:ignore*/right:0/*
      /*rtl:end:ignore*/}/*
      /*rtl:begin:ignore*/
/*
      /*rtl:end:ignore*/
.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill .1s;transition:fill .1s;fill:inherit}.numInputWrapper{position:relative;height:auto}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper input::-ms-clear{display:none}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57,57,57,0.15);-webkit-box-sizing:border-box;box-sizing:border-box}.numInputWrapper span:hover{background:rgba(0,0,0,0.1)}.numInputWrapper span:active{background:rgba(0,0,0,0.2)}.numInputWrapper span:after{display:block;content:"";position:absolute}.numInputWrapper span.arrowUp{top:0;border-bottom:0}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57,57,57,0.6);top:26%}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57,57,57,0.6);top:40%}.numInputWrapper span svg{width:inherit;height:auto}.numInputWrapper span svg path{fill:rgba(0,0,0,0.5)}.numInputWrapper:hover{background:rgba(0,0,0,0.05)}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:.5ch;padding:0}.flatpickr-current-month span.cur-month:hover{background:rgba(0,0,0,0.05)}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\0;display:inline-block}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:rgba(0,0,0,0.9)}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:rgba(0,0,0,0.9)}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 .5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:rgba(0,0,0,0.5);background:transparent;pointer-events:none}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0 0;outline:none;padding:0 0 0 .5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:rgba(0,0,0,0.05)}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:rgba(0,0,0,0.54);line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder}.dayContainer,.flatpickr-weeks{padding:1px 0 0 0}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px}.flatpickr-days:focus{outline:0}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0);opacity:1}.dayContainer + .dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 0 #e6e6e6}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6}.flatpickr-day.today{border-color:#959ea9}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0}.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 0 #569ff7}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:rgba(57,57,57,0.3);background:transparent;border-color:transparent;cursor:default}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:rgba(57,57,57,0.1)}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7;box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7}.flatpickr-day.hidden{visibility:hidden}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{float:left}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 0 #e6e6e6}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:rgba(57,57,57,0.3);background:transparent;cursor:default;border:none}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-time:after{content:"";display:table;clear:both}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-time input.flatpickr-hour{font-weight:bold}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{outline:0;border:0}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:bold;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{from{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}@keyframes fpFadeInDown{from{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/jsvectormap/dist/jsvectormap.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
svg {
  touch-action: none;
}

image, text, .jvm-zoomin, .jvm-zoomout {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.jvm-container {
  touch-action: none;
  position: relative;
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.jvm-tooltip {
  border-radius: 3px;
  background-color: #5c5cff;
  font-family: sans-serif, Verdana;
  font-size: smaller;
  box-shadow: 1px 2px 12px rgba(0, 0, 0, 0.2);
  padding: 3px 5px;
  white-space: nowrap;
  position: absolute;
  display: none;
  color: #FFF;
}
.jvm-tooltip.active {
  display: block;
}

.jvm-zoom-btn {
  border-radius: 3px;
  background-color: #292929;
  padding: 3px;
  box-sizing: border-box;
  position: absolute;
  line-height: 10px;
  cursor: pointer;
  color: #FFF;
  height: 15px;
  width: 15px;
  left: 10px;
}
.jvm-zoom-btn.jvm-zoomout {
  top: 30px;
}
.jvm-zoom-btn.jvm-zoomin {
  top: 10px;
}

.jvm-series-container {
  right: 15px;
  position: absolute;
}
.jvm-series-container.jvm-series-h {
  bottom: 15px;
}
.jvm-series-container.jvm-series-v {
  top: 15px;
}
.jvm-series-container .jvm-legend {
  background-color: #FFF;
  border: 1px solid #e5e7eb;
  margin-left: 0.75rem;
  border-radius: 0.25rem;
  border-color: #e5e7eb;
  padding: 0.6rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  float: left;
}
.jvm-series-container .jvm-legend .jvm-legend-title {
  line-height: 1;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
  margin-bottom: 0.575rem;
  text-align: left;
}
.jvm-series-container .jvm-legend .jvm-legend-inner {
  overflow: hidden;
}
.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick {
  overflow: hidden;
  min-width: 40px;
}
.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick:not(:first-child) {
  margin-top: 0.575rem;
}
.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-sample {
  border-radius: 4px;
  margin-right: 0.65rem;
  height: 16px;
  width: 16px;
  float: left;
}
.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-text {
  font-size: 12px;
  text-align: center;
  float: left;
}

.jvm-line[animation=true] {
  animation: jvm-line-animation 10s linear forwards infinite;
}
@keyframes jvm-line-animation {
  from {
    stroke-dashoffset: 250;
  }
}
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/css/satoshi.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/**
 * @license
 *
 * Font Family: Satoshi
 * Designed by: Deni Anggara
 * URL: https://www.fontshare.com/fonts/satoshi
 * © 2023 Indian Type Foundry
 *
 * Font Styles:
 * Satoshi Light
 * Satoshi Light Italic
 * Satoshi Regular
 * Satoshi Italic
 * Satoshi Medium
 * Satoshi Medium Italic
 * Satoshi Bold
 * Satoshi Bold Italic
 * Satoshi Black
 * Satoshi Black Italic
 *
*/

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Light.d3f699ab.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Light.ce217c5d.woff) format("woff"),
    url(/_next/static/media/Satoshi-Light.121b151d.ttf) format("truetype");
    font-weight: 300;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-LightItalic.0d87c97a.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-LightItalic.51efbee6.woff) format("woff"),
    url(/_next/static/media/Satoshi-LightItalic.58b0e971.ttf) format("truetype");
    font-weight: 300;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Regular.b1dca2a5.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Regular.bb2accee.woff) format("woff"),
    url(/_next/static/media/Satoshi-Regular.a12eb4fb.ttf) format("truetype");
    font-weight: 400;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Italic.3eb4bb53.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Italic.43440d31.woff) format("woff"),
    url(/_next/static/media/Satoshi-Italic.84cd9c1d.ttf) format("truetype");
    font-weight: 400;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Medium.22539d17.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Medium.f3941e68.woff) format("woff"),
    url(/_next/static/media/Satoshi-Medium.8217b72e.ttf) format("truetype");
    font-weight: 500;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-MediumItalic.17afee50.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-MediumItalic.5450477c.woff) format("woff"),
    url(/_next/static/media/Satoshi-MediumItalic.14c46485.ttf) format("truetype");
    font-weight: 500;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Bold.12084922.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Bold.b28a04c4.woff) format("woff"),
    url(/_next/static/media/Satoshi-Bold.c60efc8f.ttf) format("truetype");
    font-weight: 700;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-BoldItalic.e51fcc53.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-BoldItalic.b59cf06f.woff) format("woff"),
    url(/_next/static/media/Satoshi-BoldItalic.c1d97e57.ttf) format("truetype");
    font-weight: 700;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Black.c6d20a6b.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Black.28873509.woff) format("woff"),
    url(/_next/static/media/Satoshi-Black.12d5a2e3.ttf) format("truetype");
    font-weight: 900;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-BlackItalic.5400951d.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-BlackItalic.22c3e8d9.woff) format("woff"),
    url(/_next/static/media/Satoshi-BlackItalic.33bc16b8.ttf) format("truetype");
    font-weight: 900;
    font-display: swap;
    font-style: italic;
}

/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/css/style.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: "Satoshi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
  body {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
  body:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(2 13 26 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.invisible {
  visibility: hidden;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.\!right-0 {
  right: 0px !important;
}
.\!right-1 {
  right: 0.25rem !important;
}
.-top-1 {
  top: -0.25rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-1 {
  bottom: 0.25rem;
}
.left-0 {
  left: 0px;
}
.left-1 {
  left: 0.25rem;
}
.left-1\/2 {
  left: 50%;
}
.left-2 {
  left: 0.5rem;
}
.left-3\/4 {
  left: 75%;
}
.left-4 {
  left: 1rem;
}
.left-5 {
  left: 1.25rem;
}
.left-auto {
  left: auto;
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-4 {
  right: 1rem;
}
.right-4\.5 {
  right: 1.125rem;
}
.right-5 {
  right: 1.25rem;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\/2 {
  top: 50%;
}
.top-full {
  top: 100%;
}
.isolate {
  isolation: isolate;
}
.-z-1 {
  z-index: -1;
}
.z-1 {
  z-index: 1;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-99 {
  z-index: 99;
}
.z-\[9999\] {
  z-index: 9999;
}
.col-span-12 {
  grid-column: span 12 / span 12;
}
.col-span-5 {
  grid-column: span 5 / span 5;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.-ml-3\.5 {
  margin-left: -0.875rem;
}
.-ml-4 {
  margin-left: -1rem;
}
.-mr-5 {
  margin-right: -1.25rem;
}
.-mt-22 {
  margin-top: -5.5rem;
}
.mb-0\.5 {
  margin-bottom: 0.125rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-1\.5 {
  margin-bottom: 0.375rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-3\.5 {
  margin-bottom: 0.875rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-4\.5 {
  margin-bottom: 1.125rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-5\.5 {
  margin-bottom: 1.375rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-7 {
  margin-bottom: 1.75rem;
}
.mb-7\.5 {
  margin-bottom: 1.875rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-9 {
  margin-left: 2.25rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-0 {
  margin-right: 0px;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-auto {
  margin-right: auto;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-2\.5 {
  margin-top: 0.625rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-31 {
  margin-top: 7.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-4\.5 {
  margin-top: 1.125rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-\[3px\] {
  margin-top: 3px;
}
.\!block {
  display: block !important;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-\[6\/5\] {
  aspect-ratio: 6/5;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.size-12 {
  width: 3rem;
  height: 3rem;
}
.size-13\.5 {
  width: 3.375rem;
  height: 3.375rem;
}
.size-14 {
  width: 3.5rem;
  height: 3.5rem;
}
.size-2 {
  width: 0.5rem;
  height: 0.5rem;
}
.size-2\.5 {
  width: 0.625rem;
  height: 0.625rem;
}
.size-3\.5 {
  width: 0.875rem;
  height: 0.875rem;
}
.size-4 {
  width: 1rem;
  height: 1rem;
}
.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}
.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}
.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}
.size-8 {
  width: 2rem;
  height: 2rem;
}
.size-8\.5 {
  width: 2.125rem;
  height: 2.125rem;
}
.size-\[38px\] {
  width: 38px;
  height: 38px;
}
.h-11\.5 {
  height: 2.875rem;
}
.h-12 {
  height: 3rem;
}
.h-15 {
  height: 3.75rem;
}
.h-16 {
  height: 4rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-30 {
  height: 7.5rem;
}
.h-32 {
  height: 8rem;
}
.h-35 {
  height: 8.75rem;
}
.h-4 {
  height: 1rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-96 {
  height: 24rem;
}
.h-\[310px\] {
  height: 310px;
}
.h-\[422px\] {
  height: 422px;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-\[23rem\] {
  max-height: 23rem;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-11\.5 {
  width: 2.875rem;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-15 {
  width: 3.75rem;
}
.w-16 {
  width: 4rem;
}
.w-18 {
  width: 4.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-32 {
  width: 8rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-8 {
  width: 2rem;
}
.w-\[200\%\] {
  width: 200%;
}
.w-\[300\%\] {
  width: 300%;
}
.w-full {
  width: 100%;
}
.w-1\/2 {
  width: 50%;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-3\/4 {
  width: 75%;
}
.w-48 {
  width: 12rem;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-\[120px\] {
  min-width: 120px;
}
.min-w-\[155px\] {
  min-width: 155px;
}
.min-w-\[7rem\] {
  min-width: 7rem;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-fit {
  min-width: -moz-fit-content;
  min-width: fit-content;
}
.max-w-30 {
  max-width: 7.5rem;
}
.max-w-\[10\.847rem\] {
  max-width: 10.847rem;
}
.max-w-\[1080px\] {
  max-width: 1080px;
}
.max-w-\[290px\] {
  max-width: 290px;
}
.max-w-\[300px\] {
  max-width: 300px;
}
.max-w-\[370px\] {
  max-width: 370px;
}
.max-w-\[375px\] {
  max-width: 375px;
}
.max-w-\[720px\] {
  max-width: 720px;
}
.max-w-\[970px\] {
  max-width: 970px;
}
.max-w-fit {
  max-width: -moz-fit-content;
  max-width: fit-content;
}
.max-w-full {
  max-width: 100%;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-screen-2xl {
  max-width: 1536px;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-lg {
  max-width: 32rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-auto {
  flex: 1 1 auto;
}
.flex-initial {
  flex: 0 1 auto;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.grow {
  flex-grow: 1;
}
.caption-bottom {
  caption-side: bottom;
}
.origin-top-right {
  transform-origin: top right;
}
.\!translate-x-full {
  --tw-translate-x: 100% !important;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-6 {
  --tw-rotate: -6deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-y-\[-1\] {
  --tw-scale-y: -1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-rows-\[auto_1fr\] {
  grid-template-rows: auto 1fr;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.place-items-center {
  place-items: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-2\.5 {
  gap: 0.625rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-3\.5 {
  gap: 0.875rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-4\.5 {
  gap: 1.125rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-5\.5 {
  gap: 1.375rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-9 {
  gap: 2.25rem;
}
.gap-x-1 {
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}
.gap-x-3\.5 {
  -moz-column-gap: 0.875rem;
       column-gap: 0.875rem;
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.375rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-7\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.875rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.875rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-stroke > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-divide-opacity, 1));
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-\[10px\] {
  border-radius: 10px;
}
.rounded-\[5px\] {
  border-radius: 5px;
}
.rounded-\[7px\] {
  border-radius: 7px;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-r-\[5px\] {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.rounded-t {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.rounded-t-\[10px\] {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.rounded-bl-\[10px\] {
  border-bottom-left-radius: 10px;
}
.rounded-br-\[10px\] {
  border-bottom-right-radius: 10px;
}
.rounded-tl-\[10px\] {
  border-top-left-radius: 10px;
}
.rounded-tr-\[10px\] {
  border-top-right-radius: 10px;
}
.\!border-4 {
  border-width: 4px !important;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-\[\.5px\] {
  border-width: .5px;
}
.border-\[1\.5px\] {
  border-width: 1.5px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-l-2 {
  border-left-width: 2px;
}
.border-l-6 {
  border-left-width: 6px;
}
.border-l-\[3px\] {
  border-left-width: 3px;
}
.border-r {
  border-right-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-solid {
  border-style: solid;
}
.border-dashed {
  border-style: dashed;
}
.border-none {
  border-style: none;
}
.border-\[\#E8E8E8\] {
  --tw-border-opacity: 1;
  border-color: rgb(232 232 232 / var(--tw-border-opacity, 1));
}
.border-\[\#FFB800\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 184 0 / var(--tw-border-opacity, 1));
}
.border-\[\#eee\] {
  --tw-border-opacity: 1;
  border-color: rgb(238 238 238 / var(--tw-border-opacity, 1));
}
.border-dark {
  --tw-border-opacity: 1;
  border-color: rgb(17 25 40 / var(--tw-border-opacity, 1));
}
.border-dark-5 {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-4 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-green {
  --tw-border-opacity: 1;
  border-color: rgb(34 173 92 / var(--tw-border-opacity, 1));
}
.border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}
.border-red-light {
  --tw-border-opacity: 1;
  border-color: rgb(245 96 96 / var(--tw-border-opacity, 1));
}
.border-stroke {
  --tw-border-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-current {
  border-color: currentColor;
}
.border-t-transparent {
  border-top-color: transparent;
}
.\!bg-primary {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1)) !important;
}
.bg-\[\#212B36\] {
  --tw-bg-opacity: 1;
  background-color: rgb(33 43 54 / var(--tw-bg-opacity, 1));
}
.bg-\[\#219653\]\/\[0\.08\] {
  background-color: rgb(33 150 83 / 0.08);
}
.bg-\[\#D34053\]\/\[0\.08\] {
  background-color: rgb(211 64 83 / 0.08);
}
.bg-\[\#F7F9FC\] {
  --tw-bg-opacity: 1;
  background-color: rgb(247 249 252 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FEF5DE\] {
  --tw-bg-opacity: 1;
  background-color: rgb(254 245 222 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFA70B\]\/\[0\.08\] {
  background-color: rgb(255 167 11 / 0.08);
}
.bg-\[rgba\(87\2c 80\2c 241\2c 0\.07\)\] {
  background-color: rgba(87,80,241,0.07);
}
.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue\/\[0\.08\] {
  background-color: rgb(60 80 224 / 0.08);
}
.bg-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(17 25 40 / var(--tw-bg-opacity, 1));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-2 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-3 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-green {
  --tw-bg-opacity: 1;
  background-color: rgb(34 173 92 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-light-7 {
  --tw-bg-opacity: 1;
  background-color: rgb(233 251 240 / var(--tw-bg-opacity, 1));
}
.bg-green\/\[0\.08\] {
  background-color: rgb(34 173 92 / 0.08);
}
.bg-neutral-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}
.bg-neutral-100\/50 {
  background-color: rgb(245 245 245 / 0.5);
}
.bg-orange-light {
  --tw-bg-opacity: 1;
  background-color: rgb(245 148 96 / var(--tw-bg-opacity, 1));
}
.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
}
.bg-primary\/\[0\.08\] {
  background-color: rgb(87 80 241 / 0.08);
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-purple\/\[0\.08\] {
  background-color: rgb(139 92 246 / 0.08);
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-light {
  --tw-bg-opacity: 1;
  background-color: rgb(245 96 96 / var(--tw-bg-opacity, 1));
}
.bg-red-light-5 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 235 235 / var(--tw-bg-opacity, 1));
}
.bg-red\/\[0\.08\] {
  background-color: rgb(242 48 48 / 0.08);
}
.bg-stroke {
  --tw-bg-opacity: 1;
  background-color: rgb(230 235 241 / var(--tw-bg-opacity, 1));
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow\/\[0\.08\] {
  background-color: rgb(245 158 11 / 0.08);
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.fill-blue {
  fill: #3C50E0;
}
.fill-current {
  fill: currentColor;
}
.fill-green {
  fill: #22AD5C;
}
.fill-primary {
  fill: #5750F1;
}
.fill-purple {
  fill: #8B5CF6;
}
.fill-red {
  fill: #F23030;
}
.fill-white {
  fill: #FFFFFF;
}
.fill-yellow {
  fill: #F59E0B;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.object-center {
  -o-object-position: center;
     object-position: center;
}
.\!p-6\.5 {
  padding: 1.625rem !important;
}
.\!p-7 {
  padding: 1.75rem !important;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-7\.5 {
  padding: 1.875rem;
}
.p-\[13px\] {
  padding: 13px;
}
.p-\[15px\] {
  padding: 15px;
}
.p-\[5px\] {
  padding: 5px;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-12\.5 {
  padding-left: 3.125rem;
  padding-right: 3.125rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-3\.5 {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-5\.5 {
  padding-left: 1.375rem;
  padding-right: 1.375rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}
.px-7\.5 {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}
.px-\[15px\] {
  padding-left: 15px;
  padding-right: 15px;
}
.px-\[9px\] {
  padding-left: 9px;
  padding-right: 9px;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-5\.5 {
  padding-top: 1.375rem;
  padding-bottom: 1.375rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-\[11px\] {
  padding-top: 11px;
  padding-bottom: 11px;
}
.py-\[3px\] {
  padding-top: 3px;
  padding-bottom: 3px;
}
.py-\[5px\] {
  padding-top: 5px;
  padding-bottom: 5px;
}
.py-\[7px\] {
  padding-top: 7px;
  padding-bottom: 7px;
}
.py-\[9px\] {
  padding-top: 9px;
  padding-bottom: 9px;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pb-\[15px\] {
  padding-bottom: 15px;
}
.pl-1 {
  padding-left: 0.25rem;
}
.pl-11\.5 {
  padding-left: 2.875rem;
}
.pl-12\.5 {
  padding-left: 3.125rem;
}
.pl-13 {
  padding-left: 3.25rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-5 {
  padding-left: 1.25rem;
}
.pl-\[25px\] {
  padding-left: 25px;
}
.pl-\[53px\] {
  padding-left: 53px;
}
.pr-0 {
  padding-right: 0px;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4\.5 {
  padding-right: 1.125rem;
}
.pr-5 {
  padding-right: 1.25rem;
}
.pr-\[7px\] {
  padding-right: 7px;
}
.pt-12\.5 {
  padding-top: 3.125rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-7\.5 {
  padding-top: 1.875rem;
}
.\!text-left {
  text-align: left !important;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.\!text-right {
  text-align: right !important;
}
.text-right {
  text-align: right;
}
.align-middle {
  vertical-align: middle;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-\[26px\] {
  font-size: 26px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-body-2xlg {
  font-size: 22px;
  line-height: 28px;
}
.text-body-sm {
  font-size: 14px;
  line-height: 22px;
}
.text-body-xs {
  font-size: 12px;
  line-height: 20px;
}
.text-heading-3 {
  font-size: 40px;
  line-height: 48px;
}
.text-heading-5 {
  font-size: 28px;
  line-height: 40px;
}
.text-heading-6 {
  font-size: 24px;
  line-height: 30px;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-\[22px\] {
  line-height: 22px;
}
.leading-\[30px\] {
  line-height: 30px;
}
.leading-none {
  line-height: 1;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.text-\[\#004434\] {
  --tw-text-opacity: 1;
  color: rgb(0 68 52 / var(--tw-text-opacity, 1));
}
.text-\[\#111928\] {
  --tw-text-opacity: 1;
  color: rgb(17 25 40 / var(--tw-text-opacity, 1));
}
.text-\[\#219653\] {
  --tw-text-opacity: 1;
  color: rgb(33 150 83 / var(--tw-text-opacity, 1));
}
.text-\[\#4B5563\] {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-\[\#637381\] {
  --tw-text-opacity: 1;
  color: rgb(99 115 129 / var(--tw-text-opacity, 1));
}
.text-\[\#9CA3AF\] {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-\[\#9D5425\] {
  --tw-text-opacity: 1;
  color: rgb(157 84 37 / var(--tw-text-opacity, 1));
}
.text-\[\#BC1C21\] {
  --tw-text-opacity: 1;
  color: rgb(188 28 33 / var(--tw-text-opacity, 1));
}
.text-\[\#CD5D5D\] {
  --tw-text-opacity: 1;
  color: rgb(205 93 93 / var(--tw-text-opacity, 1));
}
.text-\[\#D0915C\] {
  --tw-text-opacity: 1;
  color: rgb(208 145 92 / var(--tw-text-opacity, 1));
}
.text-\[\#D34053\] {
  --tw-text-opacity: 1;
  color: rgb(211 64 83 / var(--tw-text-opacity, 1));
}
.text-\[\#FFA70B\] {
  --tw-text-opacity: 1;
  color: rgb(255 167 11 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-dark {
  --tw-text-opacity: 1;
  color: rgb(17 25 40 / var(--tw-text-opacity, 1));
}
.text-dark-4 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-dark-5 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-dark-6 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-2 {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-6 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green {
  --tw-text-opacity: 1;
  color: rgb(34 173 92 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-green-light-1 {
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}
.text-neutral-500 {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}
.text-primary {
  --tw-text-opacity: 1;
  color: rgb(87 80 241 / var(--tw-text-opacity, 1));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-red {
  --tw-text-opacity: 1;
  color: rgb(242 48 48 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.placeholder-gray-500::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.\!opacity-100 {
  opacity: 1 !important;
}
.opacity-0 {
  opacity: 0;
}
.opacity-75 {
  opacity: 0.75;
}
.shadow-1 {
  --tw-shadow: 0px 1px 2px 0px rgba(84, 87, 118, 0.12);
  --tw-shadow-colored: 0px 1px 2px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-switch-1 {
  --tw-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.10);
  --tw-shadow-colored: 0px 0px 4px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-switch-2 {
  --tw-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.15);
  --tw-shadow-colored: 0px 0px 5px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.outline-1 {
  outline-width: 1px;
}
.outline-primary {
  outline-color: #5750F1;
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-gray-2 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(243 244 246 / var(--tw-ring-opacity, 1));
}
.ring-primary {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(87 80 241 / var(--tw-ring-opacity, 1));
}
.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}
.ring-offset-2 {
  --tw-ring-offset-width: 2px;
}
.ring-offset-white {
  --tw-ring-offset-color: #FFFFFF;
}
.drop-shadow-2 {
  --tw-drop-shadow: drop-shadow(0px 1px 4px rgba(0, 0, 0, 0.12));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\] {
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.ease-linear {
  transition-timing-function: linear;
}
/* Chrome, Safari and Opera */
.custom-scrollbar {
  overflow: auto;
}
.custom-scrollbar::-webkit-scrollbar {
  width: 0.5rem;
  height: 0.5rem;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  position: relative;
  flex: 1 1 0%;
  border-radius: 9999px;
  background-color: rgb(229 229 229 / 0.4);
}
.custom-scrollbar:is(.dark *)::-webkit-scrollbar-thumb {
  --tw-bg-opacity: 1;
  background-color: rgb(39 48 62 / var(--tw-bg-opacity, 1));
}

/* third-party libraries CSS */

.tableCheckbox:checked ~ div span {
  opacity: 1;
}

.tableCheckbox:checked ~ div {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
}

.apexcharts-legend-text {
  --tw-text-opacity: 1 !important;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-legend-text:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-text {
  fill: #6B7280 !important;
}

.apexcharts-text:is(.dark *) {
  fill: #9CA3AF !important;
}

.apexcharts-yaxis-label {
  font-size: 0.75rem;
  line-height: 1rem;
}

.apexcharts-xaxis-label {
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
}

.apexcharts-xcrosshairs {
  fill: #E6EBF1 !important;
}

.apexcharts-xcrosshairs:is(.dark *) {
  fill: #374151 !important;
}

.apexcharts-gridline {
  stroke: #E6EBF1 !important;
}

.apexcharts-gridline:is(.dark *) {
  stroke: #374151 !important;
}

.apexcharts-series.apexcharts-pie-series path:is(.dark *) {
  stroke: transparent !important;
}

.apexcharts-legend-series {
  display: inline-flex !important;
  gap: 0.375rem;
}

.apexcharts-tooltip.apexcharts-theme-light {
  border-radius: 7px !important;
  font-size: 1rem !important;
  line-height: 1.5rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 25 40 / var(--tw-text-opacity, 1)) !important;
  --tw-shadow: 0px 8px 13px -3px rgba(0, 0, 0, 0.07) !important;
  --tw-shadow-colored: 0px 8px 13px -3px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.apexcharts-tooltip.apexcharts-theme-light:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(18 32 49 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1)) !important;
}

.apexcharts-xaxistooltip:is(.dark *),
.apexcharts-yaxistooltip:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(31 42 55 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom:after {
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(239 244 251 / var(--tw-border-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom:is(.dark *):after {
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(17 25 40 / var(--tw-border-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom:before {
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(239 244 251 / var(--tw-border-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom:is(.dark *):before {
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(17 25 40 / var(--tw-border-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom {
  border-radius: 0.25rem !important;
  border-style: none !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 244 251 / var(--tw-bg-opacity, 1)) !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 25 40 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(17 25 40 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-tooltip-series-group {
  padding-bottom: 1px !important;
  padding-left: 0.625rem !important;
  padding-right: 0.875rem !important;
}

.apexcharts-tooltip-series-group .apexcharts-tooltip-text {
  font-size: 1rem !important;
  line-height: 1.5rem !important;
  font-weight: 500 !important;
}

.apexcharts-datalabels-group .apexcharts-datalabel-label {
  fill: #6B7280 !important;
}

.apexcharts-datalabels-group .apexcharts-datalabel-label:is(.dark *) {
  fill: #9CA3AF !important;
}

.apexcharts-datalabels-group .apexcharts-datalabel-value {
  fill: #111928 !important;
}

.apexcharts-datalabels-group .apexcharts-datalabel-value:is(.dark *) {
  fill: #FFFFFF !important;
}

.flatpickr-wrapper {
  width: 100%;
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  fill: #5750F1 !important;
}

.flatpickr-calendar.arrowTop:is(.dark *):before {
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(18 32 49 / var(--tw-border-opacity, 1)) !important;
}

.flatpickr-calendar.arrowTop:is(.dark *):after {
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(18 32 49 / var(--tw-border-opacity, 1)) !important;
}

.flatpickr-calendar {
  padding: 1.5rem !important;
  --tw-shadow: 0px 8px 8.466px 0px rgba(113, 116, 152, 0.05), 0px 8px 16.224px 0px rgba(113, 116, 152, 0.07), 0px 18px 31px 0px rgba(113, 116, 152, 0.10) !important;
  --tw-shadow-colored: 0px 8px 8.466px 0px var(--tw-shadow-color), 0px 8px 16.224px 0px var(--tw-shadow-color), 0px 18px 31px 0px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.flatpickr-calendar:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(18 32 49 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1)) !important;
  --tw-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.12) !important;
  --tw-shadow-colored: 0px 1px 2px 0px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

@media (min-width: 375px) {

  .flatpickr-calendar {
    width: auto !important;
  }
}

.flatpickr-day:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1)) !important;
}

.flatpickr-day:hover:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(31 42 55 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  top: 1.75rem !important;
}

.flatpickr-months .flatpickr-prev-month:is(.dark *),
.flatpickr-months .flatpickr-next-month:is(.dark *) {
  fill: #FFFFFF !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
  left: 1.75rem !important;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
  right: 1.75rem !important;
}

span.flatpickr-weekday:is(.dark *),
.flatpickr-months .flatpickr-month:is(.dark *) {
  fill: #FFFFFF !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.flatpickr-day.inRange {
  box-shadow:
    -5px 0 0 #f3f4f6,
    5px 0 0 #f3f4f6 !important;
}

.flatpickr-day.inRange:is(.dark *) {
  --tw-shadow: -5px 0 0 #1f2a37, 5px 0 0 #1f2a37 !important;
  --tw-shadow-colored: -5px 0 0 var(--tw-shadow-color), 5px 0 0 var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  --tw-border-opacity: 1 !important;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.inRange:is(.dark *),
.flatpickr-day.prevMonthDay.inRange:is(.dark *),
.flatpickr-day.nextMonthDay.inRange:is(.dark *),
.flatpickr-day.today.inRange:is(.dark *),
.flatpickr-day.prevMonthDay.today.inRange:is(.dark *),
.flatpickr-day.nextMonthDay.today.inRange:is(.dark *),
.flatpickr-day:hover:is(.dark *),
.flatpickr-day.prevMonthDay:hover:is(.dark *),
.flatpickr-day.nextMonthDay:hover:is(.dark *),
.flatpickr-day:focus:is(.dark *),
.flatpickr-day.prevMonthDay:focus:is(.dark *),
.flatpickr-day.nextMonthDay:focus:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(31 42 55 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.selected:is(.dark *),
.flatpickr-day.startRange:is(.dark *),
.flatpickr-day.selected:is(.dark *),
.flatpickr-day.endRange:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.flatpickr-day.today {
  border-style: none !important;
}

.flatpickr-day.today:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 25 40 / var(--tw-text-opacity, 1)) !important;
}

.flatpickr-day.today:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: #3c50e0;
  --tw-border-opacity: 1 !important;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.inRange:hover,
.flatpickr-day.startRange.inRange:hover,
.flatpickr-day.endRange.inRange:hover,
.flatpickr-day.selected:focus:hover,
.flatpickr-day.startRange:focus:hover,
.flatpickr-day.endRange:focus:hover,
.flatpickr-day.selected:hover:hover,
.flatpickr-day.startRange:hover:hover,
.flatpickr-day.endRange:hover:hover,
.flatpickr-day.selected.prevMonthDay:hover,
.flatpickr-day.startRange.prevMonthDay:hover,
.flatpickr-day.endRange.prevMonthDay:hover,
.flatpickr-day.selected.nextMonthDay:hover,
.flatpickr-day.startRange.nextMonthDay:hover,
.flatpickr-day.endRange.nextMonthDay:hover {
  --tw-border-opacity: 1 !important;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)) {
  box-shadow: -10px 0 0 #3c50e0;
}

.map-btn .jvm-zoom-btn {
  display: flex;
  height: 1.875rem;
  width: 1.875rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 0px;
  padding-right: 0px;
  padding-bottom: 0.125rem;
  padding-top: 0px;
  font-size: 1.5rem;
  line-height: 2rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.map-btn .jvm-zoom-btn:hover {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.map-btn .jvm-zoom-btn:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.map-btn .jvm-zoom-btn:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.mapOne .jvm-zoom-btn {
  bottom: 0px !important;
  left: auto !important;
  top: auto !important;
}

.mapOne .jvm-zoom-btn.jvm-zoomin {
  right: 2.5rem !important;
}

.mapOne .jvm-zoom-btn.jvm-zoomout {
  right: 0px !important;
}

.mapTwo .jvm-zoom-btn {
  bottom: 0px !important;
  top: auto !important;
}

.mapTwo .jvm-zoom-btn.jvm-zoomin {
  left: 0px !important;
}

.mapTwo .jvm-zoom-btn.jvm-zoomout {
  left: 2.5rem !important;
}

.taskCheckbox:checked ~ .box span {
  opacity: 1;
}

.taskCheckbox:checked ~ p {
  text-decoration-line: line-through;
}

.taskCheckbox:checked ~ .box {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
}

.taskCheckbox:checked ~ .box:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.custom-input-date::-webkit-calendar-picker-indicator {
  background: transparent;
}

.data-stats-slider-outer .swiper-button-next:after,
.data-stats-slider-outer .swiper-button-prev:after,
.carouselOne .swiper-button-next:after,
.carouselOne .swiper-button-prev:after,
.carouselThree .swiper-button-next:after,
.carouselThree .swiper-button-prev:after {
  display: none;
}

.data-stats-slider-outer .swiper-button-next svg,
.data-stats-slider-outer .swiper-button-prev svg,
.carouselOne .swiper-button-next svg,
.carouselOne .swiper-button-prev svg,
.carouselThree .swiper-button-next svg,
.carouselThree .swiper-button-prev svg {
  width: auto;
  height: auto;
}

.carouselOne .swiper-button-next,
.carouselOne .swiper-button-prev,
.carouselThree .swiper-button-next,
.carouselThree .swiper-button-prev {
  height: 2.5rem;
  width: 2.5rem;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1 !important;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1)) !important;
  --tw-shadow: 0px 8px 13px -3px rgba(0, 0, 0, 0.07);
  --tw-shadow-colored: 0px 8px 13px -3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.carouselOne .swiper-button-next:is(.dark *),
.carouselOne .swiper-button-prev:is(.dark *),
.carouselThree .swiper-button-next:is(.dark *),
.carouselThree .swiper-button-prev:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1)) !important;
}

@media (min-width: 640px) {

  .carouselOne .swiper-button-next,
.carouselOne .swiper-button-prev,
.carouselThree .swiper-button-next,
.carouselThree .swiper-button-prev {
    height: 3.125rem;
    width: 3.125rem;
  }

  .carouselOne .swiper-button-prev,
.carouselThree .swiper-button-prev {
    left: 2.5rem !important;
  }

  .carouselOne .swiper-button-next,
.carouselThree .swiper-button-next {
    right: 2.5rem !important;
  }
}

.carouselTwo .swiper-pagination-bullet,
.carouselThree .swiper-pagination-bullet {
  height: 5px;
  width: 1.875rem;
  border-radius: 0px;
  background-color: rgb(255 255 255 / 0.5);
}

.carouselTwo .swiper-pagination-bullet-active,
.carouselThree .swiper-pagination-bullet-active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

@media (min-width: 1280px) {

  .carouselTwo .swiper-pagination,
.carouselThree .swiper-pagination {
    bottom: 2rem !important;
  }
}

.data-stats-slider-outer .swiper-button-next,
.data-stats-slider-outer .swiper-button-prev {
  top: 50%;
  height: 2.875rem;
  width: 2.875rem;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1 !important;
  color: rgb(17 25 40 / var(--tw-text-opacity, 1)) !important;
  --tw-drop-shadow: drop-shadow(0px 8px 13px rgba(0, 0, 0, 0.07));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.data-stats-slider-outer .swiper-button-next:is(.dark *),
.data-stats-slider-outer .swiper-button-prev:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.data-stats-slider-outer .swiper-button-next.swiper-button-disabled,
.data-stats-slider-outer .swiper-button-prev.swiper-button-disabled {
  display: none;
}

.data-stats-slider-outer .swiper-button-prev {
  left: -23px;
}

.data-stats-slider-outer .swiper-button-next {
  right: -23px;
}

.data-table-common .datatable-search {
  position: relative;
  margin-left: 0px !important;
  width: 25rem;
  overflow: hidden;
  border-radius: 0.25rem;
}

.data-table-one .datatable-search input {
  height: 46px;
  width: 100%;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.data-table-one .datatable-search input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.data-table-one .datatable-search input:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
}

.data-table-one .datatable-search input:focus:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.data-table-common .datatable-selector {
  position: relative;
  z-index: 20;
  display: inline-flex;
  background-color: transparent;
  padding: 0px;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.data-table-common .datatable-selector:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.data-table-common .datatable-top {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-border-opacity, 1));
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  padding-top: 1.125rem;
  padding-bottom: 1.125rem;
}

.data-table-common .datatable-top::after {
  content: var(--tw-content);
  display: none;
}

.data-table-common .datatable-top:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

@media (min-width: 640px) {

  .data-table-common .datatable-top {
    flex-direction: row-reverse;
    align-items: center;
    justify-content: space-between;
    -moz-column-gap: 1rem;
         column-gap: 1rem;
    row-gap: 0px;
  }
}

.data-table-common .datatable-dropdown label {
  display: inline-flex;
  align-items: center;
  gap: 0.625rem;
  font-weight: 500;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(17 25 40 / var(--tw-text-opacity, 1));
}

.data-table-common .datatable-dropdown label:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.datatable-table .datatable-sorter::before {
  content: var(--tw-content);
  display: none;
}

.datatable-table .datatable-sorter::after {
  content: var(--tw-content);
  display: none;
}

.datatable-table > thead > tr:first-child > th {
  border-color: transparent;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  padding-bottom: 0.625rem;
  padding-top: 2.25rem;
  font-weight: 500;
}

.data-table-common .datatable-table > tbody > tr > td:first-child,
.data-table-common .datatable-table > thead > tr > th:first-child {
  padding-left: 2rem;
}

.data-table-common .datatable-table > tbody > tr > td:last-child,
.data-table-common .datatable-table > thead > tr > th:last-child {
  padding-right: 2rem;
}

.data-table-common .datatable-table > thead > tr:last-child > th {
  border-bottom-width: 1px !important;
  --tw-border-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-border-opacity, 1));
  padding-bottom: 1.5rem;
}

.data-table-common .datatable-table > thead > tr:last-child > th:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.data-table-common .datatable-table > thead > tr:last-child > th input {
  height: 34px;
  width: 100%;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.data-table-common .datatable-table > thead > tr:last-child > th input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.data-table-common .datatable-table > thead > tr:last-child > th input:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
}

.data-table-common .datatable-table > thead > tr:last-child > th input:focus:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.data-table-common .datatable-table > tbody > tr:hover {
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
  --tw-bg-opacity: 0.05;
}

.data-table-one .datatable-table > tbody > tr > td:first-child {
  --tw-text-opacity: 1;
  color: rgb(87 80 241 / var(--tw-text-opacity, 1));
}

.data-table-one .datatable-table > tbody > tr > td:first-child:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.data-table-common .datatable-table > tbody > tr > td {
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-border-opacity, 1));
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  font-weight: 500;
}

.data-table-common .datatable-table > tbody > tr > td:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.data-table-one .datatable-bottom {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}

.data-table-one .datatable-bottom::after {
  content: var(--tw-content);
  display: none;
}

@media (min-width: 640px) {

  .data-table-one .datatable-bottom {
    flex-direction: row-reverse;
    align-items: center;
    justify-content: space-between;
    gap: 0px;
  }

  .data-table-one .datatable-bottom > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }
}

.data-table-common .datatable-wrapper.no-footer .datatable-container {
  border-style: none;
}

.data-table-common .datatable-info {
  margin: 0px !important;
  font-weight: 500;
}

.data-table-common .datatable-pagination {
  margin: 0px !important;
}

.data-table-common .datatable-pagination a {
  display: flex;
  height: 2rem;
  width: 2rem;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  padding: 0px;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.data-table-common .datatable-pagination a:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.data-table-common .datatable-pagination a:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.data-table-common .datatable-pagination .datatable-active a {
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.data-table-common .datatable-pagination li.datatable-hidden {
  visibility: visible !important;
}

.data-table-two .datatable-bottom {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}

.data-table-two .datatable-bottom::after {
  content: var(--tw-content);
  display: none;
}

@media (min-width: 640px) {

  .data-table-two .datatable-bottom {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 0px;
  }

  .data-table-two .datatable-bottom > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }
}

.data-table-two .datatable-search input {
  height: 2.875rem;
  width: 100%;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.data-table-two .datatable-search input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.data-table-two .datatable-search input:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
}

.data-table-two .datatable-search input:focus:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.rangeSliderCommon .noUi-target {
  border-style: none;
  background-color: transparent;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.rangeSliderCommon .noUi-connects {
  height: 0.375rem;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(230 235 241 / var(--tw-bg-opacity, 1));
}

.rangeSliderCommon .noUi-connects:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.rangeSliderCommon .noUi-connect {
  height: 0.375rem;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
}

.rangeSliderOne .noUi-horizontal .noUi-handle {
  top: -0.5rem;
  height: 1.375rem;
  width: 1.375rem;
  border-radius: 9999px;
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.rangeSliderTwo .noUi-horizontal .noUi-handle {
  top: -0.5rem;
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 9999px;
  border-width: 6px;
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.rangeSliderTwo .noUi-horizontal .noUi-handle:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 25 40 / var(--tw-bg-opacity, 1));
}

.noUi-horizontal .noUi-handle:after,
.noUi-horizontal .noUi-handle:before {
  display: none;
}

input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
          appearance: none;
}

.custom-input-date::-webkit-calendar-picker-indicator {
  background-position: center;
  background-repeat: no-repeat;
  background-size: 20px;
}

.custom-gradient-1 {
  background-image: linear-gradient(145deg, #eef 0%, #fff8fc 100%);
}

.file\:mr-4::file-selector-button {
  margin-right: 1rem;
}

.file\:mr-5::file-selector-button {
  margin-right: 1.25rem;
}

.file\:border-collapse::file-selector-button {
  border-collapse: collapse;
}

.file\:cursor-pointer::file-selector-button {
  cursor: pointer;
}

.file\:rounded::file-selector-button {
  border-radius: 0.25rem;
}

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:border-\[0\.5px\]::file-selector-button {
  border-width: 0.5px;
}

.file\:border-r::file-selector-button {
  border-right-width: 1px;
}

.file\:border-solid::file-selector-button {
  border-style: solid;
}

.file\:border-stroke::file-selector-button {
  --tw-border-opacity: 1;
  border-color: rgb(230 235 241 / var(--tw-border-opacity, 1));
}

.file\:bg-\[\#E2E8F0\]::file-selector-button {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}

.file\:bg-stroke::file-selector-button {
  --tw-bg-opacity: 1;
  background-color: rgb(230 235 241 / var(--tw-bg-opacity, 1));
}

.file\:px-2\.5::file-selector-button {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.file\:px-6\.5::file-selector-button {
  padding-left: 1.625rem;
  padding-right: 1.625rem;
}

.file\:py-1::file-selector-button {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.file\:py-\[13px\]::file-selector-button {
  padding-top: 13px;
  padding-bottom: 13px;
}

.file\:text-body-sm::file-selector-button {
  font-size: 14px;
  line-height: 22px;
}

.file\:text-body-xs::file-selector-button {
  font-size: 12px;
  line-height: 20px;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.file\:text-dark-5::file-selector-button {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.placeholder\:text-dark-6::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.placeholder\:text-dark-6::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.hover\:border-primary:hover {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.hover\:bg-\[\#F9FAFB\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[rgba\(87\2c 80\2c 241\2c 0\.07\)\]:hover {
  background-color: rgba(87,80,241,0.07);
}

.hover\:bg-blue-dark:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(28 63 183 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-light-5:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(225 232 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-dark\/10:hover {
  background-color: rgb(17 25 40 / 0.1);
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-2:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green\/10:hover {
  background-color: rgb(34 173 92 / 0.1);
}

.hover\:bg-neutral-100\/50:hover {
  background-color: rgb(245 245 245 / 0.5);
}

.hover\:bg-primary\/10:hover {
  background-color: rgb(87 80 241 / 0.1);
}

.hover\:bg-primary\/5:hover {
  background-color: rgb(87 80 241 / 0.05);
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.hover\:bg-opacity-50:hover {
  --tw-bg-opacity: 0.5;
}

.hover\:bg-opacity-90:hover {
  --tw-bg-opacity: 0.9;
}

.hover\:text-dark:hover {
  --tw-text-opacity: 1;
  color: rgb(17 25 40 / var(--tw-text-opacity, 1));
}

.hover\:text-dark-3:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(87 80 241 / var(--tw-text-opacity, 1));
}

.hover\:text-red:hover {
  --tw-text-opacity: 1;
  color: rgb(242 48 48 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:shadow-1:hover {
  --tw-shadow: 0px 1px 2px 0px rgba(84, 87, 118, 0.12);
  --tw-shadow-colored: 0px 1px 2px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-2:hover {
  --tw-shadow: 0px 2px 3px 0px rgba(84, 87, 118, 0.15);
  --tw-shadow-colored: 0px 2px 3px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.file\:hover\:bg-primary:hover::file-selector-button {
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
}

.file\:hover\:bg-opacity-10:hover::file-selector-button {
  --tw-bg-opacity: 0.1;
}

.focus\:z-10:focus {
  z-index: 10;
}

.focus\:border-primary:focus {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.focus\:bg-blue-light-5:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(225 232 255 / var(--tw-bg-opacity, 1));
}

.focus\:text-primary:focus {
  --tw-text-opacity: 1;
  color: rgb(87 80 241 / var(--tw-text-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-primary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(87 80 241 / var(--tw-ring-opacity, 1));
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-green-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\:ring-gray-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.file\:focus\:border-primary:focus::file-selector-button {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.focus-visible\:border-primary:focus-visible {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.focus-visible\:bg-gray-2:focus-visible {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.focus-visible\:text-primary:focus-visible {
  --tw-text-opacity: 1;
  color: rgb(87 80 241 / var(--tw-text-opacity, 1));
}

.focus-visible\:outline:focus-visible {
  outline-style: solid;
}

.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.active\:border-primary:active {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.disabled\:cursor-default:disabled {
  cursor: default;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:bg-gray-2:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group:hover .group-hover\:visible {
  visibility: visible;
}

.group:hover .group-hover\:text-primary {
  --tw-text-opacity: 1;
  color: rgb(87 80 241 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.peer:checked ~ .peer-checked\:right-1 {
  right: 0.25rem;
}

.peer:checked ~ .peer-checked\:translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:border-6 {
  border-width: 6px;
}

.peer:checked ~ .peer-checked\:border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:bg-dark-2 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:bg-gray-2 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
}

.data-\[active\=true\]\:border-primary[data-active="true"] {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.data-\[state\=selected\]\:bg-neutral-100[data-state="selected"] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.data-\[placeholder\]\:text-neutral-500[data-placeholder] {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.dark\:block:is(.dark *) {
  display: block;
}

.dark\:hidden:is(.dark *) {
  display: none;
}

.dark\:translate-x-\[48px\]:is(.dark *) {
  --tw-translate-x: 48px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:divide-dark-3:is(.dark *) > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-divide-opacity, 1));
}

.dark\:border-none:is(.dark *) {
  border-style: none;
}

.dark\:border-dark-3:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.dark\:border-dark-4:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.dark\:border-dark-6:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}

.dark\:border-primary:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.dark\:border-stroke-dark:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(39 48 62 / var(--tw-border-opacity, 1));
}

.dark\:border-white\/25:is(.dark *) {
  border-color: rgb(255 255 255 / 0.25);
}

.dark\:border-t-transparent:is(.dark *) {
  border-top-color: transparent;
}

.dark\:\!bg-dark-2:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1)) !important;
}

.dark\:\!bg-white:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) !important;
}

.dark\:bg-\[\#020D1A\]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(2 13 26 / var(--tw-bg-opacity, 1));
}

.dark\:bg-\[\#020d1a\]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(2 13 26 / var(--tw-bg-opacity, 1));
}

.dark\:bg-\[\#1B1B24\]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(27 27 36 / var(--tw-bg-opacity, 1));
}

.dark\:bg-\[\#5A616B\]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(90 97 107 / var(--tw-bg-opacity, 1));
}

.dark\:bg-\[\#FFFFFF1A\]:is(.dark *) {
  background-color: #FFFFFF1A;
}

.dark\:bg-blue-900\/20:is(.dark *) {
  background-color: rgb(30 58 138 / 0.2);
}

.dark\:bg-dark:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 25 40 / var(--tw-bg-opacity, 1));
}

.dark\:bg-dark-2:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
}

.dark\:bg-dark-3:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-dark:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(18 32 49 / var(--tw-bg-opacity, 1));
}

.dark\:bg-neutral-800\/50:is(.dark *) {
  background-color: rgb(38 38 38 / 0.5);
}

.dark\:bg-primary:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(87 80 241 / var(--tw-bg-opacity, 1));
}

.dark\:bg-transparent:is(.dark *) {
  background-color: transparent;
}

.dark\:bg-white\/10:is(.dark *) {
  background-color: rgb(255 255 255 / 0.1);
}

.dark\:bg-red-900\/20:is(.dark *) {
  background-color: rgb(127 29 29 / 0.2);
}

.dark\:bg-gray-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.dark\:bg-opacity-30:is(.dark *) {
  --tw-bg-opacity: 0.3;
}

.dark\:bg-none:is(.dark *) {
  background-image: none;
}

.dark\:fill-dark:is(.dark *) {
  fill: #111928;
}

.dark\:fill-white:is(.dark *) {
  fill: #FFFFFF;
}

.dark\:text-\[\#34D399\]:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}

.dark\:text-current:is(.dark *) {
  color: currentColor;
}

.dark\:text-dark-5:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.dark\:text-dark-6:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.dark\:text-neutral-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:text-red-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.dark\:opacity-30:is(.dark *) {
  opacity: 0.3;
}

.dark\:shadow-card:is(.dark *) {
  --tw-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0px 1px 2px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:ring-dark-2:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 42 55 / var(--tw-ring-opacity, 1));
}

.dark\:ring-dark-3:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(55 65 81 / var(--tw-ring-opacity, 1));
}

.dark\:ring-offset-gray-dark:is(.dark *) {
  --tw-ring-offset-color: #122031;
}

.dark\:ring-offset-neutral-950:is(.dark *) {
  --tw-ring-offset-color: #0a0a0a;
}

.dark\:file\:border-dark-3:is(.dark *)::file-selector-button {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.dark\:file\:bg-white\/30:is(.dark *)::file-selector-button {
  background-color: rgb(255 255 255 / 0.3);
}

.dark\:file\:text-white:is(.dark *)::file-selector-button {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:hover\:border-dark-4:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.dark\:hover\:border-dark-5:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.dark\:hover\:border-primary:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.dark\:hover\:bg-\[\#FFFFFF1A\]:hover:is(.dark *) {
  background-color: #FFFFFF1A;
}

.dark\:hover\:bg-dark-2:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-dark-3:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-white\/10:hover:is(.dark *) {
  background-color: rgb(255 255 255 / 0.1);
}

.hover\:dark\:bg-\[\#FFFFFF1A\]:is(.dark *):hover {
  background-color: #FFFFFF1A;
}

.dark\:hover\:bg-gray-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-600:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-opacity-50:hover:is(.dark *) {
  --tw-bg-opacity: 0.5;
}

.dark\:hover\:text-dark-6:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-dark-7:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-primary:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(87 80 241 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-white:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:dark\:text-white:is(.dark *):hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-gray-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:focus\:border-primary:focus:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.dark\:focus\:ring-neutral-300:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(212 212 212 / var(--tw-ring-opacity, 1));
}

.dark\:focus-visible\:border-dark-5:focus-visible:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.dark\:focus-visible\:border-primary:focus-visible:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.dark\:focus-visible\:bg-dark-2:focus-visible:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
}

.dark\:focus-visible\:bg-dark-3:focus-visible:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:focus-visible\:text-dark-7:focus-visible:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:disabled\:bg-dark:disabled:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 25 40 / var(--tw-bg-opacity, 1));
}

.group:hover .dark\:group-hover\:bg-dark-3:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .dark\:peer-checked\:bg-dark-2:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 42 55 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .dark\:peer-checked\:bg-transparent:is(.dark *) {
  background-color: transparent;
}

.peer:checked ~ .peer-checked\:dark\:bg-white:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.dark\:data-\[active\=true\]\:border-primary[data-active="true"]:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 80 241 / var(--tw-border-opacity, 1));
}

.dark\:data-\[state\=selected\]\:bg-neutral-800[data-state="selected"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity, 1));
}

.dark\:data-\[placeholder\]\:text-neutral-400[data-placeholder]:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}

@media not all and (min-width: 1280px) {

  .max-xl\:hidden {
    display: none;
  }
}

@media (max-width: 1024px) {

  .max-\[1024px\]\:sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
}

@media (max-width: 1015px) {

  .max-\[1015px\]\:size-5 {
    width: 1.25rem;
    height: 1.25rem;
  }
}

@media not all and (min-width: 640px) {

  .max-sm\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .max-sm\:border-b {
    border-bottom-width: 1px;
  }

  .max-sm\:pb-3 {
    padding-bottom: 0.75rem;
  }
}

@media (max-width: 430px) {

  .max-\[430px\]\:hidden {
    display: none;
  }
}

@media (min-width: 230px) {

  .min-\[230px\]\:min-w-\[17\.5rem\] {
    min-width: 17.5rem;
  }
}

@media (min-width: 350px) {

  .min-\[350px\]\:min-w-\[20rem\] {
    min-width: 20rem;
  }
}

@media (min-width: 375px) {

  .min-\[375px\]\:ml-4 {
    margin-left: 1rem;
  }

  .min-\[375px\]\:gap-4 {
    gap: 1rem;
  }
}

@media (min-width: 425px) {

  .xsm\:bottom-4 {
    bottom: 1rem;
  }

  .xsm\:right-4 {
    right: 1rem;
  }

  .xsm\:flex-row {
    flex-direction: row;
  }

  .xsm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

@media (min-width: 640px) {

  .sm\:bottom-2 {
    bottom: 0.5rem;
  }

  .sm\:right-2 {
    right: 0.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:h-44 {
    height: 11rem;
  }

  .sm\:w-1\/2 {
    width: 50%;
  }

  .sm\:max-w-\[176px\] {
    max-width: 176px;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:divide-x > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  .sm\:p-12\.5 {
    padding: 3.125rem;
  }

  .sm\:p-3 {
    padding: 0.75rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:p-7\.5 {
    padding: 1.875rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-7 {
    padding-left: 1.75rem;
    padding-right: 1.75rem;
  }

  .sm\:py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }

  .sm\:py-7\.5 {
    padding-top: 1.875rem;
    padding-bottom: 1.875rem;
  }

  .sm\:pl-6 {
    padding-left: 1.5rem;
  }

  .sm\:pr-6 {
    padding-right: 1.5rem;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-heading-3 {
    font-size: 40px;
    line-height: 48px;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 768px) {

  .md\:visible {
    visibility: visible;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:mt-6 {
    margin-top: 1.5rem;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-25 {
    height: 6.25rem;
  }

  .md\:h-30 {
    height: 7.5rem;
  }

  .md\:h-65 {
    height: 16.25rem;
  }

  .md\:w-\[190\%\] {
    width: 190%;
  }

  .md\:w-\[290\%\] {
    width: 290%;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-9 {
    padding: 2.25rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:opacity-100 {
    opacity: 1;
  }
}

@media (min-width: 850px) {

  .min-\[850px\]\:mt-10 {
    margin-top: 2.5rem;
  }

  .min-\[850px\]\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

@media (min-width: 1024px) {

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:block {
    display: block;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:pb-8 {
    padding-bottom: 2rem;
  }
}

@media (min-width: 1280px) {

  .xl\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .xl\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .xl\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .xl\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .xl\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .xl\:block {
    display: block;
  }

  .xl\:h-31 {
    height: 7.75rem;
  }

  .xl\:w-1\/2 {
    width: 50%;
  }

  .xl\:max-w-\[8rem\] {
    max-width: 8rem;
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:flex-row {
    flex-direction: row;
  }

  .xl\:gap-20 {
    gap: 5rem;
  }

  .xl\:gap-7\.5 {
    gap: 1.875rem;
  }

  .xl\:p-10 {
    padding: 2.5rem;
  }

  .xl\:p-15 {
    padding: 3.75rem;
  }

  .xl\:p-5 {
    padding: 1.25rem;
  }

  .xl\:p-9 {
    padding: 2.25rem;
  }

  .xl\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .xl\:px-7\.5 {
    padding-left: 1.875rem;
    padding-right: 1.875rem;
  }

  .xl\:px-8\.5 {
    padding-left: 2.125rem;
    padding-right: 2.125rem;
  }

  .xl\:px-9 {
    padding-left: 2.25rem;
    padding-right: 2.25rem;
  }

  .xl\:pb-11\.5 {
    padding-bottom: 2.875rem;
  }

  .xl\:pl-7\.5 {
    padding-left: 1.875rem;
  }

  .xl\:pr-7\.5 {
    padding-right: 1.875rem;
  }
}

@media (min-width: 1536px) {

  .\32xl\:mt-9 {
    margin-top: 2.25rem;
  }

  .\32xl\:gap-7\.5 {
    gap: 1.875rem;
  }

  .\32xl\:p-10 {
    padding: 2.5rem;
  }

  .\32xl\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]) {
  padding-right: 0px;
}

.\[\&\>\*\]\:cursor-pointer>* {
  cursor: pointer;
}

.\[\&\>\*\]\:text-white>* {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.peer:checked ~ .peer-checked\:\[\&\>\*\]\:block>* {
  display: block;
}

.\[\&\>div\]\:flex>div {
  display: flex;
}

.\[\&\>div\]\:flex-col-reverse>div {
  flex-direction: column-reverse;
}

.\[\&\>div\]\:gap-1>div {
  gap: 0.25rem;
}

.\[\&\>option\]\:text-dark-5>option {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.dark\:\[\&\>option\]\:text-dark-6>option:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.\[\&\>span\]\:line-clamp-1>span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.\[\&\>th\]\:h-auto>th {
  height: auto;
}

.\[\&\>th\]\:py-3>th {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.\[\&\>th\]\:py-4>th {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.\[\&\>th\]\:text-center>th {
  text-align: center;
}

.\[\&\>th\]\:text-base>th {
  font-size: 1rem;
  line-height: 1.5rem;
}

.\[\&\>th\]\:text-dark>th {
  --tw-text-opacity: 1;
  color: rgb(17 25 40 / var(--tw-text-opacity, 1));
}

.\[\&\>th\]\:dark\:text-white:is(.dark *)>th {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {

  .sm\:\[\&\>th\]\:py-4\.5>th {
    padding-top: 1.125rem;
    padding-bottom: 1.125rem;
  }
}

.\[\&\>tr\]\:last\:border-b-0:last-child>tr {
  border-bottom-width: 0px;
}

.\[\&\[data-state\=\'open\'\]\>svg\]\:rotate-0[data-state='open']>svg {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:\[\&_\.check-icon\]\:block .check-icon {
  display: block;
}

.peer:checked ~ .peer-checked\:\[\&_\.x-icon\]\:hidden .x-icon {
  display: none;
}

.\[\&_input\]\:py-\[15px\] input {
  padding-top: 15px;
  padding-bottom: 15px;
}

.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}

.\[\&_svg\]\:absolute svg {
  position: absolute;
}

.\[\&_svg\]\:left-4\.5 svg {
  left: 1.125rem;
}

.\[\&_svg\]\:left-5\.5 svg {
  left: 1.375rem;
}

.\[\&_svg\]\:right-4\.5 svg {
  right: 1.125rem;
}

.\[\&_svg\]\:top-1\/2 svg {
  top: 50%;
}

.\[\&_svg\]\:top-5\.5 svg {
  top: 1.375rem;
}

.\[\&_svg\]\:-translate-y-1\/2 svg {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0px;
}

.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}

