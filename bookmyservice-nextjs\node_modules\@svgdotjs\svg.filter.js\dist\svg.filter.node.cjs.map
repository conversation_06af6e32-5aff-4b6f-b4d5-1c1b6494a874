{"version": 3, "file": "svg.filter.node.cjs", "sources": ["../src/svg.filter.js"], "sourcesContent": ["import {\n  Array as <PERSON><PERSON><PERSON><PERSON>,\n  Container,\n  Defs,\n  Element,\n  extend,\n  find,\n  namespaces as ns,\n  nodeOrNew,\n  utils,\n  wrapWithAttrCheck\n} from '@svgdotjs/svg.js'\n\nexport default class Filter extends Element {\n  constructor (node) {\n    super(nodeOrNew('filter', node), node)\n\n    this.$source = 'SourceGraphic'\n    this.$sourceAlpha = 'SourceAlpha'\n    this.$background = 'BackgroundImage'\n    this.$backgroundAlpha = 'BackgroundAlpha'\n    this.$fill = 'FillPaint'\n    this.$stroke = 'StrokePaint'\n    this.$autoSetIn = true\n  }\n\n  put (element, i) {\n    element = super.put(element, i)\n\n    if (!element.attr('in') && this.$autoSetIn) {\n      element.attr('in', this.$source)\n    }\n    if (!element.attr('result')) {\n      element.attr('result', element.id())\n    }\n\n    return element\n  }\n\n  // Unmask all masked elements and remove itself\n  remove () {\n    // unmask all targets\n    this.targets().each('unfilter')\n\n    // remove mask from parent\n    return super.remove()\n  }\n\n  targets () {\n    return find('svg [filter*=\"' + this.id() + '\"]')\n  }\n\n  toString () {\n    return 'url(#' + this.id() + ')'\n  }\n}\n\n// Create Effect class\nclass Effect extends Element {\n  constructor (node, attr) {\n    super(node, attr)\n    this.result(this.id())\n  }\n\n  in (effect) {\n    // Act as getter\n    if (effect == null) {\n      const _in = this.attr('in')\n      const ref = this.parent() && this.parent().find(`[result=\"${_in}\"]`)[0]\n      return ref || _in\n    }\n\n    // Avr as setter\n    return this.attr('in', effect)\n  }\n\n  // Named result\n  result (result) {\n    return this.attr('result', result)\n  }\n\n  // Stringification\n  toString () {\n    return this.result()\n  }\n}\n\n// This function takes an array with attr keys and sets for every key the\n// attribute to the value of one paramater\n// getAttrSetter(['a', 'b']) becomes this.attr({a: param1, b: param2})\nconst getAttrSetter = (params) => {\n  return function (...args) {\n    for (let i = params.length; i--;) {\n      if (args[i] != null) {\n        this.attr(params[i], args[i])\n      }\n    }\n  }\n}\n\nconst updateFunctions = {\n  blend: getAttrSetter(['in', 'in2', 'mode']),\n  // ColorMatrix effect\n  colorMatrix: getAttrSetter(['type', 'values']),\n  // Composite effect\n  composite: getAttrSetter(['in', 'in2', 'operator']),\n  // ConvolveMatrix effect\n  convolveMatrix: function (matrix) {\n    matrix = new SVGArray(matrix).toString()\n\n    this.attr({\n      order: Math.sqrt(matrix.split(' ').length),\n      kernelMatrix: matrix\n    })\n  },\n  // DiffuseLighting effect\n  diffuseLighting: getAttrSetter(['surfaceScale', 'lightingColor', 'diffuseConstant', 'kernelUnitLength']),\n  // DisplacementMap effect\n  displacementMap: getAttrSetter(['in', 'in2', 'scale', 'xChannelSelector', 'yChannelSelector']),\n  // DropShadow effect\n  dropShadow: getAttrSetter(['in', 'dx', 'dy', 'stdDeviation']),\n  // Flood effect\n  flood: getAttrSetter(['flood-color', 'flood-opacity']),\n  // Gaussian Blur effect\n  gaussianBlur: function (x = 0, y = x) {\n    this.attr('stdDeviation', x + ' ' + y)\n  },\n  // Image effect\n  image: function (src) {\n    this.attr('href', src, ns.xlink)\n  },\n  // Morphology effect\n  morphology: getAttrSetter(['operator', 'radius']),\n  // Offset effect\n  offset: getAttrSetter(['dx', 'dy']),\n  // SpecularLighting effect\n  specularLighting: getAttrSetter(['surfaceScale', 'lightingColor', 'diffuseConstant', 'specularExponent', 'kernelUnitLength']),\n  // Tile effect\n  tile: getAttrSetter([]),\n  // Turbulence effect\n  turbulence: getAttrSetter(['baseFrequency', 'numOctaves', 'seed', 'stitchTiles', 'type'])\n}\n\nconst filterNames = [\n  'blend',\n  'colorMatrix',\n  'componentTransfer',\n  'composite',\n  'convolveMatrix',\n  'diffuseLighting',\n  'displacementMap',\n  'dropShadow',\n  'flood',\n  'gaussianBlur',\n  'image',\n  'merge',\n  'morphology',\n  'offset',\n  'specularLighting',\n  'tile',\n  'turbulence'\n]\n\n// For every filter create a class\nfilterNames.forEach((effect) => {\n  const name = utils.capitalize(effect)\n  const fn = updateFunctions[effect]\n\n  Filter[name + 'Effect'] = class extends Effect {\n    constructor (node) {\n      super(nodeOrNew('fe' + name, node), node)\n    }\n\n    // This function takes all parameters from the factory call\n    // and updates the attributes according to the updateFunctions\n    update (args) {\n      fn.apply(this, args)\n      return this\n    }\n  }\n\n  // Add factory function to filter\n  // Allow to pass a function or object\n  // The attr object is catched from \"wrapWithAttrCheck\"\n  Filter.prototype[effect] = wrapWithAttrCheck(function (fn, ...args) {\n    const effect = new Filter[name + 'Effect']()\n\n    if (fn == null) return this.put(effect)\n\n    // For Effects which can take children, a function is allowed\n    if (typeof fn === 'function') {\n      fn.call(effect, effect)\n    } else {\n      // In case it is not a function, add it to arguments\n      args.unshift(fn)\n    }\n    return this.put(effect).update(args)\n  })\n})\n\n// Correct factories which are not that simple\nextend(Filter, {\n  merge (arrayOrFn) {\n    const node = this.put(new Filter.MergeEffect())\n\n    // If a function was passed, execute it\n    // That makes stuff like this possible:\n    // filter.merge((mergeEffect) => mergeEffect.mergeNode(in))\n    if (typeof arrayOrFn === 'function') {\n      arrayOrFn.call(node, node)\n      return node\n    }\n\n    // Check if first child is an array, otherwise use arguments as array\n    const children = arrayOrFn instanceof Array ? arrayOrFn : [...arguments]\n\n    children.forEach((child) => {\n      if (child instanceof Filter.MergeNode) {\n        node.put(child)\n      } else {\n        node.mergeNode(child)\n      }\n    })\n\n    return node\n  },\n  componentTransfer (components = {}) {\n    const node = this.put(new Filter.ComponentTransferEffect())\n\n    if (typeof components === 'function') {\n      components.call(node, node)\n      return node\n    }\n\n    // If no component is set, we use the given object for all components\n    if (!components.r && !components.g && !components.b && !components.a) {\n      const temp = components\n      components = {\n        r: temp, g: temp, b: temp, a: temp\n      }\n    }\n\n    for (const c in components) {\n      // components[c] has to hold an attributes object\n      node.add(new Filter['Func' + c.toUpperCase()](components[c]))\n    }\n\n    return node\n  }\n})\n\nconst filterChildNodes = [\n  'distantLight',\n  'pointLight',\n  'spotLight',\n  'mergeNode',\n  'FuncR',\n  'FuncG',\n  'FuncB',\n  'FuncA'\n]\n\nfilterChildNodes.forEach((child) => {\n  const name = utils.capitalize(child)\n  Filter[name] = class extends Effect {\n    constructor (node) {\n      super(nodeOrNew('fe' + name, node), node)\n    }\n  }\n})\n\nconst componentFuncs = [\n  'funcR',\n  'funcG',\n  'funcB',\n  'funcA'\n]\n\n// Add an update function for componentTransfer-children\ncomponentFuncs.forEach(function (c) {\n  const _class = Filter[utils.capitalize(c)]\n  const fn = wrapWithAttrCheck(function () {\n    return this.put(new _class())\n  })\n\n  Filter.ComponentTransferEffect.prototype[c] = fn\n})\n\nconst lights = [\n  'distantLight',\n  'pointLight',\n  'spotLight'\n]\n\n// Add light sources factories to lightining effects\nlights.forEach((light) => {\n  const _class = Filter[utils.capitalize(light)]\n  const fn = wrapWithAttrCheck(function () {\n    return this.put(new _class())\n  })\n\n  Filter.DiffuseLightingEffect.prototype[light] = fn\n  Filter.SpecularLightingEffect.prototype[light] = fn\n})\n\nextend(Filter.MergeEffect, {\n  mergeNode (_in) {\n    return this.put(new Filter.MergeNode()).attr('in', _in)\n  }\n})\n\n// add .filter function\nextend(Defs, {\n  // Define filter\n  filter: function (block) {\n    const filter = this.put(new Filter())\n\n    /* invoke passed block */\n    if (typeof block === 'function') { block.call(filter, filter) }\n\n    return filter\n  }\n})\n\nextend(Container, {\n  // Define filter on defs\n  filter: function (block) {\n    return this.defs().filter(block)\n  }\n})\n\nextend(Element, {\n  // Create filter element in defs and store reference\n  filterWith: function (block) {\n    const filter = block instanceof Filter\n      ? block\n      : this.defs().filter(block)\n\n    return this.attr('filter', filter)\n  },\n  // Remove filter\n  unfilter: function (remove) {\n    /* remove filter attribute */\n    return this.attr('filter', null)\n  },\n  filterer () {\n    return this.reference('filter')\n  }\n})\n\n// chaining\nconst chainingEffects = {\n  // Blend effect\n  blend: function (in2, mode) {\n    return this.parent() && this.parent().blend(this, in2, mode) // pass this as the first input\n  },\n  // ColorMatrix effect\n  colorMatrix: function (type, values) {\n    return this.parent() && this.parent().colorMatrix(type, values).in(this)\n  },\n  // ComponentTransfer effect\n  componentTransfer: function (components) {\n    return this.parent() && this.parent().componentTransfer(components).in(this)\n  },\n  // Composite effect\n  composite: function (in2, operator) {\n    return this.parent() && this.parent().composite(this, in2, operator) // pass this as the first input\n  },\n  // ConvolveMatrix effect\n  convolveMatrix: function (matrix) {\n    return this.parent() && this.parent().convolveMatrix(matrix).in(this)\n  },\n  // DiffuseLighting effect\n  diffuseLighting: function (surfaceScale, lightingColor, diffuseConstant, kernelUnitLength) {\n    return this.parent() && this.parent().diffuseLighting(surfaceScale, diffuseConstant, kernelUnitLength).in(this)\n  },\n  // DisplacementMap effect\n  displacementMap: function (in2, scale, xChannelSelector, yChannelSelector) {\n    return this.parent() && this.parent().displacementMap(this, in2, scale, xChannelSelector, yChannelSelector) // pass this as the first input\n  },\n  // DisplacementMap effect\n  dropShadow: function (x, y, stdDeviation) {\n    return this.parent() && this.parent().dropShadow(this, x, y, stdDeviation).in(this) // pass this as the first input\n  },\n  // Flood effect\n  flood: function (color, opacity) {\n    return this.parent() && this.parent().flood(color, opacity) // this effect dont have inputs\n  },\n  // Gaussian Blur effect\n  gaussianBlur: function (x, y) {\n    return this.parent() && this.parent().gaussianBlur(x, y).in(this)\n  },\n  // Image effect\n  image: function (src) {\n    return this.parent() && this.parent().image(src) // this effect dont have inputs\n  },\n  // Merge effect\n  merge: function (arg) {\n    arg = arg instanceof Array ? arg : [...arg]\n    return this.parent() && this.parent().merge(this, ...arg) // pass this as the first argument\n  },\n  // Morphology effect\n  morphology: function (operator, radius) {\n    return this.parent() && this.parent().morphology(operator, radius).in(this)\n  },\n  // Offset effect\n  offset: function (dx, dy) {\n    return this.parent() && this.parent().offset(dx, dy).in(this)\n  },\n  // SpecularLighting effect\n  specularLighting: function (surfaceScale, lightingColor, diffuseConstant, specularExponent, kernelUnitLength) {\n    return this.parent() && this.parent().specularLighting(surfaceScale, diffuseConstant, specularExponent, kernelUnitLength).in(this)\n  },\n  // Tile effect\n  tile: function () {\n    return this.parent() && this.parent().tile().in(this)\n  },\n  // Turbulence effect\n  turbulence: function (baseFrequency, numOctaves, seed, stitchTiles, type) {\n    return this.parent() && this.parent().turbulence(baseFrequency, numOctaves, seed, stitchTiles, type).in(this)\n  }\n}\n\nextend(Effect, chainingEffects)\n\n// Effect-specific extensions\nextend(Filter.MergeEffect, {\n  in: function (effect) {\n    if (effect instanceof Filter.MergeNode) {\n      this.add(effect, 0)\n    } else {\n      this.add(new Filter.MergeNode().in(effect), 0)\n    }\n\n    return this\n  }\n})\n\nextend([Filter.CompositeEffect, Filter.BlendEffect, Filter.DisplacementMapEffect], {\n  in2: function (effect) {\n    if (effect == null) {\n      const in2 = this.attr('in2')\n      const ref = this.parent() && this.parent().find(`[result=\"${in2}\"]`)[0]\n      return ref || in2\n    }\n    return this.attr('in2', effect)\n  }\n})\n\n// Presets\nFilter.filter = {\n  sepiatone: [\n    0.343, 0.669, 0.119, 0, 0,\n    0.249, 0.626, 0.130, 0, 0,\n    0.172, 0.334, 0.111, 0, 0,\n    0.000, 0.000, 0.000, 1, 0]\n}\n"], "names": ["Filter", "Element", "constructor", "node", "nodeOrNew", "$source", "$sourceAlpha", "$background", "$backgroundAlpha", "$fill", "$stroke", "$autoSetIn", "put", "element", "i", "attr", "id", "remove", "targets", "each", "find", "toString", "Effect", "result", "in", "effect", "_in", "ref", "parent", "getAttrSetter", "params", "args", "length", "updateFunctions", "blend", "colorMatrix", "composite", "convolveMatrix", "matrix", "SVGArray", "order", "Math", "sqrt", "split", "kernelMatrix", "diffuseLighting", "displacementMap", "dropShadow", "flood", "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "x", "y", "image", "src", "ns", "xlink", "morphology", "offset", "specularLighting", "tile", "turbulence", "filterNames", "for<PERSON>ach", "name", "utils", "capitalize", "fn", "update", "apply", "prototype", "wrapWithAttrCheck", "call", "unshift", "extend", "merge", "arrayOrFn", "MergeEffect", "children", "Array", "arguments", "child", "MergeNode", "mergeNode", "componentTransfer", "components", "ComponentTransferEffect", "r", "g", "b", "a", "temp", "c", "add", "toUpperCase", "filterChildNodes", "componentFuncs", "_class", "lights", "light", "DiffuseLightingEffect", "SpecularLightingEffect", "Defs", "filter", "block", "Container", "defs", "filterWith", "unfilter", "filterer", "reference", "chainingEffects", "in2", "mode", "type", "values", "operator", "surfaceScale", "lightingColor", "diffuseConstant", "kernelUnitLength", "scale", "xChannelSelector", "yChannelSelector", "stdDeviation", "color", "opacity", "arg", "radius", "dx", "dy", "specularExponent", "baseFrequency", "numOctaves", "seed", "stitchTiles", "CompositeEffect", "BlendEffect", "DisplacementMapEffect", "sepiatone"], "mappings": ";;;;;;;;;;;;;;AAae,MAAMA,MAAM,SAASC,cAAO,CAAC;EAC1CC,WAAWA,CAAEC,IAAI,EAAE;IACjB,KAAK,CAACC,gBAAS,CAAC,QAAQ,EAAED,IAAI,CAAC,EAAEA,IAAI,CAAC;IAEtC,IAAI,CAACE,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,YAAY,GAAG,aAAa;IACjC,IAAI,CAACC,WAAW,GAAG,iBAAiB;IACpC,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;IACzC,IAAI,CAACC,KAAK,GAAG,WAAW;IACxB,IAAI,CAACC,OAAO,GAAG,aAAa;IAC5B,IAAI,CAACC,UAAU,GAAG,IAAI;AACxB;AAEAC,EAAAA,GAAGA,CAAEC,OAAO,EAAEC,CAAC,EAAE;IACfD,OAAO,GAAG,KAAK,CAACD,GAAG,CAACC,OAAO,EAAEC,CAAC,CAAC;IAE/B,IAAI,CAACD,OAAO,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAACJ,UAAU,EAAE;MAC1CE,OAAO,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,CAACV,OAAO,CAAC;AAClC;AACA,IAAA,IAAI,CAACQ,OAAO,CAACE,IAAI,CAAC,QAAQ,CAAC,EAAE;MAC3BF,OAAO,CAACE,IAAI,CAAC,QAAQ,EAAEF,OAAO,CAACG,EAAE,EAAE,CAAC;AACtC;AAEA,IAAA,OAAOH,OAAO;AAChB;;AAEA;AACAI,EAAAA,MAAMA,GAAI;AACR;IACA,IAAI,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,UAAU,CAAC;;AAE/B;AACA,IAAA,OAAO,KAAK,CAACF,MAAM,EAAE;AACvB;AAEAC,EAAAA,OAAOA,GAAI;IACT,OAAOE,WAAI,CAAC,gBAAgB,GAAG,IAAI,CAACJ,EAAE,EAAE,GAAG,IAAI,CAAC;AAClD;AAEAK,EAAAA,QAAQA,GAAI;IACV,OAAO,OAAO,GAAG,IAAI,CAACL,EAAE,EAAE,GAAG,GAAG;AAClC;AACF;;AAEA;AACA,MAAMM,MAAM,SAASrB,cAAO,CAAC;AAC3BC,EAAAA,WAAWA,CAAEC,IAAI,EAAEY,IAAI,EAAE;AACvB,IAAA,KAAK,CAACZ,IAAI,EAAEY,IAAI,CAAC;IACjB,IAAI,CAACQ,MAAM,CAAC,IAAI,CAACP,EAAE,EAAE,CAAC;AACxB;EAEAQ,EAAEA,CAAEC,MAAM,EAAE;AACV;IACA,IAAIA,MAAM,IAAI,IAAI,EAAE;AAClB,MAAA,MAAMC,GAAG,GAAG,IAAI,CAACX,IAAI,CAAC,IAAI,CAAC;MAC3B,MAAMY,GAAG,GAAG,IAAI,CAACC,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACR,IAAI,CAAC,CAAA,SAAA,EAAYM,GAAG,CAAI,EAAA,CAAA,CAAC,CAAC,CAAC,CAAC;MACvE,OAAOC,GAAG,IAAID,GAAG;AACnB;;AAEA;AACA,IAAA,OAAO,IAAI,CAACX,IAAI,CAAC,IAAI,EAAEU,MAAM,CAAC;AAChC;;AAEA;EACAF,MAAMA,CAAEA,MAAM,EAAE;AACd,IAAA,OAAO,IAAI,CAACR,IAAI,CAAC,QAAQ,EAAEQ,MAAM,CAAC;AACpC;;AAEA;AACAF,EAAAA,QAAQA,GAAI;AACV,IAAA,OAAO,IAAI,CAACE,MAAM,EAAE;AACtB;AACF;;AAEA;AACA;AACA;AACA,MAAMM,aAAa,GAAIC,MAAM,IAAK;EAChC,OAAO,UAAU,GAAGC,IAAI,EAAE;IACxB,KAAK,IAAIjB,CAAC,GAAGgB,MAAM,CAACE,MAAM,EAAElB,CAAC,EAAE,GAAG;AAChC,MAAA,IAAIiB,IAAI,CAACjB,CAAC,CAAC,IAAI,IAAI,EAAE;AACnB,QAAA,IAAI,CAACC,IAAI,CAACe,MAAM,CAAChB,CAAC,CAAC,EAAEiB,IAAI,CAACjB,CAAC,CAAC,CAAC;AAC/B;AACF;GACD;AACH,CAAC;AAED,MAAMmB,eAAe,GAAG;EACtBC,KAAK,EAAEL,aAAa,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAC3C;EACAM,WAAW,EAAEN,aAAa,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9C;EACAO,SAAS,EAAEP,aAAa,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AACnD;AACAQ,EAAAA,cAAc,EAAE,UAAUC,MAAM,EAAE;IAChCA,MAAM,GAAG,IAAIC,YAAQ,CAACD,MAAM,CAAC,CAACjB,QAAQ,EAAE;IAExC,IAAI,CAACN,IAAI,CAAC;AACRyB,MAAAA,KAAK,EAAEC,IAAI,CAACC,IAAI,CAACJ,MAAM,CAACK,KAAK,CAAC,GAAG,CAAC,CAACX,MAAM,CAAC;AAC1CY,MAAAA,YAAY,EAAEN;AAChB,KAAC,CAAC;GACH;AACD;AACAO,EAAAA,eAAe,EAAEhB,aAAa,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;AACxG;AACAiB,EAAAA,eAAe,EAAEjB,aAAa,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;AAC9F;AACAkB,EAAAA,UAAU,EAAElB,aAAa,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;AAC7D;EACAmB,KAAK,EAAEnB,aAAa,CAAC,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;AACtD;EACAoB,YAAY,EAAE,UAAUC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGD,CAAC,EAAE;IACpC,IAAI,CAACnC,IAAI,CAAC,cAAc,EAAEmC,CAAC,GAAG,GAAG,GAAGC,CAAC,CAAC;GACvC;AACD;AACAC,EAAAA,KAAK,EAAE,UAAUC,GAAG,EAAE;IACpB,IAAI,CAACtC,IAAI,CAAC,MAAM,EAAEsC,GAAG,EAAEC,iBAAE,CAACC,KAAK,CAAC;GACjC;AACD;EACAC,UAAU,EAAE3B,aAAa,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AACjD;EACA4B,MAAM,EAAE5B,aAAa,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACnC;AACA6B,EAAAA,gBAAgB,EAAE7B,aAAa,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;AAC7H;AACA8B,EAAAA,IAAI,EAAE9B,aAAa,CAAC,EAAE,CAAC;AACvB;AACA+B,EAAAA,UAAU,EAAE/B,aAAa,CAAC,CAAC,eAAe,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,CAAC;AAC1F,CAAC;AAED,MAAMgC,WAAW,GAAG,CAClB,OAAO,EACP,aAAa,EACb,mBAAmB,EACnB,WAAW,EACX,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,YAAY,EACZ,OAAO,EACP,cAAc,EACd,OAAO,EACP,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,kBAAkB,EAClB,MAAM,EACN,YAAY,CACb;;AAED;AACAA,WAAW,CAACC,OAAO,CAAErC,MAAM,IAAK;AAC9B,EAAA,MAAMsC,IAAI,GAAGC,YAAK,CAACC,UAAU,CAACxC,MAAM,CAAC;AACrC,EAAA,MAAMyC,EAAE,GAAGjC,eAAe,CAACR,MAAM,CAAC;EAElCzB,MAAM,CAAC+D,IAAI,GAAG,QAAQ,CAAC,GAAG,cAAczC,MAAM,CAAC;IAC7CpB,WAAWA,CAAEC,IAAI,EAAE;MACjB,KAAK,CAACC,gBAAS,CAAC,IAAI,GAAG2D,IAAI,EAAE5D,IAAI,CAAC,EAAEA,IAAI,CAAC;AAC3C;;AAEA;AACA;IACAgE,MAAMA,CAAEpC,IAAI,EAAE;AACZmC,MAAAA,EAAE,CAACE,KAAK,CAAC,IAAI,EAAErC,IAAI,CAAC;AACpB,MAAA,OAAO,IAAI;AACb;GACD;;AAED;AACA;AACA;AACA/B,EAAAA,MAAM,CAACqE,SAAS,CAAC5C,MAAM,CAAC,GAAG6C,wBAAiB,CAAC,UAAUJ,EAAE,EAAE,GAAGnC,IAAI,EAAE;IAClE,MAAMN,MAAM,GAAG,IAAIzB,MAAM,CAAC+D,IAAI,GAAG,QAAQ,CAAC,EAAE;IAE5C,IAAIG,EAAE,IAAI,IAAI,EAAE,OAAO,IAAI,CAACtD,GAAG,CAACa,MAAM,CAAC;;AAEvC;AACA,IAAA,IAAI,OAAOyC,EAAE,KAAK,UAAU,EAAE;AAC5BA,MAAAA,EAAE,CAACK,IAAI,CAAC9C,MAAM,EAAEA,MAAM,CAAC;AACzB,KAAC,MAAM;AACL;AACAM,MAAAA,IAAI,CAACyC,OAAO,CAACN,EAAE,CAAC;AAClB;IACA,OAAO,IAAI,CAACtD,GAAG,CAACa,MAAM,CAAC,CAAC0C,MAAM,CAACpC,IAAI,CAAC;AACtC,GAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACA0C,aAAM,CAACzE,MAAM,EAAE;EACb0E,KAAKA,CAAEC,SAAS,EAAE;AAChB,IAAA,MAAMxE,IAAI,GAAG,IAAI,CAACS,GAAG,CAAC,IAAIZ,MAAM,CAAC4E,WAAW,EAAE,CAAC;;AAE/C;AACA;AACA;AACA,IAAA,IAAI,OAAOD,SAAS,KAAK,UAAU,EAAE;AACnCA,MAAAA,SAAS,CAACJ,IAAI,CAACpE,IAAI,EAAEA,IAAI,CAAC;AAC1B,MAAA,OAAOA,IAAI;AACb;;AAEA;IACA,MAAM0E,QAAQ,GAAGF,SAAS,YAAYG,KAAK,GAAGH,SAAS,GAAG,CAAC,GAAGI,SAAS,CAAC;AAExEF,IAAAA,QAAQ,CAACf,OAAO,CAAEkB,KAAK,IAAK;AAC1B,MAAA,IAAIA,KAAK,YAAYhF,MAAM,CAACiF,SAAS,EAAE;AACrC9E,QAAAA,IAAI,CAACS,GAAG,CAACoE,KAAK,CAAC;AACjB,OAAC,MAAM;AACL7E,QAAAA,IAAI,CAAC+E,SAAS,CAACF,KAAK,CAAC;AACvB;AACF,KAAC,CAAC;AAEF,IAAA,OAAO7E,IAAI;GACZ;AACDgF,EAAAA,iBAAiBA,CAAEC,UAAU,GAAG,EAAE,EAAE;AAClC,IAAA,MAAMjF,IAAI,GAAG,IAAI,CAACS,GAAG,CAAC,IAAIZ,MAAM,CAACqF,uBAAuB,EAAE,CAAC;AAE3D,IAAA,IAAI,OAAOD,UAAU,KAAK,UAAU,EAAE;AACpCA,MAAAA,UAAU,CAACb,IAAI,CAACpE,IAAI,EAAEA,IAAI,CAAC;AAC3B,MAAA,OAAOA,IAAI;AACb;;AAEA;AACA,IAAA,IAAI,CAACiF,UAAU,CAACE,CAAC,IAAI,CAACF,UAAU,CAACG,CAAC,IAAI,CAACH,UAAU,CAACI,CAAC,IAAI,CAACJ,UAAU,CAACK,CAAC,EAAE;MACpE,MAAMC,IAAI,GAAGN,UAAU;AACvBA,MAAAA,UAAU,GAAG;AACXE,QAAAA,CAAC,EAAEI,IAAI;AAAEH,QAAAA,CAAC,EAAEG,IAAI;AAAEF,QAAAA,CAAC,EAAEE,IAAI;AAAED,QAAAA,CAAC,EAAEC;OAC/B;AACH;AAEA,IAAA,KAAK,MAAMC,CAAC,IAAIP,UAAU,EAAE;AAC1B;MACAjF,IAAI,CAACyF,GAAG,CAAC,IAAI5F,MAAM,CAAC,MAAM,GAAG2F,CAAC,CAACE,WAAW,EAAE,CAAC,CAACT,UAAU,CAACO,CAAC,CAAC,CAAC,CAAC;AAC/D;AAEA,IAAA,OAAOxF,IAAI;AACb;AACF,CAAC,CAAC;AAEF,MAAM2F,gBAAgB,GAAG,CACvB,cAAc,EACd,YAAY,EACZ,WAAW,EACX,WAAW,EACX,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR;AAEDA,gBAAgB,CAAChC,OAAO,CAAEkB,KAAK,IAAK;AAClC,EAAA,MAAMjB,IAAI,GAAGC,YAAK,CAACC,UAAU,CAACe,KAAK,CAAC;AACpChF,EAAAA,MAAM,CAAC+D,IAAI,CAAC,GAAG,cAAczC,MAAM,CAAC;IAClCpB,WAAWA,CAAEC,IAAI,EAAE;MACjB,KAAK,CAACC,gBAAS,CAAC,IAAI,GAAG2D,IAAI,EAAE5D,IAAI,CAAC,EAAEA,IAAI,CAAC;AAC3C;GACD;AACH,CAAC,CAAC;AAEF,MAAM4F,cAAc,GAAG,CACrB,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR;;AAED;AACAA,cAAc,CAACjC,OAAO,CAAC,UAAU6B,CAAC,EAAE;EAClC,MAAMK,MAAM,GAAGhG,MAAM,CAACgE,YAAK,CAACC,UAAU,CAAC0B,CAAC,CAAC,CAAC;AAC1C,EAAA,MAAMzB,EAAE,GAAGI,wBAAiB,CAAC,YAAY;IACvC,OAAO,IAAI,CAAC1D,GAAG,CAAC,IAAIoF,MAAM,EAAE,CAAC;AAC/B,GAAC,CAAC;EAEFhG,MAAM,CAACqF,uBAAuB,CAAChB,SAAS,CAACsB,CAAC,CAAC,GAAGzB,EAAE;AAClD,CAAC,CAAC;AAEF,MAAM+B,MAAM,GAAG,CACb,cAAc,EACd,YAAY,EACZ,WAAW,CACZ;;AAED;AACAA,MAAM,CAACnC,OAAO,CAAEoC,KAAK,IAAK;EACxB,MAAMF,MAAM,GAAGhG,MAAM,CAACgE,YAAK,CAACC,UAAU,CAACiC,KAAK,CAAC,CAAC;AAC9C,EAAA,MAAMhC,EAAE,GAAGI,wBAAiB,CAAC,YAAY;IACvC,OAAO,IAAI,CAAC1D,GAAG,CAAC,IAAIoF,MAAM,EAAE,CAAC;AAC/B,GAAC,CAAC;EAEFhG,MAAM,CAACmG,qBAAqB,CAAC9B,SAAS,CAAC6B,KAAK,CAAC,GAAGhC,EAAE;EAClDlE,MAAM,CAACoG,sBAAsB,CAAC/B,SAAS,CAAC6B,KAAK,CAAC,GAAGhC,EAAE;AACrD,CAAC,CAAC;AAEFO,aAAM,CAACzE,MAAM,CAAC4E,WAAW,EAAE;EACzBM,SAASA,CAAExD,GAAG,EAAE;AACd,IAAA,OAAO,IAAI,CAACd,GAAG,CAAC,IAAIZ,MAAM,CAACiF,SAAS,EAAE,CAAC,CAAClE,IAAI,CAAC,IAAI,EAAEW,GAAG,CAAC;AACzD;AACF,CAAC,CAAC;;AAEF;AACA+C,aAAM,CAAC4B,WAAI,EAAE;AACX;AACAC,EAAAA,MAAM,EAAE,UAAUC,KAAK,EAAE;IACvB,MAAMD,MAAM,GAAG,IAAI,CAAC1F,GAAG,CAAC,IAAIZ,MAAM,EAAE,CAAC;;AAErC;AACA,IAAA,IAAI,OAAOuG,KAAK,KAAK,UAAU,EAAE;AAAEA,MAAAA,KAAK,CAAChC,IAAI,CAAC+B,MAAM,EAAEA,MAAM,CAAC;AAAC;AAE9D,IAAA,OAAOA,MAAM;AACf;AACF,CAAC,CAAC;AAEF7B,aAAM,CAAC+B,gBAAS,EAAE;AAChB;AACAF,EAAAA,MAAM,EAAE,UAAUC,KAAK,EAAE;IACvB,OAAO,IAAI,CAACE,IAAI,EAAE,CAACH,MAAM,CAACC,KAAK,CAAC;AAClC;AACF,CAAC,CAAC;AAEF9B,aAAM,CAACxE,cAAO,EAAE;AACd;AACAyG,EAAAA,UAAU,EAAE,UAAUH,KAAK,EAAE;AAC3B,IAAA,MAAMD,MAAM,GAAGC,KAAK,YAAYvG,MAAM,GAClCuG,KAAK,GACL,IAAI,CAACE,IAAI,EAAE,CAACH,MAAM,CAACC,KAAK,CAAC;AAE7B,IAAA,OAAO,IAAI,CAACxF,IAAI,CAAC,QAAQ,EAAEuF,MAAM,CAAC;GACnC;AACD;AACAK,EAAAA,QAAQ,EAAE,UAAU1F,MAAM,EAAE;AAC1B;AACA,IAAA,OAAO,IAAI,CAACF,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;GACjC;AACD6F,EAAAA,QAAQA,GAAI;AACV,IAAA,OAAO,IAAI,CAACC,SAAS,CAAC,QAAQ,CAAC;AACjC;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,eAAe,GAAG;AACtB;AACA5E,EAAAA,KAAK,EAAE,UAAU6E,GAAG,EAAEC,IAAI,EAAE;IAC1B,OAAO,IAAI,CAACpF,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACM,KAAK,CAAC,IAAI,EAAE6E,GAAG,EAAEC,IAAI,CAAC,CAAC;GAC9D;AACD;AACA7E,EAAAA,WAAW,EAAE,UAAU8E,IAAI,EAAEC,MAAM,EAAE;IACnC,OAAO,IAAI,CAACtF,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACO,WAAW,CAAC8E,IAAI,EAAEC,MAAM,CAAC,CAAC1F,EAAE,CAAC,IAAI,CAAC;GACzE;AACD;AACA2D,EAAAA,iBAAiB,EAAE,UAAUC,UAAU,EAAE;IACvC,OAAO,IAAI,CAACxD,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACuD,iBAAiB,CAACC,UAAU,CAAC,CAAC5D,EAAE,CAAC,IAAI,CAAC;GAC7E;AACD;AACAY,EAAAA,SAAS,EAAE,UAAU2E,GAAG,EAAEI,QAAQ,EAAE;IAClC,OAAO,IAAI,CAACvF,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACQ,SAAS,CAAC,IAAI,EAAE2E,GAAG,EAAEI,QAAQ,CAAC,CAAC;GACtE;AACD;AACA9E,EAAAA,cAAc,EAAE,UAAUC,MAAM,EAAE;IAChC,OAAO,IAAI,CAACV,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACS,cAAc,CAACC,MAAM,CAAC,CAACd,EAAE,CAAC,IAAI,CAAC;GACtE;AACD;EACAqB,eAAe,EAAE,UAAUuE,YAAY,EAAEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,EAAE;IACzF,OAAO,IAAI,CAAC3F,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACiB,eAAe,CAACuE,YAAY,EAAEE,eAAe,EAAEC,gBAAgB,CAAC,CAAC/F,EAAE,CAAC,IAAI,CAAC;GAChH;AACD;EACAsB,eAAe,EAAE,UAAUiE,GAAG,EAAES,KAAK,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAE;IACzE,OAAO,IAAI,CAAC9F,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACkB,eAAe,CAAC,IAAI,EAAEiE,GAAG,EAAES,KAAK,EAAEC,gBAAgB,EAAEC,gBAAgB,CAAC,CAAC;GAC7G;AACD;EACA3E,UAAU,EAAE,UAAUG,CAAC,EAAEC,CAAC,EAAEwE,YAAY,EAAE;IACxC,OAAO,IAAI,CAAC/F,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACmB,UAAU,CAAC,IAAI,EAAEG,CAAC,EAAEC,CAAC,EAAEwE,YAAY,CAAC,CAACnG,EAAE,CAAC,IAAI,CAAC,CAAC;GACrF;AACD;AACAwB,EAAAA,KAAK,EAAE,UAAU4E,KAAK,EAAEC,OAAO,EAAE;AAC/B,IAAA,OAAO,IAAI,CAACjG,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACoB,KAAK,CAAC4E,KAAK,EAAEC,OAAO,CAAC,CAAC;GAC7D;AACD;AACA5E,EAAAA,YAAY,EAAE,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC5B,OAAO,IAAI,CAACvB,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACqB,YAAY,CAACC,CAAC,EAAEC,CAAC,CAAC,CAAC3B,EAAE,CAAC,IAAI,CAAC;GAClE;AACD;AACA4B,EAAAA,KAAK,EAAE,UAAUC,GAAG,EAAE;AACpB,IAAA,OAAO,IAAI,CAACzB,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACwB,KAAK,CAACC,GAAG,CAAC,CAAC;GAClD;AACD;AACAqB,EAAAA,KAAK,EAAE,UAAUoD,GAAG,EAAE;IACpBA,GAAG,GAAGA,GAAG,YAAYhD,KAAK,GAAGgD,GAAG,GAAG,CAAC,GAAGA,GAAG,CAAC;AAC3C,IAAA,OAAO,IAAI,CAAClG,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAAC8C,KAAK,CAAC,IAAI,EAAE,GAAGoD,GAAG,CAAC,CAAC;GAC3D;AACD;AACAtE,EAAAA,UAAU,EAAE,UAAU2D,QAAQ,EAAEY,MAAM,EAAE;IACtC,OAAO,IAAI,CAACnG,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAAC4B,UAAU,CAAC2D,QAAQ,EAAEY,MAAM,CAAC,CAACvG,EAAE,CAAC,IAAI,CAAC;GAC5E;AACD;AACAiC,EAAAA,MAAM,EAAE,UAAUuE,EAAE,EAAEC,EAAE,EAAE;IACxB,OAAO,IAAI,CAACrG,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAAC6B,MAAM,CAACuE,EAAE,EAAEC,EAAE,CAAC,CAACzG,EAAE,CAAC,IAAI,CAAC;GAC9D;AACD;AACAkC,EAAAA,gBAAgB,EAAE,UAAU0D,YAAY,EAAEC,aAAa,EAAEC,eAAe,EAAEY,gBAAgB,EAAEX,gBAAgB,EAAE;IAC5G,OAAO,IAAI,CAAC3F,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAAC8B,gBAAgB,CAAC0D,YAAY,EAAEE,eAAe,EAAEY,gBAAgB,EAAEX,gBAAgB,CAAC,CAAC/F,EAAE,CAAC,IAAI,CAAC;GACnI;AACD;EACAmC,IAAI,EAAE,YAAY;AAChB,IAAA,OAAO,IAAI,CAAC/B,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAAC+B,IAAI,EAAE,CAACnC,EAAE,CAAC,IAAI,CAAC;GACtD;AACD;AACAoC,EAAAA,UAAU,EAAE,UAAUuE,aAAa,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAErB,IAAI,EAAE;AACxE,IAAA,OAAO,IAAI,CAACrF,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACgC,UAAU,CAACuE,aAAa,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAErB,IAAI,CAAC,CAACzF,EAAE,CAAC,IAAI,CAAC;AAC/G;AACF,CAAC;AAEDiD,aAAM,CAACnD,MAAM,EAAEwF,eAAe,CAAC;;AAE/B;AACArC,aAAM,CAACzE,MAAM,CAAC4E,WAAW,EAAE;AACzBpD,EAAAA,EAAE,EAAE,UAAUC,MAAM,EAAE;AACpB,IAAA,IAAIA,MAAM,YAAYzB,MAAM,CAACiF,SAAS,EAAE;AACtC,MAAA,IAAI,CAACW,GAAG,CAACnE,MAAM,EAAE,CAAC,CAAC;AACrB,KAAC,MAAM;AACL,MAAA,IAAI,CAACmE,GAAG,CAAC,IAAI5F,MAAM,CAACiF,SAAS,EAAE,CAACzD,EAAE,CAACC,MAAM,CAAC,EAAE,CAAC,CAAC;AAChD;AAEA,IAAA,OAAO,IAAI;AACb;AACF,CAAC,CAAC;AAEFgD,aAAM,CAAC,CAACzE,MAAM,CAACuI,eAAe,EAAEvI,MAAM,CAACwI,WAAW,EAAExI,MAAM,CAACyI,qBAAqB,CAAC,EAAE;AACjF1B,EAAAA,GAAG,EAAE,UAAUtF,MAAM,EAAE;IACrB,IAAIA,MAAM,IAAI,IAAI,EAAE;AAClB,MAAA,MAAMsF,GAAG,GAAG,IAAI,CAAChG,IAAI,CAAC,KAAK,CAAC;MAC5B,MAAMY,GAAG,GAAG,IAAI,CAACC,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACR,IAAI,CAAC,CAAA,SAAA,EAAY2F,GAAG,CAAI,EAAA,CAAA,CAAC,CAAC,CAAC,CAAC;MACvE,OAAOpF,GAAG,IAAIoF,GAAG;AACnB;AACA,IAAA,OAAO,IAAI,CAAChG,IAAI,CAAC,KAAK,EAAEU,MAAM,CAAC;AACjC;AACF,CAAC,CAAC;;AAEF;AACAzB,MAAM,CAACsG,MAAM,GAAG;AACdoC,EAAAA,SAAS,EAAE,CACT,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EACzB,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EACzB,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EACzB,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AAC7B,CAAC;;;;"}