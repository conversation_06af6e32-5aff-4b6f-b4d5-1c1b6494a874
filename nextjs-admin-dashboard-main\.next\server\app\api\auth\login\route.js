/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Bhushan_patil_OneDrive_Desktop_Book_my_Service_new_nextjs_admin_dashboard_main_src_app_api_auth_login_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/login/route.js */ \"(rsc)/./src/app/api/auth/login/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\api\\\\auth\\\\login\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Bhushan_patil_OneDrive_Desktop_Book_my_Service_new_nextjs_admin_dashboard_main_src_app_api_auth_login_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/login/route.js":
/*!*****************************************!*\
  !*** ./src/app/api/auth/login/route.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.js\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.js\");\n/* harmony import */ var _middleware_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/middleware/auth */ \"(rsc)/./src/middleware/auth.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.js\");\n\n\n\n\n\nasync function POST(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const { email } = await request.json();\n        // Validate input\n        if (!email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Email is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.validateEmail)(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Please provide a valid email address'\n            }, {\n                status: 400\n            });\n        }\n        // Find user by email\n        const user = await _models_User__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findOne({\n            email: email.toLowerCase()\n        });\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'No account found with this email address'\n            }, {\n                status: 404\n            });\n        }\n        if (!user.isActive) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Your account has been deactivated. Please contact support.'\n            }, {\n                status: 403\n            });\n        }\n        // Generate OTP\n        const otp = (0,_middleware_auth__WEBPACK_IMPORTED_MODULE_3__.generateOTP)();\n        const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes\n        // Save OTP to user\n        user.otp = otp;\n        user.otpExpires = otpExpires;\n        await user.save();\n        // TODO: Send OTP via email (implement email service)\n        console.log(`OTP for ${email}: ${otp}`); // For development\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'OTP sent to your email address',\n            data: {\n                email: user.email,\n                // In development, return OTP for testing\n                ... true && {\n                    otp\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Login error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'An error occurred during login'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: false,\n        message: 'Method not allowed'\n    }, {\n        status: 405\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/login/route.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.js":
/*!****************************!*\
  !*** ./src/lib/mongodb.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.js":
/*!**************************!*\
  !*** ./src/lib/utils.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_BASE_URL: () => (/* binding */ API_BASE_URL),\n/* harmony export */   capitalizeFirst: () => (/* binding */ capitalizeFirst),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateOTP: () => (/* binding */ generateOTP),\n/* harmony export */   getAuthHeaders: () => (/* binding */ getAuthHeaders),\n/* harmony export */   getBookingStatusText: () => (/* binding */ getBookingStatusText),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\nfunction formatCurrency(amount) {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n    }).format(amount);\n}\nfunction formatTime(date) {\n    const d = new Date(date);\n    return d.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\nfunction generateOTP() {\n    return Math.floor(100000 + Math.random() * 900000).toString();\n}\nfunction validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction validatePhone(phone) {\n    const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;\n    return phoneRegex.test(phone);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n}\nfunction capitalizeFirst(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\nfunction getInitials(name) {\n    return name.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction getStatusColor(status) {\n    switch(status.toLowerCase()){\n        case 'pending':\n            return 'text-yellow-600 bg-yellow-100';\n        case 'confirmed':\n            return 'text-blue-600 bg-blue-100';\n        case 'completed':\n            return 'text-green-600 bg-green-100';\n        case 'cancelled':\n        case 'cancelled_by_provider':\n            return 'text-red-600 bg-red-100';\n        default:\n            return 'text-gray-600 bg-gray-100';\n    }\n}\nfunction getBookingStatusText(status) {\n    switch(status.toLowerCase()){\n        case 'pending':\n            return 'Pending Approval';\n        case 'confirmed':\n            return 'Confirmed';\n        case 'completed':\n            return 'Completed';\n        case 'cancelled':\n            return 'Cancelled by User';\n        case 'cancelled_by_provider':\n            return 'Cancelled by Provider';\n        default:\n            return status;\n    }\n}\nconst API_BASE_URL =  false ? 0 : '/api';\nfunction getAuthHeaders() {\n    const token =  false ? 0 : null;\n    return {\n        'Content-Type': 'application/json',\n        ...token && {\n            Authorization: `Bearer ${token}`\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.js\n");

/***/ }),

/***/ "(rsc)/./src/middleware/auth.js":
/*!********************************!*\
  !*** ./src/middleware/auth.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateUser: () => (/* binding */ authenticateUser),\n/* harmony export */   generateOTP: () => (/* binding */ generateOTP),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   optionalAuth: () => (/* binding */ optionalAuth),\n/* harmony export */   requireAdmin: () => (/* binding */ requireAdmin),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   requireBusinessOwner: () => (/* binding */ requireBusinessOwner),\n/* harmony export */   requireRole: () => (/* binding */ requireRole),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.js\");\n\n\n\nasync function verifyToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, process.env.JWT_SECRET);\n        return decoded;\n    } catch (error) {\n        throw new Error('Invalid token');\n    }\n}\nasync function authenticateUser(req) {\n    try {\n        const authHeader = req.headers.authorization;\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            throw new Error('No token provided');\n        }\n        const token = authHeader.substring(7);\n        const decoded = await verifyToken(token);\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(decoded.userId).select('-otp -otpExpires -resetPasswordToken -resetPasswordExpires');\n        if (!user) {\n            throw new Error('User not found');\n        }\n        if (!user.isActive) {\n            throw new Error('User account is deactivated');\n        }\n        return user;\n    } catch (error) {\n        throw error;\n    }\n}\nfunction requireAuth(handler) {\n    return async (req, res)=>{\n        try {\n            const user = await authenticateUser(req);\n            req.user = user;\n            return handler(req, res);\n        } catch (error) {\n            return res.status(401).json({\n                success: false,\n                message: error.message || 'Authentication required'\n            });\n        }\n    };\n}\nfunction requireRole(roles) {\n    return (handler)=>{\n        return requireAuth(async (req, res)=>{\n            const userRole = req.user.role;\n            if (!roles.includes(userRole)) {\n                return res.status(403).json({\n                    success: false,\n                    message: 'Insufficient permissions'\n                });\n            }\n            return handler(req, res);\n        });\n    };\n}\nfunction requireBusinessOwner(handler) {\n    return requireRole([\n        'business_owner',\n        'admin'\n    ])(handler);\n}\nfunction requireAdmin(handler) {\n    return requireRole([\n        'admin'\n    ])(handler);\n}\n// Middleware for optional authentication (doesn't fail if no token)\nfunction optionalAuth(handler) {\n    return async (req, res)=>{\n        try {\n            const user = await authenticateUser(req);\n            req.user = user;\n        } catch (error) {\n            // Continue without user if authentication fails\n            req.user = null;\n        }\n        return handler(req, res);\n    };\n}\nfunction generateToken(userId) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n        userId\n    }, process.env.JWT_SECRET, {\n        expiresIn: process.env.JWT_EXPIRE || '7d'\n    });\n}\nfunction generateOTP() {\n    return Math.floor(100000 + Math.random() * 900000).toString();\n}\nfunction isTokenExpired(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().decode(token);\n        const currentTime = Date.now() / 1000;\n        return decoded.exp < currentTime;\n    } catch (error) {\n        return true;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/middleware/auth.js\n");

/***/ }),

/***/ "(rsc)/./src/models/User.js":
/*!****************************!*\
  !*** ./src/models/User.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    firstName: {\n        type: String,\n        required: [\n            true,\n            'First name is required'\n        ],\n        trim: true,\n        maxlength: [\n            50,\n            'First name cannot be more than 50 characters'\n        ]\n    },\n    lastName: {\n        type: String,\n        required: [\n            true,\n            'Last name is required'\n        ],\n        trim: true,\n        maxlength: [\n            50,\n            'Last name cannot be more than 50 characters'\n        ]\n    },\n    email: {\n        type: String,\n        required: [\n            true,\n            'Email is required'\n        ],\n        unique: true,\n        lowercase: true,\n        trim: true,\n        match: [\n            /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n            'Please enter a valid email'\n        ]\n    },\n    phoneNumber: {\n        type: String,\n        trim: true,\n        match: [\n            /^\\+?[\\d\\s\\-\\(\\)]{10,}$/,\n            'Please enter a valid phone number'\n        ]\n    },\n    role: {\n        type: String,\n        enum: [\n            'user',\n            'business_owner',\n            'admin'\n        ],\n        default: 'user'\n    },\n    isEmailVerified: {\n        type: Boolean,\n        default: false\n    },\n    profileImage: {\n        type: String,\n        default: null\n    },\n    address: {\n        street: String,\n        city: String,\n        state: String,\n        zipCode: String,\n        country: String\n    },\n    preferences: {\n        notifications: {\n            email: {\n                type: Boolean,\n                default: true\n            },\n            sms: {\n                type: Boolean,\n                default: false\n            }\n        },\n        language: {\n            type: String,\n            default: 'en'\n        },\n        timezone: {\n            type: String,\n            default: 'UTC'\n        }\n    },\n    // For business owners\n    businessName: String,\n    businessCategory: String,\n    businessDescription: String,\n    businessAddress: {\n        street: String,\n        city: String,\n        state: String,\n        zipCode: String,\n        country: String\n    },\n    businessLogo: String,\n    businessPhone: String,\n    businessEmail: String,\n    businessWebsite: String,\n    businessHours: {\n        monday: {\n            open: String,\n            close: String,\n            closed: Boolean\n        },\n        tuesday: {\n            open: String,\n            close: String,\n            closed: Boolean\n        },\n        wednesday: {\n            open: String,\n            close: String,\n            closed: Boolean\n        },\n        thursday: {\n            open: String,\n            close: String,\n            closed: Boolean\n        },\n        friday: {\n            open: String,\n            close: String,\n            closed: Boolean\n        },\n        saturday: {\n            open: String,\n            close: String,\n            closed: Boolean\n        },\n        sunday: {\n            open: String,\n            close: String,\n            closed: Boolean\n        }\n    },\n    // Tracking\n    lastLogin: Date,\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    // OTP for verification\n    otp: String,\n    otpExpires: Date,\n    // Password reset\n    resetPasswordToken: String,\n    resetPasswordExpires: Date\n}, {\n    timestamps: true\n});\n// Indexes\nUserSchema.index({\n    email: 1\n});\nUserSchema.index({\n    role: 1\n});\nUserSchema.index({\n    'businessAddress.city': 1\n});\nUserSchema.index({\n    businessCategory: 1\n});\n// Virtual for full name\nUserSchema.virtual('fullName').get(function() {\n    return `${this.firstName} ${this.lastName}`;\n});\n// Virtual for business full address\nUserSchema.virtual('businessFullAddress').get(function() {\n    if (!this.businessAddress) return '';\n    const { street, city, state, zipCode, country } = this.businessAddress;\n    return [\n        street,\n        city,\n        state,\n        zipCode,\n        country\n    ].filter(Boolean).join(', ');\n});\n// Ensure virtual fields are serialized\nUserSchema.set('toJSON', {\n    virtuals: true\n});\nUserSchema.set('toObject', {\n    virtuals: true\n});\n// Pre-save middleware\nUserSchema.pre('save', function(next) {\n    if (this.isModified('email')) {\n        this.email = this.email.toLowerCase();\n    }\n    next();\n});\n// Instance methods\nUserSchema.methods.toSafeObject = function() {\n    const userObject = this.toObject();\n    delete userObject.otp;\n    delete userObject.otpExpires;\n    delete userObject.resetPasswordToken;\n    delete userObject.resetPasswordExpires;\n    return userObject;\n};\nUserSchema.methods.isBusinessOwner = function() {\n    return this.role === 'business_owner';\n};\nUserSchema.methods.isAdmin = function() {\n    return this.role === 'admin';\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('User', UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbW9kZWxzL1VzZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBRWhDLE1BQU1DLGFBQWEsSUFBSUQsd0RBQWUsQ0FBQztJQUNyQ0csV0FBVztRQUNUQyxNQUFNQztRQUNOQyxVQUFVO1lBQUM7WUFBTTtTQUF5QjtRQUMxQ0MsTUFBTTtRQUNOQyxXQUFXO1lBQUM7WUFBSTtTQUErQztJQUNqRTtJQUNBQyxVQUFVO1FBQ1JMLE1BQU1DO1FBQ05DLFVBQVU7WUFBQztZQUFNO1NBQXdCO1FBQ3pDQyxNQUFNO1FBQ05DLFdBQVc7WUFBQztZQUFJO1NBQThDO0lBQ2hFO0lBQ0FFLE9BQU87UUFDTE4sTUFBTUM7UUFDTkMsVUFBVTtZQUFDO1lBQU07U0FBb0I7UUFDckNLLFFBQVE7UUFDUkMsV0FBVztRQUNYTCxNQUFNO1FBQ05NLE9BQU87WUFBQztZQUErQztTQUE2QjtJQUN0RjtJQUNBQyxhQUFhO1FBQ1hWLE1BQU1DO1FBQ05FLE1BQU07UUFDTk0sT0FBTztZQUFDO1lBQTBCO1NBQW9DO0lBQ3hFO0lBQ0FFLE1BQU07UUFDSlgsTUFBTUM7UUFDTlcsTUFBTTtZQUFDO1lBQVE7WUFBa0I7U0FBUTtRQUN6Q0MsU0FBUztJQUNYO0lBQ0FDLGlCQUFpQjtRQUNmZCxNQUFNZTtRQUNORixTQUFTO0lBQ1g7SUFDQUcsY0FBYztRQUNaaEIsTUFBTUM7UUFDTlksU0FBUztJQUNYO0lBQ0FJLFNBQVM7UUFDUEMsUUFBUWpCO1FBQ1JrQixNQUFNbEI7UUFDTm1CLE9BQU9uQjtRQUNQb0IsU0FBU3BCO1FBQ1RxQixTQUFTckI7SUFDWDtJQUNBc0IsYUFBYTtRQUNYQyxlQUFlO1lBQ2JsQixPQUFPO2dCQUFFTixNQUFNZTtnQkFBU0YsU0FBUztZQUFLO1lBQ3RDWSxLQUFLO2dCQUFFekIsTUFBTWU7Z0JBQVNGLFNBQVM7WUFBTTtRQUN2QztRQUNBYSxVQUFVO1lBQUUxQixNQUFNQztZQUFRWSxTQUFTO1FBQUs7UUFDeENjLFVBQVU7WUFBRTNCLE1BQU1DO1lBQVFZLFNBQVM7UUFBTTtJQUMzQztJQUNBLHNCQUFzQjtJQUN0QmUsY0FBYzNCO0lBQ2Q0QixrQkFBa0I1QjtJQUNsQjZCLHFCQUFxQjdCO0lBQ3JCOEIsaUJBQWlCO1FBQ2ZiLFFBQVFqQjtRQUNSa0IsTUFBTWxCO1FBQ05tQixPQUFPbkI7UUFDUG9CLFNBQVNwQjtRQUNUcUIsU0FBU3JCO0lBQ1g7SUFDQStCLGNBQWMvQjtJQUNkZ0MsZUFBZWhDO0lBQ2ZpQyxlQUFlakM7SUFDZmtDLGlCQUFpQmxDO0lBQ2pCbUMsZUFBZTtRQUNiQyxRQUFRO1lBQUVDLE1BQU1yQztZQUFRc0MsT0FBT3RDO1lBQVF1QyxRQUFRekI7UUFBUTtRQUN2RDBCLFNBQVM7WUFBRUgsTUFBTXJDO1lBQVFzQyxPQUFPdEM7WUFBUXVDLFFBQVF6QjtRQUFRO1FBQ3hEMkIsV0FBVztZQUFFSixNQUFNckM7WUFBUXNDLE9BQU90QztZQUFRdUMsUUFBUXpCO1FBQVE7UUFDMUQ0QixVQUFVO1lBQUVMLE1BQU1yQztZQUFRc0MsT0FBT3RDO1lBQVF1QyxRQUFRekI7UUFBUTtRQUN6RDZCLFFBQVE7WUFBRU4sTUFBTXJDO1lBQVFzQyxPQUFPdEM7WUFBUXVDLFFBQVF6QjtRQUFRO1FBQ3ZEOEIsVUFBVTtZQUFFUCxNQUFNckM7WUFBUXNDLE9BQU90QztZQUFRdUMsUUFBUXpCO1FBQVE7UUFDekQrQixRQUFRO1lBQUVSLE1BQU1yQztZQUFRc0MsT0FBT3RDO1lBQVF1QyxRQUFRekI7UUFBUTtJQUN6RDtJQUNBLFdBQVc7SUFDWGdDLFdBQVdDO0lBQ1hDLFVBQVU7UUFDUmpELE1BQU1lO1FBQ05GLFNBQVM7SUFDWDtJQUNBLHVCQUF1QjtJQUN2QnFDLEtBQUtqRDtJQUNMa0QsWUFBWUg7SUFDWixpQkFBaUI7SUFDakJJLG9CQUFvQm5EO0lBQ3BCb0Qsc0JBQXNCTDtBQUN4QixHQUFHO0lBQ0RNLFlBQVk7QUFDZDtBQUVBLFVBQVU7QUFDVnpELFdBQVcwRCxLQUFLLENBQUM7SUFBRWpELE9BQU87QUFBRTtBQUM1QlQsV0FBVzBELEtBQUssQ0FBQztJQUFFNUMsTUFBTTtBQUFFO0FBQzNCZCxXQUFXMEQsS0FBSyxDQUFDO0lBQUUsd0JBQXdCO0FBQUU7QUFDN0MxRCxXQUFXMEQsS0FBSyxDQUFDO0lBQUUxQixrQkFBa0I7QUFBRTtBQUV2Qyx3QkFBd0I7QUFDeEJoQyxXQUFXMkQsT0FBTyxDQUFDLFlBQVlDLEdBQUcsQ0FBQztJQUNqQyxPQUFPLEdBQUcsSUFBSSxDQUFDMUQsU0FBUyxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUNNLFFBQVEsRUFBRTtBQUM3QztBQUVBLG9DQUFvQztBQUNwQ1IsV0FBVzJELE9BQU8sQ0FBQyx1QkFBdUJDLEdBQUcsQ0FBQztJQUM1QyxJQUFJLENBQUMsSUFBSSxDQUFDMUIsZUFBZSxFQUFFLE9BQU87SUFDbEMsTUFBTSxFQUFFYixNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxPQUFPLEVBQUVDLE9BQU8sRUFBRSxHQUFHLElBQUksQ0FBQ1MsZUFBZTtJQUN0RSxPQUFPO1FBQUNiO1FBQVFDO1FBQU1DO1FBQU9DO1FBQVNDO0tBQVEsQ0FBQ29DLE1BQU0sQ0FBQzNDLFNBQVM0QyxJQUFJLENBQUM7QUFDdEU7QUFFQSx1Q0FBdUM7QUFDdkM5RCxXQUFXK0QsR0FBRyxDQUFDLFVBQVU7SUFBRUMsVUFBVTtBQUFLO0FBQzFDaEUsV0FBVytELEdBQUcsQ0FBQyxZQUFZO0lBQUVDLFVBQVU7QUFBSztBQUU1QyxzQkFBc0I7QUFDdEJoRSxXQUFXaUUsR0FBRyxDQUFDLFFBQVEsU0FBU0MsSUFBSTtJQUNsQyxJQUFJLElBQUksQ0FBQ0MsVUFBVSxDQUFDLFVBQVU7UUFDNUIsSUFBSSxDQUFDMUQsS0FBSyxHQUFHLElBQUksQ0FBQ0EsS0FBSyxDQUFDMkQsV0FBVztJQUNyQztJQUNBRjtBQUNGO0FBRUEsbUJBQW1CO0FBQ25CbEUsV0FBV3FFLE9BQU8sQ0FBQ0MsWUFBWSxHQUFHO0lBQ2hDLE1BQU1DLGFBQWEsSUFBSSxDQUFDQyxRQUFRO0lBQ2hDLE9BQU9ELFdBQVdsQixHQUFHO0lBQ3JCLE9BQU9rQixXQUFXakIsVUFBVTtJQUM1QixPQUFPaUIsV0FBV2hCLGtCQUFrQjtJQUNwQyxPQUFPZ0IsV0FBV2Ysb0JBQW9CO0lBQ3RDLE9BQU9lO0FBQ1Q7QUFFQXZFLFdBQVdxRSxPQUFPLENBQUNJLGVBQWUsR0FBRztJQUNuQyxPQUFPLElBQUksQ0FBQzNELElBQUksS0FBSztBQUN2QjtBQUVBZCxXQUFXcUUsT0FBTyxDQUFDSyxPQUFPLEdBQUc7SUFDM0IsT0FBTyxJQUFJLENBQUM1RCxJQUFJLEtBQUs7QUFDdkI7QUFFQSxpRUFBZWYsd0RBQWUsQ0FBQzZFLElBQUksSUFBSTdFLHFEQUFjLENBQUMsUUFBUUMsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxCaHVzaGFuIHBhdGlsXFxPbmVEcml2ZVxcRGVza3RvcFxcQm9vayBteSBTZXJ2aWNlIG5ld1xcbmV4dGpzLWFkbWluLWRhc2hib2FyZC1tYWluXFxzcmNcXG1vZGVsc1xcVXNlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UgZnJvbSAnbW9uZ29vc2UnO1xuXG5jb25zdCBVc2VyU2NoZW1hID0gbmV3IG1vbmdvb3NlLlNjaGVtYSh7XG4gIGZpcnN0TmFtZToge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICByZXF1aXJlZDogW3RydWUsICdGaXJzdCBuYW1lIGlzIHJlcXVpcmVkJ10sXG4gICAgdHJpbTogdHJ1ZSxcbiAgICBtYXhsZW5ndGg6IFs1MCwgJ0ZpcnN0IG5hbWUgY2Fubm90IGJlIG1vcmUgdGhhbiA1MCBjaGFyYWN0ZXJzJ11cbiAgfSxcbiAgbGFzdE5hbWU6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgcmVxdWlyZWQ6IFt0cnVlLCAnTGFzdCBuYW1lIGlzIHJlcXVpcmVkJ10sXG4gICAgdHJpbTogdHJ1ZSxcbiAgICBtYXhsZW5ndGg6IFs1MCwgJ0xhc3QgbmFtZSBjYW5ub3QgYmUgbW9yZSB0aGFuIDUwIGNoYXJhY3RlcnMnXVxuICB9LFxuICBlbWFpbDoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICByZXF1aXJlZDogW3RydWUsICdFbWFpbCBpcyByZXF1aXJlZCddLFxuICAgIHVuaXF1ZTogdHJ1ZSxcbiAgICBsb3dlcmNhc2U6IHRydWUsXG4gICAgdHJpbTogdHJ1ZSxcbiAgICBtYXRjaDogWy9eXFx3KyhbLi1dP1xcdyspKkBcXHcrKFsuLV0/XFx3KykqKFxcLlxcd3syLDN9KSskLywgJ1BsZWFzZSBlbnRlciBhIHZhbGlkIGVtYWlsJ11cbiAgfSxcbiAgcGhvbmVOdW1iZXI6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgdHJpbTogdHJ1ZSxcbiAgICBtYXRjaDogWy9eXFwrP1tcXGRcXHNcXC1cXChcXCldezEwLH0kLywgJ1BsZWFzZSBlbnRlciBhIHZhbGlkIHBob25lIG51bWJlciddXG4gIH0sXG4gIHJvbGU6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgZW51bTogWyd1c2VyJywgJ2J1c2luZXNzX293bmVyJywgJ2FkbWluJ10sXG4gICAgZGVmYXVsdDogJ3VzZXInXG4gIH0sXG4gIGlzRW1haWxWZXJpZmllZDoge1xuICAgIHR5cGU6IEJvb2xlYW4sXG4gICAgZGVmYXVsdDogZmFsc2VcbiAgfSxcbiAgcHJvZmlsZUltYWdlOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIGRlZmF1bHQ6IG51bGxcbiAgfSxcbiAgYWRkcmVzczoge1xuICAgIHN0cmVldDogU3RyaW5nLFxuICAgIGNpdHk6IFN0cmluZyxcbiAgICBzdGF0ZTogU3RyaW5nLFxuICAgIHppcENvZGU6IFN0cmluZyxcbiAgICBjb3VudHJ5OiBTdHJpbmdcbiAgfSxcbiAgcHJlZmVyZW5jZXM6IHtcbiAgICBub3RpZmljYXRpb25zOiB7XG4gICAgICBlbWFpbDogeyB0eXBlOiBCb29sZWFuLCBkZWZhdWx0OiB0cnVlIH0sXG4gICAgICBzbXM6IHsgdHlwZTogQm9vbGVhbiwgZGVmYXVsdDogZmFsc2UgfVxuICAgIH0sXG4gICAgbGFuZ3VhZ2U6IHsgdHlwZTogU3RyaW5nLCBkZWZhdWx0OiAnZW4nIH0sXG4gICAgdGltZXpvbmU6IHsgdHlwZTogU3RyaW5nLCBkZWZhdWx0OiAnVVRDJyB9XG4gIH0sXG4gIC8vIEZvciBidXNpbmVzcyBvd25lcnNcbiAgYnVzaW5lc3NOYW1lOiBTdHJpbmcsXG4gIGJ1c2luZXNzQ2F0ZWdvcnk6IFN0cmluZyxcbiAgYnVzaW5lc3NEZXNjcmlwdGlvbjogU3RyaW5nLFxuICBidXNpbmVzc0FkZHJlc3M6IHtcbiAgICBzdHJlZXQ6IFN0cmluZyxcbiAgICBjaXR5OiBTdHJpbmcsXG4gICAgc3RhdGU6IFN0cmluZyxcbiAgICB6aXBDb2RlOiBTdHJpbmcsXG4gICAgY291bnRyeTogU3RyaW5nXG4gIH0sXG4gIGJ1c2luZXNzTG9nbzogU3RyaW5nLFxuICBidXNpbmVzc1Bob25lOiBTdHJpbmcsXG4gIGJ1c2luZXNzRW1haWw6IFN0cmluZyxcbiAgYnVzaW5lc3NXZWJzaXRlOiBTdHJpbmcsXG4gIGJ1c2luZXNzSG91cnM6IHtcbiAgICBtb25kYXk6IHsgb3BlbjogU3RyaW5nLCBjbG9zZTogU3RyaW5nLCBjbG9zZWQ6IEJvb2xlYW4gfSxcbiAgICB0dWVzZGF5OiB7IG9wZW46IFN0cmluZywgY2xvc2U6IFN0cmluZywgY2xvc2VkOiBCb29sZWFuIH0sXG4gICAgd2VkbmVzZGF5OiB7IG9wZW46IFN0cmluZywgY2xvc2U6IFN0cmluZywgY2xvc2VkOiBCb29sZWFuIH0sXG4gICAgdGh1cnNkYXk6IHsgb3BlbjogU3RyaW5nLCBjbG9zZTogU3RyaW5nLCBjbG9zZWQ6IEJvb2xlYW4gfSxcbiAgICBmcmlkYXk6IHsgb3BlbjogU3RyaW5nLCBjbG9zZTogU3RyaW5nLCBjbG9zZWQ6IEJvb2xlYW4gfSxcbiAgICBzYXR1cmRheTogeyBvcGVuOiBTdHJpbmcsIGNsb3NlOiBTdHJpbmcsIGNsb3NlZDogQm9vbGVhbiB9LFxuICAgIHN1bmRheTogeyBvcGVuOiBTdHJpbmcsIGNsb3NlOiBTdHJpbmcsIGNsb3NlZDogQm9vbGVhbiB9XG4gIH0sXG4gIC8vIFRyYWNraW5nXG4gIGxhc3RMb2dpbjogRGF0ZSxcbiAgaXNBY3RpdmU6IHtcbiAgICB0eXBlOiBCb29sZWFuLFxuICAgIGRlZmF1bHQ6IHRydWVcbiAgfSxcbiAgLy8gT1RQIGZvciB2ZXJpZmljYXRpb25cbiAgb3RwOiBTdHJpbmcsXG4gIG90cEV4cGlyZXM6IERhdGUsXG4gIC8vIFBhc3N3b3JkIHJlc2V0XG4gIHJlc2V0UGFzc3dvcmRUb2tlbjogU3RyaW5nLFxuICByZXNldFBhc3N3b3JkRXhwaXJlczogRGF0ZVxufSwge1xuICB0aW1lc3RhbXBzOiB0cnVlXG59KTtcblxuLy8gSW5kZXhlc1xuVXNlclNjaGVtYS5pbmRleCh7IGVtYWlsOiAxIH0pO1xuVXNlclNjaGVtYS5pbmRleCh7IHJvbGU6IDEgfSk7XG5Vc2VyU2NoZW1hLmluZGV4KHsgJ2J1c2luZXNzQWRkcmVzcy5jaXR5JzogMSB9KTtcblVzZXJTY2hlbWEuaW5kZXgoeyBidXNpbmVzc0NhdGVnb3J5OiAxIH0pO1xuXG4vLyBWaXJ0dWFsIGZvciBmdWxsIG5hbWVcblVzZXJTY2hlbWEudmlydHVhbCgnZnVsbE5hbWUnKS5nZXQoZnVuY3Rpb24oKSB7XG4gIHJldHVybiBgJHt0aGlzLmZpcnN0TmFtZX0gJHt0aGlzLmxhc3ROYW1lfWA7XG59KTtcblxuLy8gVmlydHVhbCBmb3IgYnVzaW5lc3MgZnVsbCBhZGRyZXNzXG5Vc2VyU2NoZW1hLnZpcnR1YWwoJ2J1c2luZXNzRnVsbEFkZHJlc3MnKS5nZXQoZnVuY3Rpb24oKSB7XG4gIGlmICghdGhpcy5idXNpbmVzc0FkZHJlc3MpIHJldHVybiAnJztcbiAgY29uc3QgeyBzdHJlZXQsIGNpdHksIHN0YXRlLCB6aXBDb2RlLCBjb3VudHJ5IH0gPSB0aGlzLmJ1c2luZXNzQWRkcmVzcztcbiAgcmV0dXJuIFtzdHJlZXQsIGNpdHksIHN0YXRlLCB6aXBDb2RlLCBjb3VudHJ5XS5maWx0ZXIoQm9vbGVhbikuam9pbignLCAnKTtcbn0pO1xuXG4vLyBFbnN1cmUgdmlydHVhbCBmaWVsZHMgYXJlIHNlcmlhbGl6ZWRcblVzZXJTY2hlbWEuc2V0KCd0b0pTT04nLCB7IHZpcnR1YWxzOiB0cnVlIH0pO1xuVXNlclNjaGVtYS5zZXQoJ3RvT2JqZWN0JywgeyB2aXJ0dWFsczogdHJ1ZSB9KTtcblxuLy8gUHJlLXNhdmUgbWlkZGxld2FyZVxuVXNlclNjaGVtYS5wcmUoJ3NhdmUnLCBmdW5jdGlvbihuZXh0KSB7XG4gIGlmICh0aGlzLmlzTW9kaWZpZWQoJ2VtYWlsJykpIHtcbiAgICB0aGlzLmVtYWlsID0gdGhpcy5lbWFpbC50b0xvd2VyQ2FzZSgpO1xuICB9XG4gIG5leHQoKTtcbn0pO1xuXG4vLyBJbnN0YW5jZSBtZXRob2RzXG5Vc2VyU2NoZW1hLm1ldGhvZHMudG9TYWZlT2JqZWN0ID0gZnVuY3Rpb24oKSB7XG4gIGNvbnN0IHVzZXJPYmplY3QgPSB0aGlzLnRvT2JqZWN0KCk7XG4gIGRlbGV0ZSB1c2VyT2JqZWN0Lm90cDtcbiAgZGVsZXRlIHVzZXJPYmplY3Qub3RwRXhwaXJlcztcbiAgZGVsZXRlIHVzZXJPYmplY3QucmVzZXRQYXNzd29yZFRva2VuO1xuICBkZWxldGUgdXNlck9iamVjdC5yZXNldFBhc3N3b3JkRXhwaXJlcztcbiAgcmV0dXJuIHVzZXJPYmplY3Q7XG59O1xuXG5Vc2VyU2NoZW1hLm1ldGhvZHMuaXNCdXNpbmVzc093bmVyID0gZnVuY3Rpb24oKSB7XG4gIHJldHVybiB0aGlzLnJvbGUgPT09ICdidXNpbmVzc19vd25lcic7XG59O1xuXG5Vc2VyU2NoZW1hLm1ldGhvZHMuaXNBZG1pbiA9IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gdGhpcy5yb2xlID09PSAnYWRtaW4nO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgbW9uZ29vc2UubW9kZWxzLlVzZXIgfHwgbW9uZ29vc2UubW9kZWwoJ1VzZXInLCBVc2VyU2NoZW1hKTtcbiJdLCJuYW1lcyI6WyJtb25nb29zZSIsIlVzZXJTY2hlbWEiLCJTY2hlbWEiLCJmaXJzdE5hbWUiLCJ0eXBlIiwiU3RyaW5nIiwicmVxdWlyZWQiLCJ0cmltIiwibWF4bGVuZ3RoIiwibGFzdE5hbWUiLCJlbWFpbCIsInVuaXF1ZSIsImxvd2VyY2FzZSIsIm1hdGNoIiwicGhvbmVOdW1iZXIiLCJyb2xlIiwiZW51bSIsImRlZmF1bHQiLCJpc0VtYWlsVmVyaWZpZWQiLCJCb29sZWFuIiwicHJvZmlsZUltYWdlIiwiYWRkcmVzcyIsInN0cmVldCIsImNpdHkiLCJzdGF0ZSIsInppcENvZGUiLCJjb3VudHJ5IiwicHJlZmVyZW5jZXMiLCJub3RpZmljYXRpb25zIiwic21zIiwibGFuZ3VhZ2UiLCJ0aW1lem9uZSIsImJ1c2luZXNzTmFtZSIsImJ1c2luZXNzQ2F0ZWdvcnkiLCJidXNpbmVzc0Rlc2NyaXB0aW9uIiwiYnVzaW5lc3NBZGRyZXNzIiwiYnVzaW5lc3NMb2dvIiwiYnVzaW5lc3NQaG9uZSIsImJ1c2luZXNzRW1haWwiLCJidXNpbmVzc1dlYnNpdGUiLCJidXNpbmVzc0hvdXJzIiwibW9uZGF5Iiwib3BlbiIsImNsb3NlIiwiY2xvc2VkIiwidHVlc2RheSIsIndlZG5lc2RheSIsInRodXJzZGF5IiwiZnJpZGF5Iiwic2F0dXJkYXkiLCJzdW5kYXkiLCJsYXN0TG9naW4iLCJEYXRlIiwiaXNBY3RpdmUiLCJvdHAiLCJvdHBFeHBpcmVzIiwicmVzZXRQYXNzd29yZFRva2VuIiwicmVzZXRQYXNzd29yZEV4cGlyZXMiLCJ0aW1lc3RhbXBzIiwiaW5kZXgiLCJ2aXJ0dWFsIiwiZ2V0IiwiZmlsdGVyIiwiam9pbiIsInNldCIsInZpcnR1YWxzIiwicHJlIiwibmV4dCIsImlzTW9kaWZpZWQiLCJ0b0xvd2VyQ2FzZSIsIm1ldGhvZHMiLCJ0b1NhZmVPYmplY3QiLCJ1c2VyT2JqZWN0IiwidG9PYmplY3QiLCJpc0J1c2luZXNzT3duZXIiLCJpc0FkbWluIiwibW9kZWxzIiwiVXNlciIsIm1vZGVsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();