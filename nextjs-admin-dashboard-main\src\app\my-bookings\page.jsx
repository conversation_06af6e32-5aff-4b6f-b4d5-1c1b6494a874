"use client";

import { useState, useEffect } from 'react';

export default function MyBookingsPage() {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading bookings
    setTimeout(() => {
      setBookings([
        {
          id: 1,
          service: "House Cleaning",
          provider: "Jane's Cleaning Service",
          date: "2024-01-20",
          time: "10:00 AM",
          status: "confirmed",
          amount: 120,
          address: "123 Main St, City, State"
        },
        {
          id: 2,
          service: "Plumbing Repair",
          provider: "Mike's Plumbing",
          date: "2024-01-18",
          time: "2:00 PM",
          status: "completed",
          amount: 85,
          address: "456 Oak Ave, City, State"
        },
        {
          id: 3,
          service: "Garden Maintenance",
          provider: "Green Thumb Landscaping",
          date: "2024-01-25",
          time: "9:00 AM",
          status: "pending",
          amount: 200,
          address: "789 Pine Rd, City, State"
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-48"></div>
        <div className="space-y-4">
          {[1, 2, 3].map((item) => (
            <div key={item} className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
              <div className="space-y-3">
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-heading-3 font-bold text-dark dark:text-white">
          My Bookings
        </h1>
        <div className="flex items-center gap-4">
          <select className="rounded-lg border border-gray-300 px-4 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-dark dark:text-white">
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      <div className="space-y-4">
        {bookings.map((booking) => (
          <div key={booking.id} className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-lg font-semibold text-dark dark:text-white">
                      {booking.service}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      by {booking.provider}
                    </p>
                  </div>
                  <span className={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${getStatusColor(booking.status)}`}>
                    {booking.status}
                  </span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-dark dark:text-white">Date & Time:</span>
                    <p className="text-gray-600 dark:text-gray-400">
                      {booking.date} at {booking.time}
                    </p>
                  </div>
                  
                  <div>
                    <span className="font-medium text-dark dark:text-white">Location:</span>
                    <p className="text-gray-600 dark:text-gray-400">
                      {booking.address}
                    </p>
                  </div>
                  
                  <div>
                    <span className="font-medium text-dark dark:text-white">Amount:</span>
                    <p className="text-lg font-bold text-primary">
                      ${booking.amount}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-4 flex items-center gap-3">
              {booking.status === 'pending' && (
                <button className="rounded-lg bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors">
                  Cancel Booking
                </button>
              )}
              
              {booking.status === 'confirmed' && (
                <button className="rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                  View Details
                </button>
              )}
              
              {booking.status === 'completed' && (
                <button className="rounded-lg bg-green-600 px-4 py-2 text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors">
                  Leave Review
                </button>
              )}
              
              <button className="rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800 transition-colors">
                Contact Provider
              </button>
            </div>
          </div>
        ))}
      </div>

      {bookings.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="mx-auto h-24 w-24 text-gray-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">No bookings found</h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            You haven't made any bookings yet. Browse our services to get started.
          </p>
          <div className="mt-6">
            <a
              href="/services"
              className="inline-flex items-center rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-blue-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
            >
              Browse Services
            </a>
          </div>
        </div>
      )}
    </div>
  );
}
