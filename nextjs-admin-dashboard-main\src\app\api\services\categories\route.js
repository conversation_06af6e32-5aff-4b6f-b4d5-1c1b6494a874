import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Service from '@/models/Service';
import { apiResponse } from '@/lib/apiResponse';

export async function GET() {
  try {
    await connectDB();
    
    // Get distinct categories from active services
    const categories = await Service.distinct('category', { isActive: true });
    
    // Sort categories alphabetically
    categories.sort();

    return apiResponse.success(categories, 'Categories fetched successfully');

  } catch (error) {
    console.error('Error fetching categories:', error);
    return apiResponse.error('Failed to fetch categories', 500);
  }
}
