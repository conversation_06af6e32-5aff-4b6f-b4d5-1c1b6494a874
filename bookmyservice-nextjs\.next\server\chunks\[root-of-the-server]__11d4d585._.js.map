{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Book%20my%20Service%20new/bookmyservice-nextjs/src/lib/mongodb.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Book%20my%20Service%20new/bookmyservice-nextjs/src/models/User.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst UserSchema = new mongoose.Schema({\n  firstName: {\n    type: String,\n    required: [true, 'First name is required'],\n    trim: true,\n    maxlength: [50, 'First name cannot be more than 50 characters']\n  },\n  lastName: {\n    type: String,\n    required: [true, 'Last name is required'],\n    trim: true,\n    maxlength: [50, 'Last name cannot be more than 50 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Please enter a valid email']\n  },\n  phoneNumber: {\n    type: String,\n    trim: true,\n    match: [/^\\+?[\\d\\s\\-\\(\\)]{10,}$/, 'Please enter a valid phone number']\n  },\n  role: {\n    type: String,\n    enum: ['user', 'business_owner', 'admin'],\n    default: 'user'\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false\n  },\n  profileImage: {\n    type: String,\n    default: null\n  },\n  address: {\n    street: String,\n    city: String,\n    state: String,\n    zipCode: String,\n    country: String\n  },\n  preferences: {\n    notifications: {\n      email: { type: Boolean, default: true },\n      sms: { type: Boolean, default: false }\n    },\n    language: { type: String, default: 'en' },\n    timezone: { type: String, default: 'UTC' }\n  },\n  // For business owners\n  businessName: String,\n  businessCategory: String,\n  businessDescription: String,\n  businessAddress: {\n    street: String,\n    city: String,\n    state: String,\n    zipCode: String,\n    country: String\n  },\n  businessLogo: String,\n  businessPhone: String,\n  businessEmail: String,\n  businessWebsite: String,\n  businessHours: {\n    monday: { open: String, close: String, closed: Boolean },\n    tuesday: { open: String, close: String, closed: Boolean },\n    wednesday: { open: String, close: String, closed: Boolean },\n    thursday: { open: String, close: String, closed: Boolean },\n    friday: { open: String, close: String, closed: Boolean },\n    saturday: { open: String, close: String, closed: Boolean },\n    sunday: { open: String, close: String, closed: Boolean }\n  },\n  // Tracking\n  lastLogin: Date,\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n  // OTP for verification\n  otp: String,\n  otpExpires: Date,\n  // Password reset\n  resetPasswordToken: String,\n  resetPasswordExpires: Date\n}, {\n  timestamps: true\n});\n\n// Indexes\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ 'businessAddress.city': 1 });\nUserSchema.index({ businessCategory: 1 });\n\n// Virtual for full name\nUserSchema.virtual('fullName').get(function() {\n  return `${this.firstName} ${this.lastName}`;\n});\n\n// Virtual for business full address\nUserSchema.virtual('businessFullAddress').get(function() {\n  if (!this.businessAddress) return '';\n  const { street, city, state, zipCode, country } = this.businessAddress;\n  return [street, city, state, zipCode, country].filter(Boolean).join(', ');\n});\n\n// Ensure virtual fields are serialized\nUserSchema.set('toJSON', { virtuals: true });\nUserSchema.set('toObject', { virtuals: true });\n\n// Pre-save middleware\nUserSchema.pre('save', function(next) {\n  if (this.isModified('email')) {\n    this.email = this.email.toLowerCase();\n  }\n  next();\n});\n\n// Instance methods\nUserSchema.methods.toSafeObject = function() {\n  const userObject = this.toObject();\n  delete userObject.otp;\n  delete userObject.otpExpires;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpires;\n  return userObject;\n};\n\nUserSchema.methods.isBusinessOwner = function() {\n  return this.role === 'business_owner';\n};\n\nUserSchema.methods.isAdmin = function() {\n  return this.role === 'admin';\n};\n\nexport default mongoose.models.User || mongoose.model('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAI;SAA+C;IACjE;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC;YAAI;SAA8C;IAChE;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YAAC;YAA+C;SAA6B;IACtF;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAA0B;SAAoC;IACxE;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAQ;YAAkB;SAAQ;QACzC,SAAS;IACX;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,cAAc;QACZ,MAAM;QACN,SAAS;IACX;IACA,SAAS;QACP,QAAQ;QACR,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,aAAa;QACX,eAAe;YACb,OAAO;gBAAE,MAAM;gBAAS,SAAS;YAAK;YACtC,KAAK;gBAAE,MAAM;gBAAS,SAAS;YAAM;QACvC;QACA,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAK;QACxC,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAM;IAC3C;IACA,sBAAsB;IACtB,cAAc;IACd,kBAAkB;IAClB,qBAAqB;IACrB,iBAAiB;QACf,QAAQ;QACR,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,cAAc;IACd,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,eAAe;QACb,QAAQ;YAAE,MAAM;YAAQ,OAAO;YAAQ,QAAQ;QAAQ;QACvD,SAAS;YAAE,MAAM;YAAQ,OAAO;YAAQ,QAAQ;QAAQ;QACxD,WAAW;YAAE,MAAM;YAAQ,OAAO;YAAQ,QAAQ;QAAQ;QAC1D,UAAU;YAAE,MAAM;YAAQ,OAAO;YAAQ,QAAQ;QAAQ;QACzD,QAAQ;YAAE,MAAM;YAAQ,OAAO;YAAQ,QAAQ;QAAQ;QACvD,UAAU;YAAE,MAAM;YAAQ,OAAO;YAAQ,QAAQ;QAAQ;QACzD,QAAQ;YAAE,MAAM;YAAQ,OAAO;YAAQ,QAAQ;QAAQ;IACzD;IACA,WAAW;IACX,WAAW;IACX,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,uBAAuB;IACvB,KAAK;IACL,YAAY;IACZ,iBAAiB;IACjB,oBAAoB;IACpB,sBAAsB;AACxB,GAAG;IACD,YAAY;AACd;AAEA,UAAU;AACV,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,wBAAwB;AAAE;AAC7C,WAAW,KAAK,CAAC;IAAE,kBAAkB;AAAE;AAEvC,wBAAwB;AACxB,WAAW,OAAO,CAAC,YAAY,GAAG,CAAC;IACjC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE;AAC7C;AAEA,oCAAoC;AACpC,WAAW,OAAO,CAAC,uBAAuB,GAAG,CAAC;IAC5C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,eAAe;IACtE,OAAO;QAAC;QAAQ;QAAM;QAAO;QAAS;KAAQ,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;AACtE;AAEA,uCAAuC;AACvC,WAAW,GAAG,CAAC,UAAU;IAAE,UAAU;AAAK;AAC1C,WAAW,GAAG,CAAC,YAAY;IAAE,UAAU;AAAK;AAE5C,sBAAsB;AACtB,WAAW,GAAG,CAAC,QAAQ,SAAS,IAAI;IAClC,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU;QAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW;IACrC;IACA;AACF;AAEA,mBAAmB;AACnB,WAAW,OAAO,CAAC,YAAY,GAAG;IAChC,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,GAAG;IACrB,OAAO,WAAW,UAAU;IAC5B,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,oBAAoB;IACtC,OAAO;AACT;AAEA,WAAW,OAAO,CAAC,eAAe,GAAG;IACnC,OAAO,IAAI,CAAC,IAAI,KAAK;AACvB;AAEA,WAAW,OAAO,CAAC,OAAO,GAAG;IAC3B,OAAO,IAAI,CAAC,IAAI,KAAK;AACvB;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Book%20my%20Service%20new/bookmyservice-nextjs/src/middleware/auth.js"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport User from '@/models/User';\nimport connectDB from '@/lib/mongodb';\n\nexport async function verifyToken(token) {\n  try {\n    const decoded = jwt.verify(token, process.env.JWT_SECRET);\n    return decoded;\n  } catch (error) {\n    throw new Error('Invalid token');\n  }\n}\n\nexport async function authenticateUser(req) {\n  try {\n    const authHeader = req.headers.authorization;\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      throw new Error('No token provided');\n    }\n\n    const token = authHeader.substring(7);\n    const decoded = await verifyToken(token);\n    \n    await connectDB();\n    const user = await User.findById(decoded.userId).select('-otp -otpExpires -resetPasswordToken -resetPasswordExpires');\n    \n    if (!user) {\n      throw new Error('User not found');\n    }\n\n    if (!user.isActive) {\n      throw new Error('User account is deactivated');\n    }\n\n    return user;\n  } catch (error) {\n    throw error;\n  }\n}\n\nexport function requireAuth(handler) {\n  return async (req, res) => {\n    try {\n      const user = await authenticateUser(req);\n      req.user = user;\n      return handler(req, res);\n    } catch (error) {\n      return res.status(401).json({\n        success: false,\n        message: error.message || 'Authentication required'\n      });\n    }\n  };\n}\n\nexport function requireRole(roles) {\n  return (handler) => {\n    return requireAuth(async (req, res) => {\n      const userRole = req.user.role;\n      \n      if (!roles.includes(userRole)) {\n        return res.status(403).json({\n          success: false,\n          message: 'Insufficient permissions'\n        });\n      }\n      \n      return handler(req, res);\n    });\n  };\n}\n\nexport function requireBusinessOwner(handler) {\n  return requireRole(['business_owner', 'admin'])(handler);\n}\n\nexport function requireAdmin(handler) {\n  return requireRole(['admin'])(handler);\n}\n\n// Middleware for optional authentication (doesn't fail if no token)\nexport function optionalAuth(handler) {\n  return async (req, res) => {\n    try {\n      const user = await authenticateUser(req);\n      req.user = user;\n    } catch (error) {\n      // Continue without user if authentication fails\n      req.user = null;\n    }\n    return handler(req, res);\n  };\n}\n\nexport function generateToken(userId) {\n  return jwt.sign(\n    { userId },\n    process.env.JWT_SECRET,\n    { expiresIn: process.env.JWT_EXPIRE || '7d' }\n  );\n}\n\nexport function generateOTP() {\n  return Math.floor(100000 + Math.random() * 900000).toString();\n}\n\nexport function isTokenExpired(token) {\n  try {\n    const decoded = jwt.decode(token);\n    const currentTime = Date.now() / 1000;\n    return decoded.exp < currentTime;\n  } catch (error) {\n    return true;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe,YAAY,KAAK;IACrC,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU;QACxD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,iBAAiB,GAAG;IACxC,IAAI;QACF,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;QAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,MAAM,YAAY;QAElC,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM,EAAE,MAAM,CAAC;QAExD,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM;IACR;AACF;AAEO,SAAS,YAAY,OAAO;IACjC,OAAO,OAAO,KAAK;QACjB,IAAI;YACF,MAAM,OAAO,MAAM,iBAAiB;YACpC,IAAI,IAAI,GAAG;YACX,OAAO,QAAQ,KAAK;QACtB,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS,MAAM,OAAO,IAAI;YAC5B;QACF;IACF;AACF;AAEO,SAAS,YAAY,KAAK;IAC/B,OAAO,CAAC;QACN,OAAO,YAAY,OAAO,KAAK;YAC7B,MAAM,WAAW,IAAI,IAAI,CAAC,IAAI;YAE9B,IAAI,CAAC,MAAM,QAAQ,CAAC,WAAW;gBAC7B,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAC1B,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,OAAO,QAAQ,KAAK;QACtB;IACF;AACF;AAEO,SAAS,qBAAqB,OAAO;IAC1C,OAAO,YAAY;QAAC;QAAkB;KAAQ,EAAE;AAClD;AAEO,SAAS,aAAa,OAAO;IAClC,OAAO,YAAY;QAAC;KAAQ,EAAE;AAChC;AAGO,SAAS,aAAa,OAAO;IAClC,OAAO,OAAO,KAAK;QACjB,IAAI;YACF,MAAM,OAAO,MAAM,iBAAiB;YACpC,IAAI,IAAI,GAAG;QACb,EAAE,OAAO,OAAO;YACd,gDAAgD;YAChD,IAAI,IAAI,GAAG;QACb;QACA,OAAO,QAAQ,KAAK;IACtB;AACF;AAEO,SAAS,cAAc,MAAM;IAClC,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CACb;QAAE;IAAO,GACT,QAAQ,GAAG,CAAC,UAAU,EACtB;QAAE,WAAW,QAAQ,GAAG,CAAC,UAAU,IAAI;IAAK;AAEhD;AAEO,SAAS;IACd,OAAO,KAAK,KAAK,CAAC,SAAS,KAAK,MAAM,KAAK,QAAQ,QAAQ;AAC7D;AAEO,SAAS,eAAe,KAAK;IAClC,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC;QAC3B,MAAM,cAAc,KAAK,GAAG,KAAK;QACjC,OAAO,QAAQ,GAAG,GAAG;IACvB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Book%20my%20Service%20new/bookmyservice-nextjs/src/lib/apiResponse.js"], "sourcesContent": ["export function sendResponse(res, data = null, message = '', success = true, statusCode = 200) {\n  return res.status(statusCode).json({\n    success,\n    message,\n    data,\n    timestamp: new Date().toISOString()\n  });\n}\n\nexport function sendSuccess(res, data = null, message = 'Success', statusCode = 200) {\n  return sendResponse(res, data, message, true, statusCode);\n}\n\nexport function sendError(res, message = 'An error occurred', statusCode = 500, data = null) {\n  return sendResponse(res, data, message, false, statusCode);\n}\n\nexport function sendValidationError(res, errors, message = 'Validation failed') {\n  return sendResponse(res, { errors }, message, false, 400);\n}\n\nexport function sendNotFound(res, message = 'Resource not found') {\n  return sendError(res, message, 404);\n}\n\nexport function sendUnauthorized(res, message = 'Unauthorized access') {\n  return sendError(res, message, 401);\n}\n\nexport function sendForbidden(res, message = 'Forbidden access') {\n  return sendError(res, message, 403);\n}\n\nexport function sendConflict(res, message = 'Resource conflict') {\n  return sendError(res, message, 409);\n}\n\nexport function sendTooManyRequests(res, message = 'Too many requests') {\n  return sendError(res, message, 429);\n}\n\nexport function handleApiError(res, error) {\n  console.error('API Error:', error);\n  \n  // Mongoose validation error\n  if (error.name === 'ValidationError') {\n    const errors = Object.values(error.errors).map(err => ({\n      field: err.path,\n      message: err.message\n    }));\n    return sendValidationError(res, errors);\n  }\n  \n  // Mongoose duplicate key error\n  if (error.code === 11000) {\n    const field = Object.keys(error.keyPattern)[0];\n    return sendConflict(res, `${field} already exists`);\n  }\n  \n  // JWT errors\n  if (error.name === 'JsonWebTokenError') {\n    return sendUnauthorized(res, 'Invalid token');\n  }\n  \n  if (error.name === 'TokenExpiredError') {\n    return sendUnauthorized(res, 'Token expired');\n  }\n  \n  // Custom application errors\n  if (error.statusCode) {\n    return sendError(res, error.message, error.statusCode);\n  }\n  \n  // Default server error\n  return sendError(res, 'Internal server error', 500);\n}\n\nexport class ApiError extends Error {\n  constructor(message, statusCode = 500, data = null) {\n    super(message);\n    this.statusCode = statusCode;\n    this.data = data;\n    this.name = 'ApiError';\n  }\n}\n\nexport function createApiError(message, statusCode = 500, data = null) {\n  return new ApiError(message, statusCode, data);\n}\n\n// Common error creators\nexport const errors = {\n  notFound: (resource = 'Resource') => createApiError(`${resource} not found`, 404),\n  unauthorized: (message = 'Unauthorized access') => createApiError(message, 401),\n  forbidden: (message = 'Forbidden access') => createApiError(message, 403),\n  badRequest: (message = 'Bad request') => createApiError(message, 400),\n  conflict: (message = 'Resource conflict') => createApiError(message, 409),\n  tooManyRequests: (message = 'Too many requests') => createApiError(message, 429),\n  internal: (message = 'Internal server error') => createApiError(message, 500)\n};\n\n// Async handler wrapper to catch errors\nexport function asyncHandler(handler) {\n  return async (req, res) => {\n    try {\n      await handler(req, res);\n    } catch (error) {\n      handleApiError(res, error);\n    }\n  };\n}\n\n// Method not allowed handler\nexport function methodNotAllowed(res, allowedMethods = []) {\n  res.setHeader('Allow', allowedMethods.join(', '));\n  return sendError(res, `Method ${res.req?.method} not allowed`, 405);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAO,SAAS,aAAa,GAAG,EAAE,OAAO,IAAI,EAAE,UAAU,EAAE,EAAE,UAAU,IAAI,EAAE,aAAa,GAAG;IAC3F,OAAO,IAAI,MAAM,CAAC,YAAY,IAAI,CAAC;QACjC;QACA;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAEO,SAAS,YAAY,GAAG,EAAE,OAAO,IAAI,EAAE,UAAU,SAAS,EAAE,aAAa,GAAG;IACjF,OAAO,aAAa,KAAK,MAAM,SAAS,MAAM;AAChD;AAEO,SAAS,UAAU,GAAG,EAAE,UAAU,mBAAmB,EAAE,aAAa,GAAG,EAAE,OAAO,IAAI;IACzF,OAAO,aAAa,KAAK,MAAM,SAAS,OAAO;AACjD;AAEO,SAAS,oBAAoB,GAAG,EAAE,MAAM,EAAE,UAAU,mBAAmB;IAC5E,OAAO,aAAa,KAAK;QAAE;IAAO,GAAG,SAAS,OAAO;AACvD;AAEO,SAAS,aAAa,GAAG,EAAE,UAAU,oBAAoB;IAC9D,OAAO,UAAU,KAAK,SAAS;AACjC;AAEO,SAAS,iBAAiB,GAAG,EAAE,UAAU,qBAAqB;IACnE,OAAO,UAAU,KAAK,SAAS;AACjC;AAEO,SAAS,cAAc,GAAG,EAAE,UAAU,kBAAkB;IAC7D,OAAO,UAAU,KAAK,SAAS;AACjC;AAEO,SAAS,aAAa,GAAG,EAAE,UAAU,mBAAmB;IAC7D,OAAO,UAAU,KAAK,SAAS;AACjC;AAEO,SAAS,oBAAoB,GAAG,EAAE,UAAU,mBAAmB;IACpE,OAAO,UAAU,KAAK,SAAS;AACjC;AAEO,SAAS,eAAe,GAAG,EAAE,KAAK;IACvC,QAAQ,KAAK,CAAC,cAAc;IAE5B,4BAA4B;IAC5B,IAAI,MAAM,IAAI,KAAK,mBAAmB;QACpC,MAAM,SAAS,OAAO,MAAM,CAAC,MAAM,MAAM,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;gBACrD,OAAO,IAAI,IAAI;gBACf,SAAS,IAAI,OAAO;YACtB,CAAC;QACD,OAAO,oBAAoB,KAAK;IAClC;IAEA,+BAA+B;IAC/B,IAAI,MAAM,IAAI,KAAK,OAAO;QACxB,MAAM,QAAQ,OAAO,IAAI,CAAC,MAAM,UAAU,CAAC,CAAC,EAAE;QAC9C,OAAO,aAAa,KAAK,GAAG,MAAM,eAAe,CAAC;IACpD;IAEA,aAAa;IACb,IAAI,MAAM,IAAI,KAAK,qBAAqB;QACtC,OAAO,iBAAiB,KAAK;IAC/B;IAEA,IAAI,MAAM,IAAI,KAAK,qBAAqB;QACtC,OAAO,iBAAiB,KAAK;IAC/B;IAEA,4BAA4B;IAC5B,IAAI,MAAM,UAAU,EAAE;QACpB,OAAO,UAAU,KAAK,MAAM,OAAO,EAAE,MAAM,UAAU;IACvD;IAEA,uBAAuB;IACvB,OAAO,UAAU,KAAK,yBAAyB;AACjD;AAEO,MAAM,iBAAiB;IAC5B,YAAY,OAAO,EAAE,aAAa,GAAG,EAAE,OAAO,IAAI,CAAE;QAClD,KAAK,CAAC;QACN,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,SAAS,eAAe,OAAO,EAAE,aAAa,GAAG,EAAE,OAAO,IAAI;IACnE,OAAO,IAAI,SAAS,SAAS,YAAY;AAC3C;AAGO,MAAM,SAAS;IACpB,UAAU,CAAC,WAAW,UAAU,GAAK,eAAe,GAAG,SAAS,UAAU,CAAC,EAAE;IAC7E,cAAc,CAAC,UAAU,qBAAqB,GAAK,eAAe,SAAS;IAC3E,WAAW,CAAC,UAAU,kBAAkB,GAAK,eAAe,SAAS;IACrE,YAAY,CAAC,UAAU,aAAa,GAAK,eAAe,SAAS;IACjE,UAAU,CAAC,UAAU,mBAAmB,GAAK,eAAe,SAAS;IACrE,iBAAiB,CAAC,UAAU,mBAAmB,GAAK,eAAe,SAAS;IAC5E,UAAU,CAAC,UAAU,uBAAuB,GAAK,eAAe,SAAS;AAC3E;AAGO,SAAS,aAAa,OAAO;IAClC,OAAO,OAAO,KAAK;QACjB,IAAI;YACF,MAAM,QAAQ,KAAK;QACrB,EAAE,OAAO,OAAO;YACd,eAAe,KAAK;QACtB;IACF;AACF;AAGO,SAAS,iBAAiB,GAAG,EAAE,iBAAiB,EAAE;IACvD,IAAI,SAAS,CAAC,SAAS,eAAe,IAAI,CAAC;IAC3C,OAAO,UAAU,KAAK,CAAC,OAAO,EAAE,IAAI,GAAG,EAAE,OAAO,YAAY,CAAC,EAAE;AACjE", "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Book%20my%20Service%20new/bookmyservice-nextjs/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date) {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\nexport function formatCurrency(amount) {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD'\n  }).format(amount);\n}\n\nexport function formatTime(date) {\n  const d = new Date(date);\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function generateOTP() {\n  return Math.floor(100000 + Math.random() * 900000).toString();\n}\n\nexport function validateEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhone(phone) {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;\n  return phoneRegex.test(phone);\n}\n\nexport function truncateText(text, maxLength) {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function capitalizeFirst(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function getInitials(name) {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2);\n}\n\nexport function debounce(func, wait) {\n  let timeout;\n  return (...args) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function getStatusColor(status) {\n  switch (status.toLowerCase()) {\n    case 'pending':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'confirmed':\n      return 'text-blue-600 bg-blue-100';\n    case 'completed':\n      return 'text-green-600 bg-green-100';\n    case 'cancelled':\n    case 'cancelled_by_provider':\n      return 'text-red-600 bg-red-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function getBookingStatusText(status) {\n  switch (status.toLowerCase()) {\n    case 'pending':\n      return 'Pending Approval';\n    case 'confirmed':\n      return 'Confirmed';\n    case 'completed':\n      return 'Completed';\n    case 'cancelled':\n      return 'Cancelled by User';\n    case 'cancelled_by_provider':\n      return 'Cancelled by Provider';\n    default:\n      return status;\n  }\n}\n\nexport const API_BASE_URL = process.env.NODE_ENV === 'production' \n  ? 'https://bookmyservice.onrender.com/api'\n  : '/api';\n\nexport function getAuthHeaders() {\n  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;\n  return {\n    'Content-Type': 'application/json',\n    ...(token && { Authorization: `Bearer ${token}` })\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAI;IAC7B,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAM;IACnC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAI;IAC7B,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS;IACd,OAAO,KAAK,KAAK,CAAC,SAAS,KAAK,MAAM,KAAK,QAAQ,QAAQ;AAC7D;AAEO,SAAS,cAAc,KAAK;IACjC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAK;IACjC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,IAAI,EAAE,SAAS;IAC1C,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,gBAAgB,GAAG;IACjC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,YAAY,IAAI;IAC9B,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,eAAe,MAAM;IACnC,OAAQ,OAAO,WAAW;QACxB,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,qBAAqB,MAAM;IACzC,OAAQ,OAAO,WAAW;QACxB,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,eAAe,6EAExB;AAEG,SAAS;IACd,MAAM,QAAQ,6EAAgE;IAC9E,OAAO;QACL,gBAAgB;QAChB,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF", "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Book%20my%20Service%20new/bookmyservice-nextjs/src/app/api/auth/login/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport User from '@/models/User';\nimport { generateToken, generateOTP } from '@/middleware/auth';\nimport { sendSuccess, sendError, handleApiError } from '@/lib/apiResponse';\nimport { validateEmail } from '@/lib/utils';\n\nexport async function POST(request) {\n  try {\n    await connectDB();\n    \n    const { email } = await request.json();\n    \n    // Validate input\n    if (!email) {\n      return NextResponse.json({\n        success: false,\n        message: 'Email is required'\n      }, { status: 400 });\n    }\n    \n    if (!validateEmail(email)) {\n      return NextResponse.json({\n        success: false,\n        message: 'Please provide a valid email address'\n      }, { status: 400 });\n    }\n    \n    // Find user by email\n    const user = await User.findOne({ email: email.toLowerCase() });\n    \n    if (!user) {\n      return NextResponse.json({\n        success: false,\n        message: 'No account found with this email address'\n      }, { status: 404 });\n    }\n    \n    if (!user.isActive) {\n      return NextResponse.json({\n        success: false,\n        message: 'Your account has been deactivated. Please contact support.'\n      }, { status: 403 });\n    }\n    \n    // Generate OTP\n    const otp = generateOTP();\n    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes\n    \n    // Save OTP to user\n    user.otp = otp;\n    user.otpExpires = otpExpires;\n    await user.save();\n    \n    // TODO: Send OTP via email (implement email service)\n    console.log(`OTP for ${email}: ${otp}`); // For development\n    \n    return NextResponse.json({\n      success: true,\n      message: 'OTP sent to your email address',\n      data: {\n        email: user.email,\n        // In development, return OTP for testing\n        ...(process.env.NODE_ENV === 'development' && { otp })\n      }\n    });\n    \n  } catch (error) {\n    console.error('Login error:', error);\n    return NextResponse.json({\n      success: false,\n      message: 'An error occurred during login'\n    }, { status: 500 });\n  }\n}\n\nexport async function GET() {\n  return NextResponse.json({\n    success: false,\n    message: 'Method not allowed'\n  }, { status: 405 });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpC,iBAAiB;QACjB,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,qBAAqB;QACrB,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,MAAM,WAAW;QAAG;QAE7D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,eAAe;QACf,MAAM,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;QACtB,MAAM,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,aAAa;QAEvE,mBAAmB;QACnB,KAAK,GAAG,GAAG;QACX,KAAK,UAAU,GAAG;QAClB,MAAM,KAAK,IAAI;QAEf,qDAAqD;QACrD,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG,kBAAkB;QAE3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,OAAO,KAAK,KAAK;gBACjB,yCAAyC;gBACzC,GAAI,oDAAyB,iBAAiB;oBAAE;gBAAI,CAAC;YACvD;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,SAAS;IACX,GAAG;QAAE,QAAQ;IAAI;AACnB", "debugId": null}}]}