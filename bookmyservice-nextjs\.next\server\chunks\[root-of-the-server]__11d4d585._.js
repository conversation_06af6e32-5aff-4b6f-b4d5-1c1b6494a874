module.exports = {

"[project]/.next-internal/server/app/api/auth/login/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/User.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema({
    firstName: {
        type: String,
        required: [
            true,
            'First name is required'
        ],
        trim: true,
        maxlength: [
            50,
            'First name cannot be more than 50 characters'
        ]
    },
    lastName: {
        type: String,
        required: [
            true,
            'Last name is required'
        ],
        trim: true,
        maxlength: [
            50,
            'Last name cannot be more than 50 characters'
        ]
    },
    email: {
        type: String,
        required: [
            true,
            'Email is required'
        ],
        unique: true,
        lowercase: true,
        trim: true,
        match: [
            /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
            'Please enter a valid email'
        ]
    },
    phoneNumber: {
        type: String,
        trim: true,
        match: [
            /^\+?[\d\s\-\(\)]{10,}$/,
            'Please enter a valid phone number'
        ]
    },
    role: {
        type: String,
        enum: [
            'user',
            'business_owner',
            'admin'
        ],
        default: 'user'
    },
    isEmailVerified: {
        type: Boolean,
        default: false
    },
    profileImage: {
        type: String,
        default: null
    },
    address: {
        street: String,
        city: String,
        state: String,
        zipCode: String,
        country: String
    },
    preferences: {
        notifications: {
            email: {
                type: Boolean,
                default: true
            },
            sms: {
                type: Boolean,
                default: false
            }
        },
        language: {
            type: String,
            default: 'en'
        },
        timezone: {
            type: String,
            default: 'UTC'
        }
    },
    // For business owners
    businessName: String,
    businessCategory: String,
    businessDescription: String,
    businessAddress: {
        street: String,
        city: String,
        state: String,
        zipCode: String,
        country: String
    },
    businessLogo: String,
    businessPhone: String,
    businessEmail: String,
    businessWebsite: String,
    businessHours: {
        monday: {
            open: String,
            close: String,
            closed: Boolean
        },
        tuesday: {
            open: String,
            close: String,
            closed: Boolean
        },
        wednesday: {
            open: String,
            close: String,
            closed: Boolean
        },
        thursday: {
            open: String,
            close: String,
            closed: Boolean
        },
        friday: {
            open: String,
            close: String,
            closed: Boolean
        },
        saturday: {
            open: String,
            close: String,
            closed: Boolean
        },
        sunday: {
            open: String,
            close: String,
            closed: Boolean
        }
    },
    // Tracking
    lastLogin: Date,
    isActive: {
        type: Boolean,
        default: true
    },
    // OTP for verification
    otp: String,
    otpExpires: Date,
    // Password reset
    resetPasswordToken: String,
    resetPasswordExpires: Date
}, {
    timestamps: true
});
// Indexes
UserSchema.index({
    email: 1
});
UserSchema.index({
    role: 1
});
UserSchema.index({
    'businessAddress.city': 1
});
UserSchema.index({
    businessCategory: 1
});
// Virtual for full name
UserSchema.virtual('fullName').get(function() {
    return `${this.firstName} ${this.lastName}`;
});
// Virtual for business full address
UserSchema.virtual('businessFullAddress').get(function() {
    if (!this.businessAddress) return '';
    const { street, city, state, zipCode, country } = this.businessAddress;
    return [
        street,
        city,
        state,
        zipCode,
        country
    ].filter(Boolean).join(', ');
});
// Ensure virtual fields are serialized
UserSchema.set('toJSON', {
    virtuals: true
});
UserSchema.set('toObject', {
    virtuals: true
});
// Pre-save middleware
UserSchema.pre('save', function(next) {
    if (this.isModified('email')) {
        this.email = this.email.toLowerCase();
    }
    next();
});
// Instance methods
UserSchema.methods.toSafeObject = function() {
    const userObject = this.toObject();
    delete userObject.otp;
    delete userObject.otpExpires;
    delete userObject.resetPasswordToken;
    delete userObject.resetPasswordExpires;
    return userObject;
};
UserSchema.methods.isBusinessOwner = function() {
    return this.role === 'business_owner';
};
UserSchema.methods.isAdmin = function() {
    return this.role === 'admin';
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/middleware/auth.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authenticateUser": (()=>authenticateUser),
    "generateOTP": (()=>generateOTP),
    "generateToken": (()=>generateToken),
    "isTokenExpired": (()=>isTokenExpired),
    "optionalAuth": (()=>optionalAuth),
    "requireAdmin": (()=>requireAdmin),
    "requireAuth": (()=>requireAuth),
    "requireBusinessOwner": (()=>requireBusinessOwner),
    "requireRole": (()=>requireRole),
    "verifyToken": (()=>verifyToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.js [app-route] (ecmascript)");
;
;
;
async function verifyToken(token) {
    try {
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, process.env.JWT_SECRET);
        return decoded;
    } catch (error) {
        throw new Error('Invalid token');
    }
}
async function authenticateUser(req) {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new Error('No token provided');
        }
        const token = authHeader.substring(7);
        const decoded = await verifyToken(token);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(decoded.userId).select('-otp -otpExpires -resetPasswordToken -resetPasswordExpires');
        if (!user) {
            throw new Error('User not found');
        }
        if (!user.isActive) {
            throw new Error('User account is deactivated');
        }
        return user;
    } catch (error) {
        throw error;
    }
}
function requireAuth(handler) {
    return async (req, res)=>{
        try {
            const user = await authenticateUser(req);
            req.user = user;
            return handler(req, res);
        } catch (error) {
            return res.status(401).json({
                success: false,
                message: error.message || 'Authentication required'
            });
        }
    };
}
function requireRole(roles) {
    return (handler)=>{
        return requireAuth(async (req, res)=>{
            const userRole = req.user.role;
            if (!roles.includes(userRole)) {
                return res.status(403).json({
                    success: false,
                    message: 'Insufficient permissions'
                });
            }
            return handler(req, res);
        });
    };
}
function requireBusinessOwner(handler) {
    return requireRole([
        'business_owner',
        'admin'
    ])(handler);
}
function requireAdmin(handler) {
    return requireRole([
        'admin'
    ])(handler);
}
function optionalAuth(handler) {
    return async (req, res)=>{
        try {
            const user = await authenticateUser(req);
            req.user = user;
        } catch (error) {
            // Continue without user if authentication fails
            req.user = null;
        }
        return handler(req, res);
    };
}
function generateToken(userId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign({
        userId
    }, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRE || '7d'
    });
}
function generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}
function isTokenExpired(token) {
    try {
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].decode(token);
        const currentTime = Date.now() / 1000;
        return decoded.exp < currentTime;
    } catch (error) {
        return true;
    }
}
}}),
"[project]/src/lib/apiResponse.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiError": (()=>ApiError),
    "asyncHandler": (()=>asyncHandler),
    "createApiError": (()=>createApiError),
    "errors": (()=>errors),
    "handleApiError": (()=>handleApiError),
    "methodNotAllowed": (()=>methodNotAllowed),
    "sendConflict": (()=>sendConflict),
    "sendError": (()=>sendError),
    "sendForbidden": (()=>sendForbidden),
    "sendNotFound": (()=>sendNotFound),
    "sendResponse": (()=>sendResponse),
    "sendSuccess": (()=>sendSuccess),
    "sendTooManyRequests": (()=>sendTooManyRequests),
    "sendUnauthorized": (()=>sendUnauthorized),
    "sendValidationError": (()=>sendValidationError)
});
function sendResponse(res, data = null, message = '', success = true, statusCode = 200) {
    return res.status(statusCode).json({
        success,
        message,
        data,
        timestamp: new Date().toISOString()
    });
}
function sendSuccess(res, data = null, message = 'Success', statusCode = 200) {
    return sendResponse(res, data, message, true, statusCode);
}
function sendError(res, message = 'An error occurred', statusCode = 500, data = null) {
    return sendResponse(res, data, message, false, statusCode);
}
function sendValidationError(res, errors, message = 'Validation failed') {
    return sendResponse(res, {
        errors
    }, message, false, 400);
}
function sendNotFound(res, message = 'Resource not found') {
    return sendError(res, message, 404);
}
function sendUnauthorized(res, message = 'Unauthorized access') {
    return sendError(res, message, 401);
}
function sendForbidden(res, message = 'Forbidden access') {
    return sendError(res, message, 403);
}
function sendConflict(res, message = 'Resource conflict') {
    return sendError(res, message, 409);
}
function sendTooManyRequests(res, message = 'Too many requests') {
    return sendError(res, message, 429);
}
function handleApiError(res, error) {
    console.error('API Error:', error);
    // Mongoose validation error
    if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map((err)=>({
                field: err.path,
                message: err.message
            }));
        return sendValidationError(res, errors);
    }
    // Mongoose duplicate key error
    if (error.code === 11000) {
        const field = Object.keys(error.keyPattern)[0];
        return sendConflict(res, `${field} already exists`);
    }
    // JWT errors
    if (error.name === 'JsonWebTokenError') {
        return sendUnauthorized(res, 'Invalid token');
    }
    if (error.name === 'TokenExpiredError') {
        return sendUnauthorized(res, 'Token expired');
    }
    // Custom application errors
    if (error.statusCode) {
        return sendError(res, error.message, error.statusCode);
    }
    // Default server error
    return sendError(res, 'Internal server error', 500);
}
class ApiError extends Error {
    constructor(message, statusCode = 500, data = null){
        super(message);
        this.statusCode = statusCode;
        this.data = data;
        this.name = 'ApiError';
    }
}
function createApiError(message, statusCode = 500, data = null) {
    return new ApiError(message, statusCode, data);
}
const errors = {
    notFound: (resource = 'Resource')=>createApiError(`${resource} not found`, 404),
    unauthorized: (message = 'Unauthorized access')=>createApiError(message, 401),
    forbidden: (message = 'Forbidden access')=>createApiError(message, 403),
    badRequest: (message = 'Bad request')=>createApiError(message, 400),
    conflict: (message = 'Resource conflict')=>createApiError(message, 409),
    tooManyRequests: (message = 'Too many requests')=>createApiError(message, 429),
    internal: (message = 'Internal server error')=>createApiError(message, 500)
};
function asyncHandler(handler) {
    return async (req, res)=>{
        try {
            await handler(req, res);
        } catch (error) {
            handleApiError(res, error);
        }
    };
}
function methodNotAllowed(res, allowedMethods = []) {
    res.setHeader('Allow', allowedMethods.join(', '));
    return sendError(res, `Method ${res.req?.method} not allowed`, 405);
}
}}),
"[project]/src/lib/utils.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "API_BASE_URL": (()=>API_BASE_URL),
    "capitalizeFirst": (()=>capitalizeFirst),
    "cn": (()=>cn),
    "debounce": (()=>debounce),
    "formatCurrency": (()=>formatCurrency),
    "formatDate": (()=>formatDate),
    "formatTime": (()=>formatTime),
    "generateOTP": (()=>generateOTP),
    "getAuthHeaders": (()=>getAuthHeaders),
    "getBookingStatusText": (()=>getBookingStatusText),
    "getInitials": (()=>getInitials),
    "getStatusColor": (()=>getStatusColor),
    "truncateText": (()=>truncateText),
    "validateEmail": (()=>validateEmail),
    "validatePhone": (()=>validatePhone)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(date) {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}
function formatTime(date) {
    const d = new Date(date);
    return d.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
}
function generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function validatePhone(phone) {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
}
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}
function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
function getInitials(name) {
    return name.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function getStatusColor(status) {
    switch(status.toLowerCase()){
        case 'pending':
            return 'text-yellow-600 bg-yellow-100';
        case 'confirmed':
            return 'text-blue-600 bg-blue-100';
        case 'completed':
            return 'text-green-600 bg-green-100';
        case 'cancelled':
        case 'cancelled_by_provider':
            return 'text-red-600 bg-red-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
}
function getBookingStatusText(status) {
    switch(status.toLowerCase()){
        case 'pending':
            return 'Pending Approval';
        case 'confirmed':
            return 'Confirmed';
        case 'completed':
            return 'Completed';
        case 'cancelled':
            return 'Cancelled by User';
        case 'cancelled_by_provider':
            return 'Cancelled by Provider';
        default:
            return status;
    }
}
const API_BASE_URL = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : '/api';
function getAuthHeaders() {
    const token = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
    return {
        'Content-Type': 'application/json',
        ...token && {
            Authorization: `Bearer ${token}`
        }
    };
}
}}),
"[project]/src/app/api/auth/login/route.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/middleware/auth.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiResponse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiResponse.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.js [app-route] (ecmascript)");
;
;
;
;
;
;
async function POST(request) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { email } = await request.json();
        // Validate input
        if (!email) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Email is required'
            }, {
                status: 400
            });
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateEmail"])(email)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Please provide a valid email address'
            }, {
                status: 400
            });
        }
        // Find user by email
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            email: email.toLowerCase()
        });
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'No account found with this email address'
            }, {
                status: 404
            });
        }
        if (!user.isActive) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Your account has been deactivated. Please contact support.'
            }, {
                status: 403
            });
        }
        // Generate OTP
        const otp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateOTP"])();
        const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
        // Save OTP to user
        user.otp = otp;
        user.otpExpires = otpExpires;
        await user.save();
        // TODO: Send OTP via email (implement email service)
        console.log(`OTP for ${email}: ${otp}`); // For development
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'OTP sent to your email address',
            data: {
                email: user.email,
                // In development, return OTP for testing
                ...("TURBOPACK compile-time value", "development") === 'development' && {
                    otp
                }
            }
        });
    } catch (error) {
        console.error('Login error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: 'An error occurred during login'
        }, {
            status: 500
        });
    }
}
async function GET() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: false,
        message: 'Method not allowed'
    }, {
        status: 405
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__11d4d585._.js.map