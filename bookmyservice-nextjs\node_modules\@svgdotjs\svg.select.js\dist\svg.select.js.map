{"version": 3, "file": "svg.select.js", "sources": ["../src/utils.js", "../src/SelectHandler.js", "../src/PointSelectHandler.js", "../src/svg.select.js"], "sourcesContent": ["/**\n *\n * @param {string} eventName\n * @param {import('@svgdotjs/svg.js').Element} el\n * @param {number | null} index\n */\nexport function getMoseDownFunc(eventName, el, points, index = null) {\n  return function (ev) {\n    ev.preventDefault()\n    ev.stopPropagation()\n\n    var x = ev.pageX || ev.touches[0].pageX\n    var y = ev.pageY || ev.touches[0].pageY\n    el.fire(eventName, { x: x, y: y, event: ev, index, points })\n  }\n}\n\nexport function transformPoint([x, y], { a, b, c, d, e, f }) {\n  return [x * a + y * c + e, x * b + y * d + f]\n}\n", "import { G, getWindow } from '@svgdotjs/svg.js'\nimport { getMoseDownFunc, transformPoint } from './utils'\n\nexport class SelectHandler {\n  constructor(el) {\n    this.el = el\n    el.remember('_selectHandler', this)\n    this.selection = new G()\n    this.order = ['lt', 't', 'rt', 'r', 'rb', 'b', 'lb', 'l', 'rot']\n    this.mutationHandler = this.mutationHandler.bind(this)\n\n    const win = getWindow()\n    this.observer = new win.MutationObserver(this.mutationHandler)\n  }\n\n  init(options) {\n    this.createHandle = options.createHandle || this.createHandleFn\n    this.createRot = options.createRot || this.createRotFn\n\n    this.updateHandle = options.updateHandle || this.updateHandleFn\n    this.updateRot = options.updateRot || this.updateRotFn\n\n    // mount group\n    this.el.root().put(this.selection)\n\n    this.updatePoints()\n    this.createSelection()\n    this.createResizeHandles()\n    this.updateResizeHandles()\n    this.createRotationHandle()\n    this.updateRotationHandle()\n    this.observer.observe(this.el.node, { attributes: true })\n  }\n\n  active(val, options) {\n    // Disable selection\n    if (!val) {\n      this.selection.clear().remove()\n      this.observer.disconnect()\n      return\n    }\n\n    // Enable selection\n    this.init(options)\n  }\n\n  createSelection() {\n    this.selection.polygon(this.handlePoints).addClass('svg_select_shape')\n  }\n\n  updateSelection() {\n    this.selection.get(0).plot(this.handlePoints)\n  }\n\n  createResizeHandles() {\n    this.handlePoints.forEach((p, index, arr) => {\n      const name = this.order[index]\n      this.createHandle.call(this, this.selection, p, index, arr, name)\n\n      this.selection\n        .get(index + 1)\n        .addClass('svg_select_handle svg_select_handle_' + name)\n        .on('mousedown.selection touchstart.selection', getMoseDownFunc(name, this.el, this.handlePoints, index))\n    })\n  }\n\n  createHandleFn(group) {\n    group.polyline()\n  }\n\n  updateHandleFn(shape, point, index, arr) {\n    const before = arr.at(index - 1)\n    const next = arr[(index + 1) % arr.length]\n    const p = point\n\n    const diff1 = [p[0] - before[0], p[1] - before[1]]\n    const diff2 = [p[0] - next[0], p[1] - next[1]]\n\n    const len1 = Math.sqrt(diff1[0] * diff1[0] + diff1[1] * diff1[1])\n    const len2 = Math.sqrt(diff2[0] * diff2[0] + diff2[1] * diff2[1])\n\n    const normalized1 = [diff1[0] / len1, diff1[1] / len1]\n    const normalized2 = [diff2[0] / len2, diff2[1] / len2]\n\n    const beforeNew = [p[0] - normalized1[0] * 10, p[1] - normalized1[1] * 10]\n    const nextNew = [p[0] - normalized2[0] * 10, p[1] - normalized2[1] * 10]\n\n    shape.plot([beforeNew, p, nextNew])\n  }\n\n  updateResizeHandles() {\n    this.handlePoints.forEach((p, index, arr) => {\n      const name = this.order[index]\n      this.updateHandle.call(this, this.selection.get(index + 1), p, index, arr, name)\n    })\n  }\n\n  createRotFn(group) {\n    group.line()\n    group.circle(5)\n  }\n\n  getPoint(name) {\n    return this.handlePoints[this.order.indexOf(name)]\n  }\n\n  getPointHandle(name) {\n    return this.selection.get(this.order.indexOf(name) + 1)\n  }\n\n  updateRotFn(group, rotPoint) {\n    const topPoint = this.getPoint('t')\n    group.get(0).plot(topPoint[0], topPoint[1], rotPoint[0], rotPoint[1])\n    group.get(1).center(rotPoint[0], rotPoint[1])\n  }\n\n  createRotationHandle() {\n    const handle = this.selection\n      .group()\n      .addClass('svg_select_handle_rot')\n      .on('mousedown.selection touchstart.selection', getMoseDownFunc('rot', this.el, this.handlePoints))\n\n    this.createRot.call(this, handle)\n  }\n\n  updateRotationHandle() {\n    const group = this.selection.findOne('g.svg_select_handle_rot')\n    this.updateRot(group, this.rotationPoint, this.handlePoints)\n  }\n\n  // gets new bounding box points and transform them into the elements space\n  updatePoints() {\n    const bbox = this.el.bbox()\n    const fromShapeToUiMatrix = this.el.root().screenCTM().inverseO().multiplyO(this.el.screenCTM())\n\n    this.handlePoints = this.getHandlePoints(bbox).map((p) => transformPoint(p, fromShapeToUiMatrix))\n    this.rotationPoint = transformPoint(this.getRotationPoint(bbox), fromShapeToUiMatrix)\n  }\n\n  // A collection of all the points we need to draw our ui\n  getHandlePoints({ x, x2, y, y2, cx, cy } = this.el.bbox()) {\n    return [\n      [x, y],\n      [cx, y],\n      [x2, y],\n      [x2, cy],\n      [x2, y2],\n      [cx, y2],\n      [x, y2],\n      [x, cy],\n    ]\n  }\n\n  // A collection of all the points we need to draw our ui\n  getRotationPoint({ y, cx } = this.el.bbox()) {\n    return [cx, y - 20]\n  }\n\n  mutationHandler() {\n    this.updatePoints()\n\n    this.updateSelection()\n    this.updateResizeHandles()\n    this.updateRotationHandle()\n  }\n}\n", "import { G, getWindow } from '@svgdotjs/svg.js'\nimport { getMoseDownFunc, transformPoint } from './utils'\n\nexport class PointSelectHandler {\n  constructor(el) {\n    this.el = el\n    el.remember('_pointSelectHandler', this)\n    this.selection = new G()\n    this.order = ['lt', 't', 'rt', 'r', 'rb', 'b', 'lb', 'l', 'rot']\n    this.mutationHandler = this.mutationHandler.bind(this)\n\n    const win = getWindow()\n    this.observer = new win.MutationObserver(this.mutationHandler)\n  }\n\n  init(options) {\n    this.createHandle = options.createHandle || this.createHandleFn\n    this.updateHandle = options.updateHandle || this.updateHandleFn\n\n    // mount group\n    this.el.root().put(this.selection)\n\n    this.updatePoints()\n    this.createSelection()\n    this.createPointHandles()\n    this.updatePointHandles()\n    this.observer.observe(this.el.node, { attributes: true })\n  }\n\n  active(val, options) {\n    // Disable selection\n    if (!val) {\n      this.selection.clear().remove()\n      this.observer.disconnect()\n      return\n    }\n\n    // Enable selection\n    this.init(options)\n  }\n\n  createSelection() {\n    this.selection.polygon(this.points).addClass('svg_select_shape_pointSelect')\n  }\n\n  updateSelection() {\n    this.selection.get(0).plot(this.points)\n  }\n\n  createPointHandles() {\n    this.points.forEach((p, index, arr) => {\n      this.createHandle.call(this, this.selection, p, index, arr)\n\n      this.selection\n        .get(index + 1)\n        .addClass('svg_select_handle_point')\n        .on('mousedown.selection touchstart.selection', getMoseDownFunc('point', this.el, this.points, index))\n    })\n  }\n\n  createHandleFn(group) {\n    group.circle(5)\n  }\n\n  updateHandleFn(shape, point) {\n    shape.center(point[0], point[1])\n  }\n\n  updatePointHandles() {\n    this.points.forEach((p, index, arr) => {\n      this.updateHandle.call(this, this.selection.get(index + 1), p, index, arr)\n    })\n  }\n\n  // gets new bounding box points and transform them into the elements space\n  updatePoints() {\n    const fromShapeToUiMatrix = this.el.root().screenCTM().inverseO().multiplyO(this.el.screenCTM())\n    this.points = this.el.array().map((p) => transformPoint(p, fromShapeToUiMatrix))\n  }\n\n  mutationHandler() {\n    this.updatePoints()\n\n    this.updateSelection()\n    this.updatePointHandles()\n  }\n}\n", "import { Element, Line, Polygon, <PERSON>yline, extend } from '@svgdotjs/svg.js'\nimport { SelectHandler } from './SelectHandler'\nimport { PointSelectHandler } from './PointSelectHandler'\n\nconst getSelectFn = (handleClass) => {\n  return function (enabled = true, options = {}) {\n    if (typeof enabled === 'object') {\n      options = enabled\n      enabled = true\n    }\n\n    let selectHandler = this.remember('_' + handleClass.name)\n\n    if (!selectHandler) {\n      if (enabled.prototype instanceof SelectHandler) {\n        selectHandler = new enabled(this)\n        enabled = true\n      } else {\n        selectHandler = new handleClass(this)\n      }\n\n      this.remember('_' + handleClass.name, selectHandler)\n    }\n\n    selectHandler.active(enabled, options)\n\n    return this\n  }\n}\n\nextend(Element, {\n  select: getSelectFn(SelectHandler),\n})\n\nextend([Polygon, Polyline, Line], {\n  pointSelect: getSelectFn(PointSelectHandler),\n})\n\nexport { SelectHand<PERSON>, PointSelectHandler }\n"], "names": [], "mappings": ";;;;;;;;;;;;AAMO,SAAS,gBAAgB,WAAW,IAAI,QAAQ,QAAQ,MAAM;AACnE,SAAO,SAAU,IAAI;AACnB,OAAG,eAAgB;AACnB,OAAG,gBAAiB;AAEpB,QAAI,IAAI,GAAG,SAAS,GAAG,QAAQ,CAAC,EAAE;AAClC,QAAI,IAAI,GAAG,SAAS,GAAG,QAAQ,CAAC,EAAE;AAClC,OAAG,KAAK,WAAW,EAAE,GAAM,GAAM,OAAO,IAAI,OAAO,OAAM,CAAE;AAAA,EAC5D;AACH;AAEO,SAAS,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC,GAAI;AAC3D,SAAO,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC;AAC9C;AChBO,MAAM,cAAc;AAAA,EACzB,YAAY,IAAI;AACd,SAAK,KAAK;AACV,OAAG,SAAS,kBAAkB,IAAI;AAClC,SAAK,YAAY,IAAI,EAAG;AACxB,SAAK,QAAQ,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK;AAC/D,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AAErD,UAAM,MAAM,UAAW;AACvB,SAAK,WAAW,IAAI,IAAI,iBAAiB,KAAK,eAAe;AAAA,EAC9D;AAAA,EAED,KAAK,SAAS;AACZ,SAAK,eAAe,QAAQ,gBAAgB,KAAK;AACjD,SAAK,YAAY,QAAQ,aAAa,KAAK;AAE3C,SAAK,eAAe,QAAQ,gBAAgB,KAAK;AACjD,SAAK,YAAY,QAAQ,aAAa,KAAK;AAG3C,SAAK,GAAG,KAAI,EAAG,IAAI,KAAK,SAAS;AAEjC,SAAK,aAAc;AACnB,SAAK,gBAAiB;AACtB,SAAK,oBAAqB;AAC1B,SAAK,oBAAqB;AAC1B,SAAK,qBAAsB;AAC3B,SAAK,qBAAsB;AAC3B,SAAK,SAAS,QAAQ,KAAK,GAAG,MAAM,EAAE,YAAY,MAAM;AAAA,EACzD;AAAA,EAED,OAAO,KAAK,SAAS;AAEnB,QAAI,CAAC,KAAK;AACR,WAAK,UAAU,MAAO,EAAC,OAAQ;AAC/B,WAAK,SAAS,WAAY;AAC1B;AAAA,IACD;AAGD,SAAK,KAAK,OAAO;AAAA,EAClB;AAAA,EAED,kBAAkB;AAChB,SAAK,UAAU,QAAQ,KAAK,YAAY,EAAE,SAAS,kBAAkB;AAAA,EACtE;AAAA,EAED,kBAAkB;AAChB,SAAK,UAAU,IAAI,CAAC,EAAE,KAAK,KAAK,YAAY;AAAA,EAC7C;AAAA,EAED,sBAAsB;AACpB,SAAK,aAAa,QAAQ,CAAC,GAAG,OAAO,QAAQ;AAC3C,YAAM,OAAO,KAAK,MAAM,KAAK;AAC7B,WAAK,aAAa,KAAK,MAAM,KAAK,WAAW,GAAG,OAAO,KAAK,IAAI;AAEhE,WAAK,UACF,IAAI,QAAQ,CAAC,EACb,SAAS,yCAAyC,IAAI,EACtD,GAAG,4CAA4C,gBAAgB,MAAM,KAAK,IAAI,KAAK,cAAc,KAAK,CAAC;AAAA,IAChH,CAAK;AAAA,EACF;AAAA,EAED,eAAe,OAAO;AACpB,UAAM,SAAU;AAAA,EACjB;AAAA,EAED,eAAe,OAAO,OAAO,OAAO,KAAK;AACvC,UAAM,SAAS,IAAI,GAAG,QAAQ,CAAC;AAC/B,UAAM,OAAO,KAAK,QAAQ,KAAK,IAAI,MAAM;AACzC,UAAM,IAAI;AAEV,UAAM,QAAQ,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,OAAO,CAAC,CAAC;AACjD,UAAM,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC;AAE7C,UAAM,OAAO,KAAK,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAChE,UAAM,OAAO,KAAK,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAEhE,UAAM,cAAc,CAAC,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC,IAAI,IAAI;AACrD,UAAM,cAAc,CAAC,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC,IAAI,IAAI;AAErD,UAAM,YAAY,CAAC,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE;AACzE,UAAM,UAAU,CAAC,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE;AAEvE,UAAM,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC;AAAA,EACnC;AAAA,EAED,sBAAsB;AACpB,SAAK,aAAa,QAAQ,CAAC,GAAG,OAAO,QAAQ;AAC3C,YAAM,OAAO,KAAK,MAAM,KAAK;AAC7B,WAAK,aAAa,KAAK,MAAM,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,IAAI;AAAA,IACrF,CAAK;AAAA,EACF;AAAA,EAED,YAAY,OAAO;AACjB,UAAM,KAAM;AACZ,UAAM,OAAO,CAAC;AAAA,EACf;AAAA,EAED,SAAS,MAAM;AACb,WAAO,KAAK,aAAa,KAAK,MAAM,QAAQ,IAAI,CAAC;AAAA,EAClD;AAAA,EAED,eAAe,MAAM;AACnB,WAAO,KAAK,UAAU,IAAI,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC;AAAA,EACvD;AAAA,EAED,YAAY,OAAO,UAAU;AAC3B,UAAM,WAAW,KAAK,SAAS,GAAG;AAClC,UAAM,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AACpE,UAAM,IAAI,CAAC,EAAE,OAAO,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,EAC7C;AAAA,EAED,uBAAuB;AACrB,UAAM,SAAS,KAAK,UACjB,MAAO,EACP,SAAS,uBAAuB,EAChC,GAAG,4CAA4C,gBAAgB,OAAO,KAAK,IAAI,KAAK,YAAY,CAAC;AAEpG,SAAK,UAAU,KAAK,MAAM,MAAM;AAAA,EACjC;AAAA,EAED,uBAAuB;AACrB,UAAM,QAAQ,KAAK,UAAU,QAAQ,yBAAyB;AAC9D,SAAK,UAAU,OAAO,KAAK,eAAe,KAAK,YAAY;AAAA,EAC5D;AAAA;AAAA,EAGD,eAAe;AACb,UAAM,OAAO,KAAK,GAAG,KAAM;AAC3B,UAAM,sBAAsB,KAAK,GAAG,KAAM,EAAC,UAAS,EAAG,SAAQ,EAAG,UAAU,KAAK,GAAG,UAAS,CAAE;AAE/F,SAAK,eAAe,KAAK,gBAAgB,IAAI,EAAE,IAAI,CAAC,MAAM,eAAe,GAAG,mBAAmB,CAAC;AAChG,SAAK,gBAAgB,eAAe,KAAK,iBAAiB,IAAI,GAAG,mBAAmB;AAAA,EACrF;AAAA;AAAA,EAGD,gBAAgB,EAAE,GAAG,IAAI,GAAG,IAAI,IAAI,OAAO,KAAK,GAAG,KAAI,GAAI;AACzD,WAAO;AAAA,MACL,CAAC,GAAG,CAAC;AAAA,MACL,CAAC,IAAI,CAAC;AAAA,MACN,CAAC,IAAI,CAAC;AAAA,MACN,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,GAAG,EAAE;AAAA,MACN,CAAC,GAAG,EAAE;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,iBAAiB,EAAE,GAAG,GAAE,IAAK,KAAK,GAAG,QAAQ;AAC3C,WAAO,CAAC,IAAI,IAAI,EAAE;AAAA,EACnB;AAAA,EAED,kBAAkB;AAChB,SAAK,aAAc;AAEnB,SAAK,gBAAiB;AACtB,SAAK,oBAAqB;AAC1B,SAAK,qBAAsB;AAAA,EAC5B;AACH;AClKO,MAAM,mBAAmB;AAAA,EAC9B,YAAY,IAAI;AACd,SAAK,KAAK;AACV,OAAG,SAAS,uBAAuB,IAAI;AACvC,SAAK,YAAY,IAAI,EAAG;AACxB,SAAK,QAAQ,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK;AAC/D,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AAErD,UAAM,MAAM,UAAW;AACvB,SAAK,WAAW,IAAI,IAAI,iBAAiB,KAAK,eAAe;AAAA,EAC9D;AAAA,EAED,KAAK,SAAS;AACZ,SAAK,eAAe,QAAQ,gBAAgB,KAAK;AACjD,SAAK,eAAe,QAAQ,gBAAgB,KAAK;AAGjD,SAAK,GAAG,KAAI,EAAG,IAAI,KAAK,SAAS;AAEjC,SAAK,aAAc;AACnB,SAAK,gBAAiB;AACtB,SAAK,mBAAoB;AACzB,SAAK,mBAAoB;AACzB,SAAK,SAAS,QAAQ,KAAK,GAAG,MAAM,EAAE,YAAY,MAAM;AAAA,EACzD;AAAA,EAED,OAAO,KAAK,SAAS;AAEnB,QAAI,CAAC,KAAK;AACR,WAAK,UAAU,MAAO,EAAC,OAAQ;AAC/B,WAAK,SAAS,WAAY;AAC1B;AAAA,IACD;AAGD,SAAK,KAAK,OAAO;AAAA,EAClB;AAAA,EAED,kBAAkB;AAChB,SAAK,UAAU,QAAQ,KAAK,MAAM,EAAE,SAAS,8BAA8B;AAAA,EAC5E;AAAA,EAED,kBAAkB;AAChB,SAAK,UAAU,IAAI,CAAC,EAAE,KAAK,KAAK,MAAM;AAAA,EACvC;AAAA,EAED,qBAAqB;AACnB,SAAK,OAAO,QAAQ,CAAC,GAAG,OAAO,QAAQ;AACrC,WAAK,aAAa,KAAK,MAAM,KAAK,WAAW,GAAG,OAAO,GAAG;AAE1D,WAAK,UACF,IAAI,QAAQ,CAAC,EACb,SAAS,yBAAyB,EAClC,GAAG,4CAA4C,gBAAgB,SAAS,KAAK,IAAI,KAAK,QAAQ,KAAK,CAAC;AAAA,IAC7G,CAAK;AAAA,EACF;AAAA,EAED,eAAe,OAAO;AACpB,UAAM,OAAO,CAAC;AAAA,EACf;AAAA,EAED,eAAe,OAAO,OAAO;AAC3B,UAAM,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EAChC;AAAA,EAED,qBAAqB;AACnB,SAAK,OAAO,QAAQ,CAAC,GAAG,OAAO,QAAQ;AACrC,WAAK,aAAa,KAAK,MAAM,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,GAAG;AAAA,IAC/E,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,eAAe;AACb,UAAM,sBAAsB,KAAK,GAAG,KAAM,EAAC,UAAS,EAAG,SAAQ,EAAG,UAAU,KAAK,GAAG,UAAS,CAAE;AAC/F,SAAK,SAAS,KAAK,GAAG,MAAO,EAAC,IAAI,CAAC,MAAM,eAAe,GAAG,mBAAmB,CAAC;AAAA,EAChF;AAAA,EAED,kBAAkB;AAChB,SAAK,aAAc;AAEnB,SAAK,gBAAiB;AACtB,SAAK,mBAAoB;AAAA,EAC1B;AACH;AClFA,MAAM,cAAc,CAAC,gBAAgB;AACnC,SAAO,SAAU,UAAU,MAAM,UAAU,CAAA,GAAI;AAC7C,QAAI,OAAO,YAAY,UAAU;AAC/B,gBAAU;AACV,gBAAU;AAAA,IACX;AAED,QAAI,gBAAgB,KAAK,SAAS,MAAM,YAAY,IAAI;AAExD,QAAI,CAAC,eAAe;AAClB,UAAI,QAAQ,qBAAqB,eAAe;AAC9C,wBAAgB,IAAI,QAAQ,IAAI;AAChC,kBAAU;AAAA,MAClB,OAAa;AACL,wBAAgB,IAAI,YAAY,IAAI;AAAA,MACrC;AAED,WAAK,SAAS,MAAM,YAAY,MAAM,aAAa;AAAA,IACpD;AAED,kBAAc,OAAO,SAAS,OAAO;AAErC,WAAO;AAAA,EACR;AACH;AAEA,OAAO,SAAS;AAAA,EACd,QAAQ,YAAY,aAAa;AACnC,CAAC;AAED,OAAO,CAAC,SAAS,UAAU,IAAI,GAAG;AAAA,EAChC,aAAa,YAAY,kBAAkB;AAC7C,CAAC;"}