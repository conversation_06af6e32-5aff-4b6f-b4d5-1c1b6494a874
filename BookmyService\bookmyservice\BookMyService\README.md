## Prerequisite

1. NodeJS - (v20.X.X)

## Working Environment

| No. | Name  | PORT     | Purpose           | Domain                                     |
| --- | ----- | -------- | ----------------- | ------------------------------------------ |
| 1.  | LOCAL | 5000     | Local development | <http://localhost:5000>                    |

## Execute Project

1. Copy environment files

    `cp .env.example .env`

2. `nvm use`

3. Install Dependencies

    `npm install`

4. Start **Development** Server `npm run dev`

## 🛎 Commands

-   Run the Server in development mode : `npm run dev`

Optional Setup :

1. Install [VSCode](https://code.visualstudio.com/)