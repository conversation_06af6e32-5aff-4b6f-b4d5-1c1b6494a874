import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Booking from '@/models/Booking';
import Service from '@/models/Service';
import User from '@/models/User';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';
import { sendEmail } from '@/lib/email';

export async function POST(request) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded) {
      return apiResponse.error('Invalid token', 401);
    }
    
    const userId = decoded.userId;
    const bookingData = await request.json();
    
    // Validate required fields
    const { serviceId, preferredDate, preferredTime, message } = bookingData;
    
    if (!serviceId) {
      return apiResponse.error('Service ID is required', 400);
    }
    
    // Check if service exists and is active
    const service = await Service.findById(serviceId).populate('businessOwner');
    if (!service || !service.isActive) {
      return apiResponse.error('Service not found or not available', 404);
    }
    
    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return apiResponse.error('User not found', 404);
    }
    
    // Create booking
    const booking = new Booking({
      user: userId,
      service: serviceId,
      businessOwner: service.businessOwner._id,
      preferredDate: preferredDate || new Date(),
      preferredTime,
      message: message || '',
      status: 'pending',
      totalAmount: service.price
    });
    
    await booking.save();
    
    // Populate the booking with related data
    await booking.populate([
      { path: 'user', select: 'firstName lastName email' },
      { path: 'service', select: 'title description price' },
      { path: 'businessOwner', select: 'businessName email ownerFirstName ownerLastName' }
    ]);
    
    // Send email notification to business owner
    try {
      await sendEmail({
        to: booking.businessOwner.email,
        subject: `New Booking Request for ${service.title}`,
        html: `
          <h2>New Booking Request</h2>
          <p>You have received a new booking request for your service.</p>
          
          <h3>Service Details:</h3>
          <p><strong>Service:</strong> ${service.title}</p>
          <p><strong>Price:</strong> $${service.price}</p>
          
          <h3>Customer Details:</h3>
          <p><strong>Name:</strong> ${user.firstName} ${user.lastName}</p>
          <p><strong>Email:</strong> ${user.email}</p>
          
          <h3>Booking Details:</h3>
          <p><strong>Preferred Date:</strong> ${new Date(booking.preferredDate).toLocaleDateString()}</p>
          ${booking.preferredTime ? `<p><strong>Preferred Time:</strong> ${booking.preferredTime}</p>` : ''}
          ${booking.message ? `<p><strong>Message:</strong> ${booking.message}</p>` : ''}
          
          <p>Please log in to your dashboard to accept or decline this booking request.</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send booking notification email:', emailError);
    }
    
    // Send confirmation email to user
    try {
      await sendEmail({
        to: user.email,
        subject: `Booking Request Submitted - ${service.title}`,
        html: `
          <h2>Booking Request Submitted</h2>
          <p>Dear ${user.firstName},</p>
          <p>Your booking request has been submitted successfully!</p>
          
          <h3>Service Details:</h3>
          <p><strong>Service:</strong> ${service.title}</p>
          <p><strong>Provider:</strong> ${booking.businessOwner.businessName}</p>
          <p><strong>Price:</strong> $${service.price}</p>
          
          <h3>Your Request:</h3>
          <p><strong>Preferred Date:</strong> ${new Date(booking.preferredDate).toLocaleDateString()}</p>
          ${booking.preferredTime ? `<p><strong>Preferred Time:</strong> ${booking.preferredTime}</p>` : ''}
          
          <p>The service provider will review your request and get back to you soon.</p>
          <p>You can track the status of your booking in your dashboard.</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send booking confirmation email:', emailError);
    }
    
    return apiResponse.success(booking, 'Booking request submitted successfully', 201);
    
  } catch (error) {
    console.error('Error creating booking:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return apiResponse.error(`Validation error: ${errors.join(', ')}`, 400);
    }
    
    return apiResponse.error('Failed to create booking', 500);
  }
}

export async function GET(request) {
  try {
    await connectDB();
    
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded) {
      return apiResponse.error('Invalid token', 401);
    }
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const status = searchParams.get('status');
    const skip = (page - 1) * limit;
    
    // Build query based on user role
    let query = {};
    
    // If user is business owner, show bookings for their services
    if (decoded.role === 'business_owner') {
      query.businessOwner = decoded.userId;
    } else if (decoded.role === 'admin' || decoded.role === 'super_admin') {
      // Admin can see all bookings
    } else {
      // Regular users see only their bookings
      query.user = decoded.userId;
    }
    
    if (status) {
      query.status = status;
    }
    
    const bookings = await Booking.find(query)
      .populate('user', 'firstName lastName email')
      .populate('service', 'title description price images')
      .populate('businessOwner', 'businessName email ownerFirstName ownerLastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    const total = await Booking.countDocuments(query);
    
    return apiResponse.success({
      bookings,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }, 'Bookings fetched successfully');
    
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return apiResponse.error('Failed to fetch bookings', 500);
  }
}
