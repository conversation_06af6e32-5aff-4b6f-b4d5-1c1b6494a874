import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // For now, redirect to a placeholder or show a message
    // In a real implementation, you would integrate with Facebook OAuth
    const facebookAuthUrl = `https://www.facebook.com/v18.0/dialog/oauth?` +
      `client_id=${process.env.FACEBOOK_APP_ID}&` +
      `redirect_uri=${process.env.NEXT_PUBLIC_BACKEND_URL}/api/auth/facebook/callback&` +
      `scope=email,public_profile&` +
      `response_type=code&` +
      `auth_type=rerequest&` +
      `display=popup`;

    return NextResponse.redirect(facebookAuthUrl);
  } catch (error) {
    console.error('Facebook OAuth error:', error);
    return NextResponse.json({
      success: false,
      message: 'Facebook OAuth not configured'
    }, { status: 500 });
  }
}

export async function POST() {
  return NextResponse.json({
    success: false,
    message: 'Method not allowed'
  }, { status: 405 });
}
