"use client";

import { useState } from 'react';
import { toast } from 'react-toastify';

export default function SetupTestUsersPage() {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);

  const createTestUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setUsers(data.data.users);
        toast.success('Test users created successfully!');
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error('Error creating test users:', error);
      toast.error('Failed to create test users');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-heading-3 font-bold text-dark dark:text-white">
          Setup Test Users
        </h1>
        <p className="text-body-sm text-dark-5 dark:text-dark-6">
          Create test users to test the role-based sidebar functionality.
        </p>
      </div>

      <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">
              Test Accounts
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-dark dark:text-white">Regular User</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">Email: <EMAIL></p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Role: User</p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                  Access to: Home, Services, My Bookings, Profile
                </p>
              </div>
              
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-dark dark:text-white">Business Owner</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">Email: <EMAIL></p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Role: Business Owner</p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                  Access to: Dashboard, Services, Bookings, Revenue, Profile
                </p>
              </div>
              
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-dark dark:text-white">Admin</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">Email: <EMAIL></p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Role: Admin</p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                  Access to: Admin Dashboard, User Management, Reports, etc.
                </p>
              </div>
            </div>
          </div>

          <button
            onClick={createTestUsers}
            disabled={loading}
            className="w-full md:w-auto inline-flex items-center justify-center rounded-lg bg-primary px-6 py-3 text-sm font-medium text-white hover:bg-blue-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating Users...
              </>
            ) : (
              'Create Test Users'
            )}
          </button>

          {users.length > 0 && (
            <div className="mt-6">
              <h4 className="font-medium text-dark dark:text-white mb-3">
                Created Users:
              </h4>
              <div className="space-y-2">
                {users.map((user, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <span className="font-medium text-dark dark:text-white">{user.name}</span>
                      <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">({user.email})</span>
                    </div>
                    <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                      {user.role}
                    </span>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  How to Test:
                </h5>
                <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>1. Go to <a href="/auth/login" className="underline">/auth/login</a></li>
                  <li>2. Enter one of the test emails above</li>
                  <li>3. Use the OTP that appears in the browser console (development mode)</li>
                  <li>4. You'll be redirected to the appropriate dashboard based on the user role</li>
                  <li>5. Check the sidebar to see role-specific navigation options</li>
                </ol>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
