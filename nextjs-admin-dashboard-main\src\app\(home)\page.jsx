"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

// Dashboard Stats Component
function DashboardStats() {
  const [stats, setStats] = useState({
    totalBookings: 0,
    totalRevenue: 0,
    activeServices: 0,
    totalUsers: 0
  });

  useEffect(() => {
    // TODO: Fetch real stats from API
    setStats({
      totalBookings: 156,
      totalRevenue: 12450,
      activeServices: 23,
      totalUsers: 89
    });
  }, []);

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5">
      <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
        <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-primary/[0.08]">
          <svg className="fill-primary dark:fill-white" width="22" height="16" viewBox="0 0 22 16" fill="none">
            <path d="M11 15.1156C4.19376 15.1156 0.825012 8.61876 0.687512 8.34376C0.584387 8.13751 0.584387 7.86251 0.687512 7.65626C0.825012 7.38126 4.19376 0.918762 11 0.918762C17.8063 0.918762 21.175 7.38126 21.3125 7.65626C21.4156 7.86251 21.4156 8.13751 21.3125 8.34376C21.175 8.61876 17.8063 15.1156 11 15.1156ZM2.26876 8.00001C3.02501 9.27189 5.98126 13.5688 11 13.5688C16.0188 13.5688 18.975 9.27189 19.7313 8.00001C18.975 6.72814 16.0188 2.43126 11 2.43126C5.98126 2.43126 3.02501 6.72814 2.26876 8.00001Z"/>
            <path d="M11 10.9219C9.38438 10.9219 8.07812 9.61562 8.07812 8C8.07812 6.38438 9.38438 5.07812 11 5.07812C12.6156 5.07812 13.9219 6.38438 13.9219 8C13.9219 9.61562 12.6156 10.9219 11 10.9219ZM11 6.625C10.2437 6.625 9.625 7.24375 9.625 8C9.625 8.75625 10.2437 9.375 11 9.375C11.7563 9.375 12.375 8.75625 12.375 8C12.375 7.24375 11.7563 6.625 11 6.625Z"/>
          </svg>
        </div>
        <div className="mt-4 flex items-end justify-between">
          <div>
            <h4 className="mb-1.5 text-heading-6 font-bold text-dark dark:text-white">
              {stats.totalBookings}
            </h4>
            <span className="text-body-sm font-medium">Total Bookings</span>
          </div>
          <span className="flex items-center gap-1.5 text-body-sm font-medium text-green">
            <svg className="fill-current" width="10" height="11" viewBox="0 0 10 11" fill="none">
              <path d="M4.35716 2.47737L0.908974 5.82987L5.0443e-07 4.94612L5 0.0848689L10 4.94612L9.09103 5.82987L5.64284 2.47737L5.64284 10.0849L4.35716 10.0849L4.35716 2.47737Z"/>
            </svg>
            2.5%
          </span>
        </div>
      </div>

      <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
        <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-green/[0.08]">
          <svg className="fill-green dark:fill-white" width="20" height="22" viewBox="0 0 20 22" fill="none">
            <path d="M11.7531 16.4312C10.3781 16.4312 9.27808 17.5312 9.27808 18.9062C9.27808 20.2812 10.3781 21.3812 11.7531 21.3812C13.1281 21.3812 14.2281 20.2812 14.2281 18.9062C14.2281 17.5656 13.1281 16.4312 11.7531 16.4312ZM11.7531 19.8687C11.2375 19.8687 10.7906 19.4218 10.7906 18.9062C10.7906 18.3906 11.2375 17.9437 11.7531 17.9437C12.2687 17.9437 12.7156 18.3906 12.7156 18.9062C12.7156 19.4218 12.2343 19.8687 11.7531 19.8687Z"/>
            <path d="M5.22183 16.4312C3.84683 16.4312 2.74683 17.5312 2.74683 18.9062C2.74683 20.2812 3.84683 21.3812 5.22183 21.3812C6.59683 21.3812 7.69683 20.2812 7.69683 18.9062C7.69683 17.5656 6.59683 16.4312 5.22183 16.4312ZM5.22183 19.8687C4.7062 19.8687 4.25933 19.4218 4.25933 18.9062C4.25933 18.3906 4.7062 17.9437 5.22183 17.9437C5.73745 17.9437 6.18433 18.3906 6.18433 18.9062C6.18433 19.4218 5.7062 19.8687 5.22183 19.8687Z"/>
            <path d="M19.0062 0.618744H17.15C16.325 0.618744 15.6031 1.23749 15.5 2.06249L14.95 6.01562H1.37185C1.0281 6.01562 0.684353 6.18749 0.443728 6.46249C0.237478 6.73749 0.134353 7.11562 0.237478 7.45937L2.36873 13.9562C2.50623 14.4375 2.9531 14.7812 3.46873 14.7812H12.9562C13.4375 14.7812 13.8844 14.4375 14.0219 13.9562L16.1531 7.45937C16.2562 7.11562 16.1531 6.73749 15.9469 6.46249C15.7062 6.18749 15.3625 6.01562 15.0187 6.01562H16.2906L16.8062 2.23437C16.8406 2.02812 17.0125 1.89062 17.2187 1.89062H19.0062C19.3844 1.89062 19.7281 1.54687 19.7281 1.16874C19.7281 0.790619 19.35 0.618744 19.0062 0.618744ZM14.0219 13.2406H3.9531L2.40623 7.52812H14.6031L14.0219 13.2406Z"/>
          </svg>
        </div>
        <div className="mt-4 flex items-end justify-between">
          <div>
            <h4 className="mb-1.5 text-heading-6 font-bold text-dark dark:text-white">
              ${stats.totalRevenue.toLocaleString()}
            </h4>
            <span className="text-body-sm font-medium">Total Revenue</span>
          </div>
          <span className="flex items-center gap-1.5 text-body-sm font-medium text-green">
            <svg className="fill-current" width="10" height="11" viewBox="0 0 10 11" fill="none">
              <path d="M4.35716 2.47737L0.908974 5.82987L5.0443e-07 4.94612L5 0.0848689L10 4.94612L9.09103 5.82987L5.64284 2.47737L5.64284 10.0849L4.35716 10.0849L4.35716 2.47737Z"/>
            </svg>
            4.3%
          </span>
        </div>
      </div>

      <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
        <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-blue/[0.08]">
          <svg className="fill-blue dark:fill-white" width="22" height="22" viewBox="0 0 22 22" fill="none">
            <path d="M21.1063 18.0469L19.3875 3.23126C19.2157 1.71876 17.9438 0.584381 16.3969 0.584381H5.56878C4.05628 0.584381 2.78441 1.71876 2.57816 3.23126L0.859406 18.0469C0.756281 18.9063 1.03128 19.7313 1.61566 20.3844C2.20003 21.0375 2.99066 21.3813 3.85003 21.3813H18.1157C18.975 21.3813 19.8 21.0031 20.35 20.3844C20.9344 19.7313 21.2094 18.9063 21.1063 18.0469ZM19.2157 19.3531C18.9407 19.6625 18.5625 19.8344 18.15 19.8344H3.85003C3.43753 19.8344 3.05941 19.6625 2.78441 19.3531C2.50941 19.0438 2.37191 18.6313 2.44066 18.2188L4.12503 3.43751C4.19378 2.71563 4.81253 2.16563 5.56878 2.16563H16.4313C17.1875 2.16563 17.8063 2.71563 17.875 3.43751L19.5594 18.2188C19.6281 18.6313 19.4906 19.0438 19.2157 19.3531Z"/>
            <path d="M14.3344 5.29375C13.9219 5.29375 13.5781 5.6375 13.5781 6.05V8.4C13.5781 9.2875 12.8219 10.0438 11.9344 10.0438C11.0469 10.0438 10.2906 9.2875 10.2906 8.4V6.05C10.2906 5.6375 9.94687 5.29375 9.53437 5.29375C9.12187 5.29375 8.77812 5.6375 8.77812 6.05V8.4C8.77812 10.125 10.2094 11.5563 11.9344 11.5563C13.6594 11.5563 15.0906 10.125 15.0906 8.4V6.05C15.0906 5.6375 14.7469 5.29375 14.3344 5.29375Z"/>
          </svg>
        </div>
        <div className="mt-4 flex items-end justify-between">
          <div>
            <h4 className="mb-1.5 text-heading-6 font-bold text-dark dark:text-white">
              {stats.activeServices}
            </h4>
            <span className="text-body-sm font-medium">Active Services</span>
          </div>
          <span className="flex items-center gap-1.5 text-body-sm font-medium text-green">
            <svg className="fill-current" width="10" height="11" viewBox="0 0 10 11" fill="none">
              <path d="M4.35716 2.47737L0.908974 5.82987L5.0443e-07 4.94612L5 0.0848689L10 4.94612L9.09103 5.82987L5.64284 2.47737L5.64284 10.0849L4.35716 10.0849L4.35716 2.47737Z"/>
            </svg>
            1.8%
          </span>
        </div>
      </div>

      <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
        <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-orange/[0.08]">
          <svg className="fill-orange dark:fill-white" width="20" height="22" viewBox="0 0 20 22" fill="none">
            <path d="M18.7906 14.6531C18.7906 15.075 18.4469 15.4187 18.025 15.4187H16.8781V16.5656C16.8781 16.9875 16.5344 17.3312 16.1125 17.3312C15.6906 17.3312 15.3469 16.9875 15.3469 16.5656V15.4187H14.2C13.7781 15.4187 13.4344 15.075 13.4344 14.6531C13.4344 14.2312 13.7781 13.8875 14.2 13.8875H15.3469V12.7406C15.3469 12.3187 15.6906 11.975 16.1125 11.975C16.5344 11.975 16.8781 12.3187 16.8781 12.7406V13.8875H18.025C18.4469 13.8875 18.7906 14.2312 18.7906 14.6531Z"/>
            <path d="M10.0969 2.2625C9.675 2.2625 9.33125 2.60625 9.33125 3.02812V4.17499C9.33125 4.59687 9.675 4.94062 10.0969 4.94062C10.5188 4.94062 10.8625 4.59687 10.8625 4.17499V3.02812C10.8625 2.60625 10.5188 2.2625 10.0969 2.2625Z"/>
            <path d="M10.0969 17.3312C9.675 17.3312 9.33125 17.675 9.33125 18.0969V19.2437C9.33125 19.6656 9.675 20.0094 10.0969 20.0094C10.5188 20.0094 10.8625 19.6656 10.8625 19.2437V18.0969C10.8625 17.675 10.5188 17.3312 10.0969 17.3312Z"/>
            <path d="M15.2781 6.21562C15.5531 5.94062 15.5531 5.49375 15.2781 5.21875L14.4875 4.42812C14.2125 4.15312 13.7656 4.15312 13.4906 4.42812C13.2156 4.70312 13.2156 5.15 13.4906 5.425L14.2812 6.21562C14.4187 6.35312 14.5906 6.42187 14.7625 6.42187C14.9344 6.42187 15.1406 6.35312 15.2781 6.21562Z"/>
            <path d="M6.70312 17.8469C6.42812 17.5719 5.98125 17.5719 5.70625 17.8469L4.91562 18.6375C4.64062 18.9125 4.64062 19.3594 4.91562 19.6344C5.05312 19.7719 5.225 19.8406 5.39687 19.8406C5.56875 19.8406 5.74062 19.7719 5.87812 19.6344L6.66875 18.8437C6.94375 18.5687 6.94375 18.1219 6.70312 17.8469Z"/>
            <path d="M18.0969 10.2625H16.95C16.5281 10.2625 16.1844 10.6062 16.1844 11.0281C16.1844 11.45 16.5281 11.7937 16.95 11.7937H18.0969C18.5188 11.7937 18.8625 11.45 18.8625 11.0281C18.8625 10.6062 18.5188 10.2625 18.0969 10.2625Z"/>
            <path d="M3.24375 10.2625H2.09687C1.675 10.2625 1.33125 10.6062 1.33125 11.0281C1.33125 11.45 1.675 11.7937 2.09687 11.7937H3.24375C3.66562 11.7937 4.00937 11.45 4.00937 11.0281C4.00937 10.6062 3.66562 10.2625 3.24375 10.2625Z"/>
            <path d="M15.2781 16.8437C15.5531 16.5687 15.5531 16.1219 15.2781 15.8469C15.0031 15.5719 14.5562 15.5719 14.2812 15.8469L13.4906 16.6375C13.2156 16.9125 13.2156 17.3594 13.4906 17.6344C13.6281 17.7719 13.8 17.8406 13.9719 17.8406C14.1437 17.8406 14.3156 17.7719 14.4531 17.6344L15.2781 16.8437Z"/>
            <path d="M6.70312 4.42812C6.42812 4.15312 5.98125 4.15312 5.70625 4.42812L4.91562 5.21875C4.64062 5.49375 4.64062 5.94062 4.91562 6.21562C5.05312 6.35312 5.225 6.42187 5.39687 6.42187C5.56875 6.42187 5.74062 6.35312 5.87812 6.21562L6.66875 5.425C6.94375 5.15 6.94375 4.70312 6.70312 4.42812Z"/>
          </svg>
        </div>
        <div className="mt-4 flex items-end justify-between">
          <div>
            <h4 className="mb-1.5 text-heading-6 font-bold text-dark dark:text-white">
              {stats.totalUsers}
            </h4>
            <span className="text-body-sm font-medium">Total Users</span>
          </div>
          <span className="flex items-center gap-1.5 text-body-sm font-medium text-green">
            <svg className="fill-current" width="10" height="11" viewBox="0 0 10 11" fill="none">
              <path d="M4.35716 2.47737L0.908974 5.82987L5.0443e-07 4.94612L5 0.0848689L10 4.94612L9.09103 5.82987L5.64284 2.47737L5.64284 10.0849L4.35716 10.0849L4.35716 2.47737Z"/>
            </svg>
            3.1%
          </span>
        </div>
      </div>
    </div>
  );
}

// Recent Bookings Component
function RecentBookings() {
  const [bookings, setBookings] = useState([]);

  useEffect(() => {
    // TODO: Fetch real bookings from API
    setBookings([
      {
        id: 1,
        service: "House Cleaning",
        customer: "John Doe",
        date: "2024-01-15",
        status: "confirmed",
        amount: 120
      },
      {
        id: 2,
        service: "Plumbing Repair",
        customer: "Jane Smith",
        date: "2024-01-14",
        status: "completed",
        amount: 85
      },
      {
        id: 3,
        service: "Garden Maintenance",
        customer: "Mike Johnson",
        date: "2024-01-13",
        status: "pending",
        amount: 200
      }
    ]);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
      <h4 className="mb-5.5 text-body-2xlg font-bold text-dark dark:text-white">
        Recent Bookings
      </h4>
      <div className="flex flex-col">
        <div className="grid grid-cols-3 rounded-sm bg-gray-2 dark:bg-gray-dark sm:grid-cols-5">
          <div className="p-2.5 xl:p-5">
            <h5 className="text-sm font-medium uppercase xsm:text-base">Service</h5>
          </div>
          <div className="p-2.5 text-center xl:p-5">
            <h5 className="text-sm font-medium uppercase xsm:text-base">Customer</h5>
          </div>
          <div className="p-2.5 text-center xl:p-5">
            <h5 className="text-sm font-medium uppercase xsm:text-base">Date</h5>
          </div>
          <div className="hidden p-2.5 text-center sm:block xl:p-5">
            <h5 className="text-sm font-medium uppercase xsm:text-base">Status</h5>
          </div>
          <div className="hidden p-2.5 text-center sm:block xl:p-5">
            <h5 className="text-sm font-medium uppercase xsm:text-base">Amount</h5>
          </div>
        </div>

        {bookings.map((booking) => (
          <div key={booking.id} className="grid grid-cols-3 border-b border-stroke dark:border-dark-3 sm:grid-cols-5">
            <div className="flex items-center gap-3 p-2.5 xl:p-5">
              <p className="text-dark dark:text-white">{booking.service}</p>
            </div>

            <div className="flex items-center justify-center p-2.5 xl:p-5">
              <p className="text-dark dark:text-white">{booking.customer}</p>
            </div>

            <div className="flex items-center justify-center p-2.5 xl:p-5">
              <p className="text-dark dark:text-white">{booking.date}</p>
            </div>

            <div className="hidden items-center justify-center p-2.5 sm:flex xl:p-5">
              <span className={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${getStatusColor(booking.status)}`}>
                {booking.status}
              </span>
            </div>

            <div className="hidden items-center justify-center p-2.5 sm:flex xl:p-5">
              <p className="text-dark dark:text-white">${booking.amount}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default function Home() {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <>
      <div className="mb-6">
        <h1 className="text-heading-3 font-bold text-dark dark:text-white">
          Welcome back, {user?.firstName || 'User'}!
        </h1>
        <p className="text-body-sm text-dark-5 dark:text-dark-6">
          Here's what's happening with your BookMyService platform today.
        </p>
      </div>

      <DashboardStats />

      <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-9 2xl:gap-7.5">
        <div className="col-span-12">
          <RecentBookings />
        </div>
      </div>
    </>
  );
}
