globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/auth/login/route"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js":{"*":{"id":"(ssr)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.jsx":{"*":{"id":"(ssr)/./src/app/providers.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Layouts/header/index.tsx":{"*":{"id":"(ssr)/./src/components/Layouts/header/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Layouts/sidebar/SidebarWrapper.jsx":{"*":{"id":"(ssr)/./src/components/Layouts/sidebar/SidebarWrapper.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(home)/page.jsx":{"*":{"id":"(ssr)/./src/app/(home)/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/services/page.jsx":{"*":{"id":"(ssr)/./src/app/services/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.jsx":{"*":{"id":"(ssr)/./src/app/auth/login/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/my-bookings/page.jsx":{"*":{"id":"(ssr)/./src/app/my-bookings/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/profile/page.jsx":{"*":{"id":"(ssr)/./src/app/profile/page.jsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\nextjs-toploader\\dist\\index.js":{"id":"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\react-toastify\\dist\\ReactToastify.css":{"id":"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\flatpickr\\dist\\flatpickr.min.css":{"id":"(app-pages-browser)/./node_modules/flatpickr/dist/flatpickr.min.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\jsvectormap\\dist\\jsvectormap.css":{"id":"(app-pages-browser)/./node_modules/jsvectormap/dist/jsvectormap.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\app\\providers.jsx":{"id":"(app-pages-browser)/./src/app/providers.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\components\\Layouts\\header\\index.tsx":{"id":"(app-pages-browser)/./src/components/Layouts/header/index.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\components\\Layouts\\sidebar\\SidebarWrapper.jsx":{"id":"(app-pages-browser)/./src/components/Layouts/sidebar/SidebarWrapper.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\css\\satoshi.css":{"id":"(app-pages-browser)/./src/css/satoshi.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\css\\style.css":{"id":"(app-pages-browser)/./src/css/style.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\next\\dist\\esm\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\app\\(home)\\page.jsx":{"id":"(app-pages-browser)/./src/app/(home)/page.jsx","name":"*","chunks":["app/(home)/page","static/chunks/app/(home)/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\app\\services\\page.jsx":{"id":"(app-pages-browser)/./src/app/services/page.jsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\app\\auth\\login\\page.jsx":{"id":"(app-pages-browser)/./src/app/auth/login/page.jsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\app\\my-bookings\\page.jsx":{"id":"(app-pages-browser)/./src/app/my-bookings/page.jsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\app\\profile\\page.jsx":{"id":"(app-pages-browser)/./src/app/profile/page.jsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\":[],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\app\\(home)\\page":[],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\src\\app\\api\\auth\\login\\route":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js":{"*":{"id":"(rsc)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css":{"*":{"id":"(rsc)/./node_modules/react-toastify/dist/ReactToastify.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flatpickr/dist/flatpickr.min.css":{"*":{"id":"(rsc)/./node_modules/flatpickr/dist/flatpickr.min.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/jsvectormap/dist/jsvectormap.css":{"*":{"id":"(rsc)/./node_modules/jsvectormap/dist/jsvectormap.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.jsx":{"*":{"id":"(rsc)/./src/app/providers.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Layouts/header/index.tsx":{"*":{"id":"(rsc)/./src/components/Layouts/header/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Layouts/sidebar/SidebarWrapper.jsx":{"*":{"id":"(rsc)/./src/components/Layouts/sidebar/SidebarWrapper.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/css/satoshi.css":{"*":{"id":"(rsc)/./src/css/satoshi.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/css/style.css":{"*":{"id":"(rsc)/./src/css/style.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(home)/page.jsx":{"*":{"id":"(rsc)/./src/app/(home)/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/services/page.jsx":{"*":{"id":"(rsc)/./src/app/services/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.jsx":{"*":{"id":"(rsc)/./src/app/auth/login/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/my-bookings/page.jsx":{"*":{"id":"(rsc)/./src/app/my-bookings/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/profile/page.jsx":{"*":{"id":"(rsc)/./src/app/profile/page.jsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}