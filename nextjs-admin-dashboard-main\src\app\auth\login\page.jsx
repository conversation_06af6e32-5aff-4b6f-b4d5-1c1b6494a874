"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import AuthModal from '@/components/Auth/AuthModal';

export default function LoginPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [isModalOpen, setIsModalOpen] = useState(true);

  // Redirect if already authenticated
  useEffect(() => {
    if (!loading && user) {
      if (user.role === 'business_owner') {
        router.push('/business/dashboard');
      } else if (user.role === 'admin') {
        router.push('/admin/dashboard');
      } else {
        router.push('/');
      }
    }
  }, [user, loading, router]);

  const handleModalClose = () => {
    setIsModalOpen(false);
    router.push('/');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <AuthModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
      />

      {!isModalOpen && (
        <div className="text-center">
          <h1 className="text-heading-3 font-bold text-dark dark:text-white mb-4">
            Welcome to BookMyService
          </h1>
          <p className="text-body-sm text-dark-5 dark:text-dark-6 mb-6">
            Please log in to access your account.
          </p>
          <button
            onClick={() => setIsModalOpen(true)}
            className="inline-flex items-center justify-center rounded-lg bg-primary px-6 py-3 text-sm font-medium text-white hover:bg-blue-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
          >
            Open Login
          </button>
        </div>
      )}
    </div>
  );
}
