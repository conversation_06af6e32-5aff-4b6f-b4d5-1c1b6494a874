import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { apiResponse } from '@/lib/apiResponse';
import { verifyToken } from '@/lib/auth-utils';
import { sendEmail } from '@/lib/email';

export async function PATCH(request, { params }) {
  try {
    await connectDB();
    
    // Verify authentication and admin role
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return apiResponse.error('Authentication required', 401);
    }
    
    const decoded = verifyToken(token);
    if (!decoded || (decoded.role !== 'admin' && decoded.role !== 'super_admin')) {
      return apiResponse.error('Admin access required', 403);
    }
    
    const userId = params.id;
    const { status } = await request.json();
    
    // Validate status
    if (!['active', 'inactive'].includes(status)) {
      return apiResponse.error('Invalid status. Must be "active" or "inactive"', 400);
    }
    
    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return apiResponse.error('User not found', 404);
    }
    
    // Prevent admins from modifying super_admin users
    if (user.role === 'super_admin' && decoded.role !== 'super_admin') {
      return apiResponse.error('Insufficient privileges to modify super admin users', 403);
    }
    
    // Prevent users from deactivating themselves
    if (userId === decoded.userId) {
      return apiResponse.error('Cannot modify your own account status', 400);
    }
    
    // Update user status
    const isActive = status === 'active';
    user.isActive = isActive;
    user.updatedAt = new Date();
    
    await user.save();
    
    // Send email notification to user
    try {
      const statusMessage = isActive 
        ? 'Your account has been activated and you can now access all features.'
        : 'Your account has been temporarily deactivated. Please contact support if you have any questions.';
      
      await sendEmail({
        to: user.email,
        subject: `Account ${isActive ? 'Activated' : 'Deactivated'} - BookMyService`,
        html: `
          <h2>Account Status Update</h2>
          <p>Dear ${user.firstName},</p>
          <p>${statusMessage}</p>
          
          ${isActive ? `
            <p>You can now log in to your account and access all features.</p>
          ` : `
            <p>If you believe this is an error, please contact our support team.</p>
          `}
          
          <p>Best regards,<br>The BookMyService Team</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send status update email:', emailError);
      // Don't fail the request if email fails
    }
    
    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;
    
    return apiResponse.success(
      userResponse, 
      `User ${isActive ? 'activated' : 'deactivated'} successfully`
    );
    
  } catch (error) {
    console.error('Error updating user status:', error);
    return apiResponse.error('Failed to update user status', 500);
  }
}
