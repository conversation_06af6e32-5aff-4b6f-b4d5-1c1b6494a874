import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import BusinessOwner from '@/models/BusinessOwner';

export async function POST(request) {
  try {
    await connectDB();
    
    const testUsers = [
      {
        firstName: "John",
        lastName: "User",
        email: "<EMAIL>",
        password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
        role: "user",
        isEmailVerified: true,
        isActive: true
      },
      {
        firstName: "Admin",
        lastName: "User",
        email: "<EMAIL>",
        password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
        role: "admin",
        isEmailVerified: true,
        isActive: true
      }
    ];

    // Clear existing test users
    await User.deleteMany({
      email: { $in: ["<EMAIL>", "<EMAIL>"] }
    });
    await BusinessOwner.deleteMany({
      email: { $in: ["<EMAIL>"] }
    });

    // Create new test users
    const createdUsers = await User.insertMany(testUsers);

    // Create test business owner
    const testBusinessOwner = {
      ownerFirstName: "Jane",
      ownerLastName: "Business",
      email: "<EMAIL>",
      password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
      phoneNumber: "1234567890",
      businessName: "Jane's Cleaning Service",
      businessCategory: "Cleaning",
      businessDescription: "Professional cleaning services for homes and offices",
      businessAddress: "123 Main St",
      city: "New York",
      state: "NY",
      zipCode: "10001",
      country: "USA",
      role: "business_owner",
      isEmailVerified: true,
      isActive: true
    };

    const createdBusinessOwner = await BusinessOwner.create(testBusinessOwner);

    return NextResponse.json({
      success: true,
      message: 'Test users created successfully',
      data: {
        users: [
          ...createdUsers.map(user => ({
            email: user.email,
            role: user.role,
            name: `${user.firstName} ${user.lastName}`
          })),
          {
            email: createdBusinessOwner.email,
            role: createdBusinessOwner.role,
            name: `${createdBusinessOwner.ownerFirstName} ${createdBusinessOwner.ownerLastName}`
          }
        ]
      }
    });

  } catch (error) {
    console.error('Test users creation error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to create test users'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Test users endpoint',
    data: {
      instructions: 'Send a POST request to create test users',
      testAccounts: [
        { email: '<EMAIL>', role: 'user' },
        { email: '<EMAIL>', role: 'business_owner' },
        { email: '<EMAIL>', role: 'admin' }
      ]
    }
  });
}
