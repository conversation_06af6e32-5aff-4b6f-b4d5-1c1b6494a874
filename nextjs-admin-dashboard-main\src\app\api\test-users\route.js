import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Service from '@/models/Service';
import { hashPassword } from '@/lib/auth-utils';
import { apiResponse } from '@/lib/apiResponse';

export async function POST(request) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return apiResponse.error('Test users endpoint not available in production', 403);
    }

    await connectDB();

    // Check if test users already exist
    const existingUser = await User.findOne({ email: '<EMAIL>' });
    if (existingUser) {
      return apiResponse.success(null, 'Test users already exist');
    }

    // Hash the password
    const hashedPassword = await hashPassword("password123");

    const testUsers = [
      {
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "user",
        isEmailVerified: true,
        isActive: true
      },
      {
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "business_owner",
        businessName: "Smith Services",
        businessDescription: "Professional cleaning and maintenance services",
        businessAddress: "123 Business St, City, State 12345",
        businessPhone: "******-0123",
        isEmailVerified: true,
        isActive: true
      },
      {
        firstName: "Admin",
        lastName: "User",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "admin",
        isEmailVerified: true,
        isActive: true
      },
      {
        firstName: "Super",
        lastName: "Admin",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "super_admin",
        isEmailVerified: true,
        isActive: true
      }
    ];

    // Clear existing test users
    const testEmails = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"];
    await User.deleteMany({
      email: { $in: testEmails }
    });

    // Create new test users
    const createdUsers = await User.insertMany(testUsers);

    // Create test services for the business owner
    const businessOwner = createdUsers.find(user => user.role === 'business_owner');

    if (businessOwner) {
      const testServices = [
        {
          title: 'Professional House Cleaning',
          name: 'Professional House Cleaning',
          description: 'Complete house cleaning service including all rooms, kitchen, and bathrooms. We use eco-friendly products and professional equipment.',
          category: 'Cleaning',
          price: 120,
          businessOwner: businessOwner._id,
          images: ['https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=500'],
          tags: ['cleaning', 'house', 'professional', 'eco-friendly'],
          status: 'active',
          isActive: true
        },
        {
          title: 'Garden Maintenance',
          name: 'Garden Maintenance',
          description: 'Complete garden care including lawn mowing, hedge trimming, weeding, and seasonal planting.',
          category: 'Home & Garden',
          price: 80,
          businessOwner: businessOwner._id,
          images: ['https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=500'],
          tags: ['garden', 'lawn', 'maintenance', 'landscaping'],
          status: 'active',
          isActive: true
        },
        {
          title: 'Plumbing Repair',
          name: 'Plumbing Repair',
          description: 'Professional plumbing services for leaks, clogs, installations, and emergency repairs. Licensed and insured.',
          category: 'Repair & Maintenance',
          price: 95,
          businessOwner: businessOwner._id,
          images: ['https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=500'],
          tags: ['plumbing', 'repair', 'emergency', 'licensed'],
          status: 'active',
          isActive: true
        }
      ];

      await Service.insertMany(testServices);
    }

    return apiResponse.success({
      message: 'Test users and services created successfully!',
      users: [
        { email: '<EMAIL>', password: 'password123', role: 'user' },
        { email: '<EMAIL>', password: 'password123', role: 'business_owner' },
        { email: '<EMAIL>', password: 'password123', role: 'admin' },
        { email: '<EMAIL>', password: 'password123', role: 'super_admin' }
      ],
      servicesCreated: businessOwner ? 3 : 0
    }, 'Test data created successfully');

  } catch (error) {
    console.error('Test users creation error:', error);
    return apiResponse.error('Failed to create test users', 500);
  }
}

export async function GET() {
  try {
    await connectDB();

    const testEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
    const existingUsers = await User.find({ email: { $in: testEmails } }).select('email role firstName lastName businessName');

    return apiResponse.success({
      instructions: 'Send a POST request to create test users',
      existingTestUsers: existingUsers.map(user => ({
        email: user.email,
        role: user.role,
        name: user.role === 'business_owner' ? user.businessName : `${user.firstName} ${user.lastName}`
      })),
      availableCredentials: [
        { email: '<EMAIL>', password: 'password123', role: 'user' },
        { email: '<EMAIL>', password: 'password123', role: 'business_owner' },
        { email: '<EMAIL>', password: 'password123', role: 'admin' },
        { email: '<EMAIL>', password: 'password123', role: 'super_admin' }
      ]
    }, 'Test users endpoint');

  } catch (error) {
    console.error('Error fetching test users:', error);
    return apiResponse.error('Failed to fetch test users', 500);
  }
}
