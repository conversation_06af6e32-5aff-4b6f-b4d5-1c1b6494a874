import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function POST(request) {
  try {
    await connectDB();
    
    const testUsers = [
      {
        firstName: "John",
        lastName: "User",
        email: "<EMAIL>",
        role: "user",
        isEmailVerified: true,
        isActive: true
      },
      {
        firstName: "Jane",
        lastName: "Business",
        email: "<EMAIL>",
        role: "business_owner",
        businessName: "Jane's Cleaning Service",
        businessCategory: "Cleaning",
        businessDescription: "Professional cleaning services for homes and offices",
        isEmailVerified: true,
        isActive: true
      },
      {
        firstName: "Admin",
        lastName: "User",
        email: "<EMAIL>",
        role: "admin",
        isEmailVerified: true,
        isActive: true
      }
    ];

    // Clear existing test users
    await User.deleteMany({ 
      email: { $in: ["<EMAIL>", "<EMAIL>", "<EMAIL>"] } 
    });

    // Create new test users
    const createdUsers = await User.insertMany(testUsers);

    return NextResponse.json({
      success: true,
      message: 'Test users created successfully',
      data: {
        users: createdUsers.map(user => ({
          email: user.email,
          role: user.role,
          name: `${user.firstName} ${user.lastName}`
        }))
      }
    });

  } catch (error) {
    console.error('Test users creation error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to create test users'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Test users endpoint',
    data: {
      instructions: 'Send a POST request to create test users',
      testAccounts: [
        { email: '<EMAIL>', role: 'user' },
        { email: '<EMAIL>', role: 'business_owner' },
        { email: '<EMAIL>', role: 'admin' }
      ]
    }
  });
}
