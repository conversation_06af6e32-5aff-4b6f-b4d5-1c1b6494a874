import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { hashPassword, signToken } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();
    
    const { otp } = await request.json();
    const cookieStore = await cookies();

    // Get stored OTP and user data from cookies
    const storedOtp = cookieStore.get('otp')?.value;
    const userData = cookieStore.get('user_data')?.value;

    if (!storedOtp || !userData) {
      return NextResponse.json({
        success: false,
        message: 'Session expired. Please register again.'
      }, { status: 400 });
    }

    // Verify OTP
    if (otp !== storedOtp) {
      return NextResponse.json({
        success: false,
        message: 'Invalid OTP'
      }, { status: 400 });
    }

    // Parse user data
    const parsedUserData = JSON.parse(userData);
    
    // Hash password
    const hashedPassword = await hashPassword(parsedUserData.password);

    // Create user
    const user = await User.create({
      ...parsedUserData,
      password: hashedPassword,
      isEmailVerified: true,
      role: 'user',
    });

    // Generate JWT token
    const payload = {
      user: {
        id: user._id,
        role: user.role,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
      },
    };
    const token = signToken(payload);

    // Clear cookies
    const response = NextResponse.json({
      success: true,
      message: 'User registered successfully',
      data: { user, token }
    });

    response.cookies.delete('user_data');
    response.cookies.delete('otp');

    return response;

  } catch (error) {
    console.error('User Creation Error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to create user'
    }, { status: 500 });
  }
}
