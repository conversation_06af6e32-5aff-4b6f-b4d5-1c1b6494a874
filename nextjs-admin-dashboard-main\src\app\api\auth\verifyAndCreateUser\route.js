import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { hashPassword, signToken } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();
    
    const { otp } = await request.json();
    const cookieStore = await cookies();

    // Get stored OTP and user data from cookies
    const storedOtp = cookieStore.get('otp')?.value;
    const userData = cookieStore.get('user_data')?.value;
    const businessOwnerData = cookieStore.get('business_owner_data')?.value;

    const dataToUse = userData || businessOwnerData;
    const isBusinessOwner = !!businessOwnerData;

    console.log('OTP Verification:', {
      storedOtp,
      receivedOtp: otp,
      hasUserData: !!userData,
      hasBusinessData: !!businessOwnerData,
      isBusinessOwner
    });

    if (!storedOtp || !dataToUse) {
      return NextResponse.json({
        success: false,
        message: 'Session expired. Please register again.'
      }, { status: 400 });
    }

    // Verify OTP
    if (otp !== storedOtp) {
      return NextResponse.json({
        success: false,
        message: 'Invalid OTP'
      }, { status: 400 });
    }

    // Parse user data
    const parsedData = JSON.parse(dataToUse);

    let userCreateData;

    if (isBusinessOwner) {
      // Hash password
      const hashedPassword = await hashPassword(parsedData.personalInfo.password);

      // Create business owner user
      userCreateData = {
        firstName: parsedData.personalInfo.ownerFirstName,
        lastName: parsedData.personalInfo.ownerLastName,
        email: parsedData.personalInfo.email,
        password: hashedPassword,
        phoneNumber: parsedData.personalInfo.phoneNumber,
        businessName: parsedData.businessInfo.businessName,
        businessDescription: parsedData.businessInfo.businessDescription,
        businessAddress: parsedData.businessInfo.businessAddress,
        businessCategory: parsedData.businessInfo.businessCategory,
        city: parsedData.businessInfo.city,
        state: parsedData.businessInfo.state,
        zipCode: parsedData.businessInfo.zipCode,
        country: parsedData.businessInfo.country,
        businessPhone: parsedData.personalInfo.phoneNumber,
        isEmailVerified: true,
        role: 'business_owner',
        isActive: true,
      };
    } else {
      // Hash password
      const hashedPassword = await hashPassword(parsedData.password);

      // Create regular user
      userCreateData = {
        ...parsedData,
        password: hashedPassword,
        isEmailVerified: true,
        role: 'user',
        isActive: true,
      };
    }

    console.log('Creating user with data:', {
      email: userCreateData.email,
      role: userCreateData.role,
      businessName: userCreateData.businessName || 'N/A'
    });

    // Create user
    const user = await User.create(userCreateData);

    // Generate JWT token
    const payload = {
      user: {
        id: user._id,
        role: user.role,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
      },
    };
    const token = signToken(payload);

    console.log('User created successfully:', {
      id: user._id,
      email: user.email,
      role: user.role,
      businessName: user.businessName || 'N/A'
    });

    // Clear cookies
    const response = NextResponse.json({
      success: true,
      message: isBusinessOwner ? 'Business owner registered successfully' : 'User registered successfully',
      data: {
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          businessName: user.businessName,
          businessDescription: user.businessDescription,
          businessAddress: user.businessAddress,
          businessCategory: user.businessCategory,
          role: user.role,
        },
        token
      }
    });

    // Clear both possible cookies
    response.cookies.delete('user_data');
    response.cookies.delete('business_owner_data');
    response.cookies.delete('otp');

    return response;

  } catch (error) {
    console.error('User Creation Error:', error);

    // Handle specific validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json({
        success: false,
        message: `Validation failed: ${validationErrors.join(', ')}`
      }, { status: 400 });
    }

    // Handle duplicate key errors
    if (error.code === 11000) {
      return NextResponse.json({
        success: false,
        message: 'Email already exists'
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      message: 'Failed to create user account'
    }, { status: 500 });
  }
}
