{"c": ["app/layout", "webpack"], "r": ["app/profile/page"], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CBook%20my%20Service%20new%5C%5Cnextjs-admin-dashboard-main%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/profile/_components/icons.tsx", "(app-pages-browser)/./src/app/profile/_components/social-accounts.tsx", "(app-pages-browser)/./src/app/profile/page.tsx", "(app-pages-browser)/./src/components/Breadcrumbs/Breadcrumb.tsx"]}