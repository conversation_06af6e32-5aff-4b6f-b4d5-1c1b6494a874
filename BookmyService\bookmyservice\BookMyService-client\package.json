{"name": "bookmyservice", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "@tailwindcss/vite": "^4.0.0", "axios": "^1.8.4", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "framer-motion": "^12.0.6", "lucide-react": "^0.475.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-lottie": "^1.2.10", "react-router-dom": "^7.1.3", "react-spinners": "^0.17.0", "react-toastify": "^11.0.5", "stripe": "^18.0.0", "tailwindcss": "^4.0.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "vite": "^6.0.5"}}