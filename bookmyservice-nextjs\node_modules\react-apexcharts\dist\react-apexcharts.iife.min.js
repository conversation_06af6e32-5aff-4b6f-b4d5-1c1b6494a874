var ReactApexChart=function(e,r,t){"use strict";function n(e,r,t){return(r=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function o(){return o=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},o.apply(null,arguments)}function i(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,n)}return t}function u(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?i(Object(t),!0).forEach((function(r){n(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}var f=["type","width","height","series","options"];function s(e,r){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new WeakSet;if(e===r)return!0;if("object"!==c(e)||null===e||"object"!==c(r)||null===r)return!1;if(t.has(e)||t.has(r))return!0;t.add(e),t.add(r);var n=Object.keys(e),o=Object.keys(r);if(n.length!==o.length)return!1;for(var i=0,u=n;i<u.length;i++){var f=u[i];if(!o.includes(f)||!s(e[f],r[f],t))return!1}return!0}function a(t){var i=t.type,p=void 0===i?"line":i,l=t.width,y=void 0===l?"100%":l,b=t.height,h=void 0===b?"auto":b,v=t.series,O=t.options,d=function(e,r){if(null==e)return{};var t,n,o=function(e,r){if(null==e)return{};var t={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(r.includes(n))continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],r.includes(t)||{}.propertyIsEnumerable.call(e,t)&&(o[t]=e[t])}return o}(t,f),g=e.useRef(null),j=e.useRef(null),m=e.useRef();e.useEffect((function(){m.current=O;var e=g.current;return j.current=new r(e,E()),j.current.render(),function(){j.current&&"function"==typeof j.current.destroy&&j.current.destroy()}}),[]),e.useEffect((function(){var e=!s(j.current.w.config.series,v),r=!s(m.current,O)||h!==j.current.opts.chart.height||y!==j.current.opts.chart.width;(e||r)&&(e?r?j.current.updateOptions(E()):j.current.updateSeries(v):j.current.updateOptions(E())),m.current=O}),[O,v,h,y]);var w,P,S,E=function(){return k(O,{chart:{type:p,height:h,width:y},series:v})},R=function(e){return e&&"object"===c(e)&&!Array.isArray(e)},k=function(e,r){var t=u({},e);return R(e)&&R(r)&&Object.keys(r).forEach((function(o){R(r[o])&&o in e?t[o]=k(e[o],r[o]):Object.assign(t,n({},o,r[o]))})),t},T=(w=d,P=Object.keys(a.propTypes),S=u({},w),P.forEach((function(e){delete S[e]})),S);return e.createElement("div",o({ref:g},T))}return a.propTypes={type:t.string.isRequired,series:t.array.isRequired,options:t.object.isRequired,width:t.oneOfType([t.string,t.number]),height:t.oneOfType([t.string,t.number])},a}(React,ApexCharts,PropTypes);
