@import "tailwindcss";

@layer base {
  body {
    @apply bg-gray-2 text-dark-5 dark:bg-[#020D1A] dark:text-dark-6;
  }
}

/* React Toastify Styles */
.Toastify__toast-container {
  z-index: 9999;
}

.Toastify__toast {
  border-radius: 8px;
  font-family: inherit;
}

.Toastify__toast--success {
  background: #22AD5C;
}

.Toastify__toast--error {
  background: #F23030;
}

.Toastify__toast--info {
  background: #3C50E0;
}

.Toastify__toast--warning {
  background: #F59E0B;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
  background: #6B7280;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
