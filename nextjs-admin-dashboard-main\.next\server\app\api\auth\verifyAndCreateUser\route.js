/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/verifyAndCreateUser/route";
exports.ids = ["app/api/auth/verifyAndCreateUser/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute&page=%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute&page=%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Bhushan_patil_OneDrive_Desktop_Book_my_Service_new_nextjs_admin_dashboard_main_src_app_api_auth_verifyAndCreateUser_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/verifyAndCreateUser/route.js */ \"(rsc)/./src/app/api/auth/verifyAndCreateUser/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/verifyAndCreateUser/route\",\n        pathname: \"/api/auth/verifyAndCreateUser\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/verifyAndCreateUser/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Book my Service new\\\\nextjs-admin-dashboard-main\\\\src\\\\app\\\\api\\\\auth\\\\verifyAndCreateUser\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Bhushan_patil_OneDrive_Desktop_Book_my_Service_new_nextjs_admin_dashboard_main_src_app_api_auth_verifyAndCreateUser_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhdXRoJTJGdmVyaWZ5QW5kQ3JlYXRlVXNlciUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGYXV0aCUyRnZlcmlmeUFuZENyZWF0ZVVzZXIlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZhdXRoJTJGdmVyaWZ5QW5kQ3JlYXRlVXNlciUyRnJvdXRlLmpzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNCaHVzaGFuJTIwcGF0aWwlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNCb29rJTIwbXklMjBTZXJ2aWNlJTIwbmV3JTVDbmV4dGpzLWFkbWluLWRhc2hib2FyZC1tYWluJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNCaHVzaGFuJTIwcGF0aWwlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNCb29rJTIwbXklMjBTZXJ2aWNlJTIwbmV3JTVDbmV4dGpzLWFkbWluLWRhc2hib2FyZC1tYWluJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNpRztBQUM5SztBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcQmh1c2hhbiBwYXRpbFxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXEJvb2sgbXkgU2VydmljZSBuZXdcXFxcbmV4dGpzLWFkbWluLWRhc2hib2FyZC1tYWluXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGF1dGhcXFxcdmVyaWZ5QW5kQ3JlYXRlVXNlclxcXFxyb3V0ZS5qc1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYXV0aC92ZXJpZnlBbmRDcmVhdGVVc2VyL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYXV0aC92ZXJpZnlBbmRDcmVhdGVVc2VyXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9hdXRoL3ZlcmlmeUFuZENyZWF0ZVVzZXIvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxCaHVzaGFuIHBhdGlsXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcQm9vayBteSBTZXJ2aWNlIG5ld1xcXFxuZXh0anMtYWRtaW4tZGFzaGJvYXJkLW1haW5cXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYXV0aFxcXFx2ZXJpZnlBbmRDcmVhdGVVc2VyXFxcXHJvdXRlLmpzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute&page=%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/verifyAndCreateUser/route.js":
/*!*******************************************************!*\
  !*** ./src/app/api/auth/verifyAndCreateUser/route.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.js\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.js\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-utils */ \"(rsc)/./src/lib/auth-utils.js\");\n\n\n\n\n\nasync function POST(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const { otp } = await request.json();\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        // Get stored OTP and user data from cookies\n        const storedOtp = cookieStore.get('otp')?.value;\n        const userData = cookieStore.get('user_data')?.value;\n        const businessOwnerData = cookieStore.get('business_owner_data')?.value;\n        const dataToUse = userData || businessOwnerData;\n        const isBusinessOwner = !!businessOwnerData;\n        console.log('OTP Verification:', {\n            storedOtp,\n            receivedOtp: otp,\n            hasUserData: !!userData,\n            hasBusinessData: !!businessOwnerData,\n            isBusinessOwner\n        });\n        if (!storedOtp || !dataToUse) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Session expired. Please register again.'\n            }, {\n                status: 400\n            });\n        }\n        // Verify OTP\n        if (otp !== storedOtp) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid OTP'\n            }, {\n                status: 400\n            });\n        }\n        // Parse user data\n        const parsedData = JSON.parse(dataToUse);\n        let userCreateData;\n        if (isBusinessOwner) {\n            // Hash password\n            const hashedPassword = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.hashPassword)(parsedData.personalInfo.password);\n            // Create business owner user\n            userCreateData = {\n                firstName: parsedData.personalInfo.ownerFirstName,\n                lastName: parsedData.personalInfo.ownerLastName,\n                email: parsedData.personalInfo.email,\n                password: hashedPassword,\n                phoneNumber: parsedData.personalInfo.phoneNumber,\n                businessName: parsedData.businessInfo.businessName,\n                businessDescription: parsedData.businessInfo.businessDescription,\n                businessAddress: parsedData.businessInfo.businessAddress,\n                businessCategory: parsedData.businessInfo.businessCategory,\n                city: parsedData.businessInfo.city,\n                state: parsedData.businessInfo.state,\n                zipCode: parsedData.businessInfo.zipCode,\n                country: parsedData.businessInfo.country,\n                businessPhone: parsedData.personalInfo.phoneNumber,\n                isEmailVerified: true,\n                role: 'business_owner',\n                isActive: true\n            };\n        } else {\n            // Hash password\n            const hashedPassword = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.hashPassword)(parsedData.password);\n            // Create regular user\n            userCreateData = {\n                ...parsedData,\n                password: hashedPassword,\n                isEmailVerified: true,\n                role: 'user',\n                isActive: true\n            };\n        }\n        console.log('Creating user with data:', {\n            email: userCreateData.email,\n            role: userCreateData.role,\n            businessName: userCreateData.businessName || 'N/A'\n        });\n        // Create user\n        const user = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].create(userCreateData);\n        // Generate JWT token\n        const payload = {\n            user: {\n                id: user._id,\n                role: user.role,\n                firstName: user.firstName,\n                lastName: user.lastName,\n                email: user.email\n            }\n        };\n        const token = (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.signToken)(payload);\n        console.log('User created successfully:', {\n            id: user._id,\n            email: user.email,\n            role: user.role,\n            businessName: user.businessName || 'N/A'\n        });\n        // Clear cookies\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: isBusinessOwner ? 'Business owner registered successfully' : 'User registered successfully',\n            data: {\n                user: {\n                    id: user._id,\n                    firstName: user.firstName,\n                    lastName: user.lastName,\n                    email: user.email,\n                    businessName: user.businessName,\n                    businessDescription: user.businessDescription,\n                    businessAddress: user.businessAddress,\n                    businessCategory: user.businessCategory,\n                    role: user.role\n                },\n                token\n            }\n        });\n        // Clear both possible cookies\n        response.cookies.delete('user_data');\n        response.cookies.delete('business_owner_data');\n        response.cookies.delete('otp');\n        return response;\n    } catch (error) {\n        console.error('User Creation Error:', error);\n        // Handle specific validation errors\n        if (error.name === 'ValidationError') {\n            const validationErrors = Object.values(error.errors).map((err)=>err.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: `Validation failed: ${validationErrors.join(', ')}`\n            }, {\n                status: 400\n            });\n        }\n        // Handle duplicate key errors\n        if (error.code === 11000) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Email already exists'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to create user account'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/verifyAndCreateUser/route.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-utils.js":
/*!*******************************!*\
  !*** ./src/lib/auth-utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RESPONSE_CODE: () => (/* binding */ RESPONSE_CODE),\n/* harmony export */   RESPONSE_FAILURE: () => (/* binding */ RESPONSE_FAILURE),\n/* harmony export */   RESPONSE_SUCCESS: () => (/* binding */ RESPONSE_SUCCESS),\n/* harmony export */   comparePassword: () => (/* binding */ comparePassword),\n/* harmony export */   generateOtp: () => (/* binding */ generateOtp),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   sendOtpMail: () => (/* binding */ sendOtpMail),\n/* harmony export */   sendResponse: () => (/* binding */ sendResponse),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n\n\n\n// Generate random OTP\nfunction generateOtp() {\n    return Math.floor(100000 + Math.random() * 900000).toString();\n}\n// Hash password\nasync function hashPassword(password) {\n    const saltRounds = 10;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, saltRounds);\n}\n// Compare password\nasync function comparePassword(password, hashedPassword) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\n// Sign JWT token\nfunction signToken(payload) {\n    const secret = process.env.JWT_SECRET || 'your-secret-key';\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n        expiresIn: '7d'\n    });\n}\n// Verify JWT token\nfunction verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET || 'your-secret-key';\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n    } catch (error) {\n        console.error('Token verification error:', error.message);\n        return null;\n    }\n}\n// Create email transporter\nfunction createTransporter() {\n    return nodemailer__WEBPACK_IMPORTED_MODULE_2__.createTransport({\n        service: 'gmail',\n        auth: {\n            user: process.env.EMAIL_USER,\n            pass: process.env.EMAIL_PASS\n        }\n    });\n}\n// Send OTP email\nasync function sendOtpMail(email, firstName, lastName, otp) {\n    try {\n        // For development, just log the OTP\n        if (true) {\n            console.log(`OTP for ${email}: ${otp}`);\n            return;\n        }\n        const transporter = createTransporter();\n        const mailOptions = {\n            from: process.env.EMAIL_USER,\n            to: email,\n            subject: 'BookMyService - Email Verification',\n            html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h2 style=\"color: #3C50E0;\">BookMyService Email Verification</h2>\n          <p>Hello ${firstName},</p>\n          <p>Thank you for registering with BookMyService. Please use the following OTP to verify your email address:</p>\n          <div style=\"background-color: #f8f9fa; padding: 20px; text-align: center; margin: 20px 0;\">\n            <h1 style=\"color: #3C50E0; font-size: 32px; margin: 0;\">${otp}</h1>\n          </div>\n          <p>This OTP will expire in 5 minutes.</p>\n          <p>If you didn't request this verification, please ignore this email.</p>\n          <p>Best regards,<br>BookMyService Team</p>\n        </div>\n      `\n        };\n        await transporter.sendMail(mailOptions);\n        console.log('OTP email sent successfully');\n    } catch (error) {\n        console.error('Error sending OTP email:', error);\n        throw new Error('Failed to send OTP email');\n    }\n}\n// Send response helper\nfunction sendResponse(res, data, message, success, statusCode) {\n    return res.status(statusCode).json({\n        success,\n        message,\n        data\n    });\n}\n// Response constants\nconst RESPONSE_SUCCESS = true;\nconst RESPONSE_FAILURE = false;\nconst RESPONSE_CODE = {\n    SUCCESS: 200,\n    CREATED: 201,\n    BAD_REQUEST: 400,\n    UNAUTHORISED: 401,\n    FORBIDDEN: 403,\n    NOT_FOUND: 404,\n    INTERNAL_SERVER_ERROR: 500\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-utils.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.js":
/*!****************************!*\
  !*** ./src/lib/mongodb.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.js\n");

/***/ }),

/***/ "(rsc)/./src/models/User.js":
/*!****************************!*\
  !*** ./src/models/User.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    firstName: {\n        type: String,\n        required: [\n            true,\n            'The first name is required.'\n        ],\n        trim: true\n    },\n    lastName: {\n        type: String,\n        default: null,\n        trim: true\n    },\n    email: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    password: {\n        type: String,\n        default: null\n    },\n    phoneNumber: {\n        type: String,\n        unique: true,\n        sparse: true,\n        trim: true\n    },\n    avatar: {\n        type: String\n    },\n    role: {\n        type: String,\n        default: 'user',\n        enum: [\n            'user',\n            'business_owner',\n            'admin',\n            'super_admin'\n        ]\n    },\n    isVerified: {\n        type: Boolean,\n        default: false\n    },\n    isEmailVerified: {\n        type: Boolean,\n        default: false\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    // Business owner specific fields\n    businessName: {\n        type: String,\n        required: function() {\n            return this.role === 'business_owner';\n        },\n        trim: true\n    },\n    businessDescription: {\n        type: String,\n        trim: true\n    },\n    businessAddress: {\n        type: String,\n        trim: true\n    },\n    businessPhone: {\n        type: String,\n        trim: true\n    },\n    businessCategory: {\n        type: String,\n        trim: true\n    },\n    city: {\n        type: String,\n        trim: true\n    },\n    state: {\n        type: String,\n        trim: true\n    },\n    zipCode: {\n        type: String,\n        trim: true\n    },\n    country: {\n        type: String,\n        trim: true\n    },\n    bookedServiceIds: [\n        {\n            type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n            ref: 'Service'\n        }\n    ],\n    serviceOtp: {\n        otp: {\n            type: Number\n        },\n        expiresAt: {\n            type: Date\n        }\n    },\n    // Social login fields\n    googleId: {\n        type: String,\n        unique: true,\n        sparse: true\n    },\n    facebookId: {\n        type: String,\n        unique: true,\n        sparse: true\n    },\n    authProvider: {\n        type: String,\n        enum: [\n            'local',\n            'google',\n            'facebook'\n        ],\n        default: 'local'\n    },\n    // OTP for verification\n    otp: String,\n    otpExpires: Date\n}, {\n    timestamps: true\n});\n// Index for better query performance\nUserSchema.index({\n    email: 1\n});\nUserSchema.index({\n    role: 1\n});\nUserSchema.index({\n    googleId: 1\n});\nUserSchema.index({\n    facebookId: 1\n});\n// Instance methods\nUserSchema.methods.generateAuthToken = function() {\n    const jwt = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n    return jwt.sign({\n        uId: this._id,\n        role: this.role\n    }, process.env.JWT_SECRET || 'your-secret-key', {\n        expiresIn: \"24h\"\n    });\n};\nUserSchema.methods.toSafeObject = function() {\n    const userObject = this.toObject();\n    delete userObject.password;\n    delete userObject.otp;\n    delete userObject.otpExpires;\n    delete userObject.serviceOtp;\n    return userObject;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('User', UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbW9kZWxzL1VzZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBRWhDLE1BQU1DLGFBQWEsSUFBSUQsd0RBQWUsQ0FBQztJQUNyQ0csV0FBVztRQUNUQyxNQUFNQztRQUNOQyxVQUFVO1lBQUM7WUFBTTtTQUE4QjtRQUMvQ0MsTUFBTTtJQUNSO0lBQ0FDLFVBQVU7UUFDUkosTUFBTUM7UUFDTkksU0FBUztRQUNURixNQUFNO0lBQ1I7SUFDQUcsT0FBTztRQUNMTixNQUFNQztRQUNOQyxVQUFVO1FBQ1ZLLFFBQVE7UUFDUkMsV0FBVztRQUNYTCxNQUFNO0lBQ1I7SUFDQU0sVUFBVTtRQUNSVCxNQUFNQztRQUNOSSxTQUFTO0lBQ1g7SUFDQUssYUFBYTtRQUNYVixNQUFNQztRQUNOTSxRQUFRO1FBQ1JJLFFBQVE7UUFDUlIsTUFBTTtJQUNSO0lBQ0FTLFFBQVE7UUFDTlosTUFBTUM7SUFDUjtJQUNBWSxNQUFNO1FBQ0piLE1BQU1DO1FBQ05JLFNBQVM7UUFDVFMsTUFBTTtZQUFDO1lBQVE7WUFBa0I7WUFBUztTQUFjO0lBQzFEO0lBQ0FDLFlBQVk7UUFDVmYsTUFBTWdCO1FBQ05YLFNBQVM7SUFDWDtJQUNBWSxpQkFBaUI7UUFDZmpCLE1BQU1nQjtRQUNOWCxTQUFTO0lBQ1g7SUFDQWEsVUFBVTtRQUNSbEIsTUFBTWdCO1FBQ05YLFNBQVM7SUFDWDtJQUVBLGlDQUFpQztJQUNqQ2MsY0FBYztRQUNabkIsTUFBTUM7UUFDTkMsVUFBVTtZQUFhLE9BQU8sSUFBSSxDQUFDVyxJQUFJLEtBQUs7UUFBa0I7UUFDOURWLE1BQU07SUFDUjtJQUNBaUIscUJBQXFCO1FBQ25CcEIsTUFBTUM7UUFDTkUsTUFBTTtJQUNSO0lBQ0FrQixpQkFBaUI7UUFDZnJCLE1BQU1DO1FBQ05FLE1BQU07SUFDUjtJQUNBbUIsZUFBZTtRQUNidEIsTUFBTUM7UUFDTkUsTUFBTTtJQUNSO0lBQ0FvQixrQkFBa0I7UUFDaEJ2QixNQUFNQztRQUNORSxNQUFNO0lBQ1I7SUFDQXFCLE1BQU07UUFDSnhCLE1BQU1DO1FBQ05FLE1BQU07SUFDUjtJQUNBc0IsT0FBTztRQUNMekIsTUFBTUM7UUFDTkUsTUFBTTtJQUNSO0lBQ0F1QixTQUFTO1FBQ1AxQixNQUFNQztRQUNORSxNQUFNO0lBQ1I7SUFDQXdCLFNBQVM7UUFDUDNCLE1BQU1DO1FBQ05FLE1BQU07SUFDUjtJQUVBeUIsa0JBQWtCO1FBQUM7WUFDakI1QixNQUFNSix3REFBZSxDQUFDaUMsS0FBSyxDQUFDQyxRQUFRO1lBQ3BDQyxLQUFLO1FBQ1A7S0FBRTtJQUNGQyxZQUFZO1FBQ1ZDLEtBQUs7WUFBRWpDLE1BQU1rQztRQUFPO1FBQ3BCQyxXQUFXO1lBQUVuQyxNQUFNb0M7UUFBSztJQUMxQjtJQUNBLHNCQUFzQjtJQUN0QkMsVUFBVTtRQUNSckMsTUFBTUM7UUFDTk0sUUFBUTtRQUNSSSxRQUFRO0lBQ1Y7SUFDQTJCLFlBQVk7UUFDVnRDLE1BQU1DO1FBQ05NLFFBQVE7UUFDUkksUUFBUTtJQUNWO0lBQ0E0QixjQUFjO1FBQ1p2QyxNQUFNQztRQUNOYSxNQUFNO1lBQUM7WUFBUztZQUFVO1NBQVc7UUFDckNULFNBQVM7SUFDWDtJQUNBLHVCQUF1QjtJQUN2QjRCLEtBQUtoQztJQUNMdUMsWUFBWUo7QUFDZCxHQUFHO0lBQ0RLLFlBQVk7QUFDZDtBQUVBLHFDQUFxQztBQUNyQzVDLFdBQVc2QyxLQUFLLENBQUM7SUFBRXBDLE9BQU87QUFBRTtBQUM1QlQsV0FBVzZDLEtBQUssQ0FBQztJQUFFN0IsTUFBTTtBQUFFO0FBQzNCaEIsV0FBVzZDLEtBQUssQ0FBQztJQUFFTCxVQUFVO0FBQUU7QUFDL0J4QyxXQUFXNkMsS0FBSyxDQUFDO0lBQUVKLFlBQVk7QUFBRTtBQUVqQyxtQkFBbUI7QUFDbkJ6QyxXQUFXOEMsT0FBTyxDQUFDQyxpQkFBaUIsR0FBRztJQUNyQyxNQUFNQyxNQUFNQyxtQkFBT0EsQ0FBQyxnRUFBYztJQUNsQyxPQUFPRCxJQUFJRSxJQUFJLENBQ2I7UUFBRUMsS0FBSyxJQUFJLENBQUNDLEdBQUc7UUFBRXBDLE1BQU0sSUFBSSxDQUFDQSxJQUFJO0lBQUMsR0FDakNxQyxRQUFRQyxHQUFHLENBQUNDLFVBQVUsSUFBSSxtQkFDMUI7UUFDRUMsV0FBVztJQUNiO0FBRUo7QUFFQXhELFdBQVc4QyxPQUFPLENBQUNXLFlBQVksR0FBRztJQUNoQyxNQUFNQyxhQUFhLElBQUksQ0FBQ0MsUUFBUTtJQUNoQyxPQUFPRCxXQUFXOUMsUUFBUTtJQUMxQixPQUFPOEMsV0FBV3RCLEdBQUc7SUFDckIsT0FBT3NCLFdBQVdmLFVBQVU7SUFDNUIsT0FBT2UsV0FBV3ZCLFVBQVU7SUFDNUIsT0FBT3VCO0FBQ1Q7QUFFQSxpRUFBZTNELHdEQUFlLENBQUM4RCxJQUFJLElBQUk5RCxxREFBYyxDQUFDLFFBQVFDLFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmh1c2hhbiBwYXRpbFxcT25lRHJpdmVcXERlc2t0b3BcXEJvb2sgbXkgU2VydmljZSBuZXdcXG5leHRqcy1hZG1pbi1kYXNoYm9hcmQtbWFpblxcc3JjXFxtb2RlbHNcXFVzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gJ21vbmdvb3NlJztcblxuY29uc3QgVXNlclNjaGVtYSA9IG5ldyBtb25nb29zZS5TY2hlbWEoe1xuICBmaXJzdE5hbWU6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgcmVxdWlyZWQ6IFt0cnVlLCAnVGhlIGZpcnN0IG5hbWUgaXMgcmVxdWlyZWQuJ10sXG4gICAgdHJpbTogdHJ1ZSxcbiAgfSxcbiAgbGFzdE5hbWU6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgZGVmYXVsdDogbnVsbCxcbiAgICB0cmltOiB0cnVlLFxuICB9LFxuICBlbWFpbDoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICB1bmlxdWU6IHRydWUsXG4gICAgbG93ZXJjYXNlOiB0cnVlLFxuICAgIHRyaW06IHRydWUsXG4gIH0sXG4gIHBhc3N3b3JkOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIGRlZmF1bHQ6IG51bGwsXG4gIH0sXG4gIHBob25lTnVtYmVyOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHVuaXF1ZTogdHJ1ZSxcbiAgICBzcGFyc2U6IHRydWUsIC8vIEFsbG93IG11bHRpcGxlIG51bGwgdmFsdWVzIChmb3Igc29jaWFsIGxvZ2luIHVzZXJzIHdobyBkb24ndCBwcm92aWRlIHBob25lKVxuICAgIHRyaW06IHRydWUsXG4gIH0sXG4gIGF2YXRhcjoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgfSxcbiAgcm9sZToge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICBkZWZhdWx0OiAndXNlcicsXG4gICAgZW51bTogWyd1c2VyJywgJ2J1c2luZXNzX293bmVyJywgJ2FkbWluJywgJ3N1cGVyX2FkbWluJ10sXG4gIH0sXG4gIGlzVmVyaWZpZWQ6IHtcbiAgICB0eXBlOiBCb29sZWFuLFxuICAgIGRlZmF1bHQ6IGZhbHNlLFxuICB9LFxuICBpc0VtYWlsVmVyaWZpZWQ6IHtcbiAgICB0eXBlOiBCb29sZWFuLFxuICAgIGRlZmF1bHQ6IGZhbHNlLFxuICB9LFxuICBpc0FjdGl2ZToge1xuICAgIHR5cGU6IEJvb2xlYW4sXG4gICAgZGVmYXVsdDogdHJ1ZSxcbiAgfSxcblxuICAvLyBCdXNpbmVzcyBvd25lciBzcGVjaWZpYyBmaWVsZHNcbiAgYnVzaW5lc3NOYW1lOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHJlcXVpcmVkOiBmdW5jdGlvbigpIHsgcmV0dXJuIHRoaXMucm9sZSA9PT0gJ2J1c2luZXNzX293bmVyJzsgfSxcbiAgICB0cmltOiB0cnVlLFxuICB9LFxuICBidXNpbmVzc0Rlc2NyaXB0aW9uOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHRyaW06IHRydWUsXG4gIH0sXG4gIGJ1c2luZXNzQWRkcmVzczoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICB0cmltOiB0cnVlLFxuICB9LFxuICBidXNpbmVzc1Bob25lOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHRyaW06IHRydWUsXG4gIH0sXG4gIGJ1c2luZXNzQ2F0ZWdvcnk6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgdHJpbTogdHJ1ZSxcbiAgfSxcbiAgY2l0eToge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICB0cmltOiB0cnVlLFxuICB9LFxuICBzdGF0ZToge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICB0cmltOiB0cnVlLFxuICB9LFxuICB6aXBDb2RlOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHRyaW06IHRydWUsXG4gIH0sXG4gIGNvdW50cnk6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgdHJpbTogdHJ1ZSxcbiAgfSxcblxuICBib29rZWRTZXJ2aWNlSWRzOiBbe1xuICAgIHR5cGU6IG1vbmdvb3NlLlNjaGVtYS5UeXBlcy5PYmplY3RJZCxcbiAgICByZWY6ICdTZXJ2aWNlJ1xuICB9XSxcbiAgc2VydmljZU90cDoge1xuICAgIG90cDogeyB0eXBlOiBOdW1iZXIgfSxcbiAgICBleHBpcmVzQXQ6IHsgdHlwZTogRGF0ZSB9LFxuICB9LFxuICAvLyBTb2NpYWwgbG9naW4gZmllbGRzXG4gIGdvb2dsZUlkOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHVuaXF1ZTogdHJ1ZSxcbiAgICBzcGFyc2U6IHRydWUsIC8vIEFsbG93IG11bHRpcGxlIG51bGwgdmFsdWVzXG4gIH0sXG4gIGZhY2Vib29rSWQ6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgdW5pcXVlOiB0cnVlLFxuICAgIHNwYXJzZTogdHJ1ZSwgLy8gQWxsb3cgbXVsdGlwbGUgbnVsbCB2YWx1ZXNcbiAgfSxcbiAgYXV0aFByb3ZpZGVyOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIGVudW06IFsnbG9jYWwnLCAnZ29vZ2xlJywgJ2ZhY2Vib29rJ10sXG4gICAgZGVmYXVsdDogJ2xvY2FsJ1xuICB9LFxuICAvLyBPVFAgZm9yIHZlcmlmaWNhdGlvblxuICBvdHA6IFN0cmluZyxcbiAgb3RwRXhwaXJlczogRGF0ZVxufSwge1xuICB0aW1lc3RhbXBzOiB0cnVlLFxufSk7XG5cbi8vIEluZGV4IGZvciBiZXR0ZXIgcXVlcnkgcGVyZm9ybWFuY2VcblVzZXJTY2hlbWEuaW5kZXgoeyBlbWFpbDogMSB9KTtcblVzZXJTY2hlbWEuaW5kZXgoeyByb2xlOiAxIH0pO1xuVXNlclNjaGVtYS5pbmRleCh7IGdvb2dsZUlkOiAxIH0pO1xuVXNlclNjaGVtYS5pbmRleCh7IGZhY2Vib29rSWQ6IDEgfSk7XG5cbi8vIEluc3RhbmNlIG1ldGhvZHNcblVzZXJTY2hlbWEubWV0aG9kcy5nZW5lcmF0ZUF1dGhUb2tlbiA9IGZ1bmN0aW9uICgpIHtcbiAgY29uc3Qgand0ID0gcmVxdWlyZSgnanNvbndlYnRva2VuJyk7XG4gIHJldHVybiBqd3Quc2lnbihcbiAgICB7IHVJZDogdGhpcy5faWQsIHJvbGU6IHRoaXMucm9sZSB9LFxuICAgIHByb2Nlc3MuZW52LkpXVF9TRUNSRVQgfHwgJ3lvdXItc2VjcmV0LWtleScsXG4gICAge1xuICAgICAgZXhwaXJlc0luOiBcIjI0aFwiLFxuICAgIH1cbiAgKTtcbn07XG5cblVzZXJTY2hlbWEubWV0aG9kcy50b1NhZmVPYmplY3QgPSBmdW5jdGlvbigpIHtcbiAgY29uc3QgdXNlck9iamVjdCA9IHRoaXMudG9PYmplY3QoKTtcbiAgZGVsZXRlIHVzZXJPYmplY3QucGFzc3dvcmQ7XG4gIGRlbGV0ZSB1c2VyT2JqZWN0Lm90cDtcbiAgZGVsZXRlIHVzZXJPYmplY3Qub3RwRXhwaXJlcztcbiAgZGVsZXRlIHVzZXJPYmplY3Quc2VydmljZU90cDtcbiAgcmV0dXJuIHVzZXJPYmplY3Q7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBtb25nb29zZS5tb2RlbHMuVXNlciB8fCBtb25nb29zZS5tb2RlbCgnVXNlcicsIFVzZXJTY2hlbWEpO1xuIl0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiVXNlclNjaGVtYSIsIlNjaGVtYSIsImZpcnN0TmFtZSIsInR5cGUiLCJTdHJpbmciLCJyZXF1aXJlZCIsInRyaW0iLCJsYXN0TmFtZSIsImRlZmF1bHQiLCJlbWFpbCIsInVuaXF1ZSIsImxvd2VyY2FzZSIsInBhc3N3b3JkIiwicGhvbmVOdW1iZXIiLCJzcGFyc2UiLCJhdmF0YXIiLCJyb2xlIiwiZW51bSIsImlzVmVyaWZpZWQiLCJCb29sZWFuIiwiaXNFbWFpbFZlcmlmaWVkIiwiaXNBY3RpdmUiLCJidXNpbmVzc05hbWUiLCJidXNpbmVzc0Rlc2NyaXB0aW9uIiwiYnVzaW5lc3NBZGRyZXNzIiwiYnVzaW5lc3NQaG9uZSIsImJ1c2luZXNzQ2F0ZWdvcnkiLCJjaXR5Iiwic3RhdGUiLCJ6aXBDb2RlIiwiY291bnRyeSIsImJvb2tlZFNlcnZpY2VJZHMiLCJUeXBlcyIsIk9iamVjdElkIiwicmVmIiwic2VydmljZU90cCIsIm90cCIsIk51bWJlciIsImV4cGlyZXNBdCIsIkRhdGUiLCJnb29nbGVJZCIsImZhY2Vib29rSWQiLCJhdXRoUHJvdmlkZXIiLCJvdHBFeHBpcmVzIiwidGltZXN0YW1wcyIsImluZGV4IiwibWV0aG9kcyIsImdlbmVyYXRlQXV0aFRva2VuIiwiand0IiwicmVxdWlyZSIsInNpZ24iLCJ1SWQiLCJfaWQiLCJwcm9jZXNzIiwiZW52IiwiSldUX1NFQ1JFVCIsImV4cGlyZXNJbiIsInRvU2FmZU9iamVjdCIsInVzZXJPYmplY3QiLCJ0b09iamVjdCIsIm1vZGVscyIsIlVzZXIiLCJtb2RlbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/nodemailer","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute&page=%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2FverifyAndCreateUser%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CBook%20my%20Service%20new%5Cnextjs-admin-dashboard-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();