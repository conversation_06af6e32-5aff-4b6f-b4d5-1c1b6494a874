/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nextjs-toploader";
exports.ids = ["vendor-chunks/nextjs-toploader"];
exports.modules = {

/***/ "(ssr)/./node_modules/nextjs-toploader/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/nextjs-toploader/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar J = Object.create;\nvar y = Object.defineProperty;\nvar X = Object.getOwnPropertyDescriptor;\nvar _ = Object.getOwnPropertyNames;\nvar D = Object.getPrototypeOf, G = Object.prototype.hasOwnProperty;\nvar a = (r, o)=>y(r, \"name\", {\n        value: o,\n        configurable: !0\n    });\nvar Q = (r, o)=>{\n    for(var i in o)y(r, i, {\n        get: o[i],\n        enumerable: !0\n    });\n}, M = (r, o, i, g)=>{\n    if (o && typeof o == \"object\" || typeof o == \"function\") for (let c of _(o))!G.call(r, c) && c !== i && y(r, c, {\n        get: ()=>o[c],\n        enumerable: !(g = X(o, c)) || g.enumerable\n    });\n    return r;\n};\nvar N = (r, o, i)=>(i = r != null ? J(D(r)) : {}, M(o || !r || !r.__esModule ? y(i, \"default\", {\n        value: r,\n        enumerable: !0\n    }) : i, r)), V = (r)=>M(y({}, \"__esModule\", {\n        value: !0\n    }), r);\nvar Z = {};\nQ(Z, {\n    default: ()=>Y\n});\nmodule.exports = V(Z);\nvar t = N(__webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\")), v = N(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\")), s = N(__webpack_require__(/*! nprogress */ \"(ssr)/./node_modules/nprogress/nprogress.js\"));\nvar O = a(({ color: r, height: o, showSpinner: i, crawl: g, crawlSpeed: c, initialPosition: L, easing: T, speed: E, shadow: x, template: k, zIndex: H = 1600, showAtBottom: S = !1, showForHashAnchor: z = !0 })=>{\n    let C = \"#29d\", m = r != null ? r : C, K = o != null ? o : 3, W = !x && x !== void 0 ? \"\" : x ? `box-shadow:${x}` : `box-shadow:0 0 10px ${m},0 0 5px ${m}`, j = v.createElement(\"style\", null, `#nprogress{pointer-events:none}#nprogress .bar{background:${m};position:fixed;z-index:${H};${S ? \"bottom: 0;\" : \"top: 0;\"}left:0;width:100%;height:${K}px}#nprogress .peg{display:block;position:absolute;right:0;width:100px;height:100%;${W};opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translate(0px,-4px)}#nprogress .spinner{display:block;position:fixed;z-index:${H};${S ? \"bottom: 15px;\" : \"top: 15px;\"}right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:2px solid transparent;border-top-color:${m};border-left-color:${m};border-radius:50%;-webkit-animation:nprogress-spinner 400ms linear infinite;animation:nprogress-spinner 400ms linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .bar,.nprogress-custom-parent #nprogress .spinner{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}`), u = a((h)=>new URL(h, window.location.href).href, \"toAbsoluteURL\"), B = a((h, f)=>{\n        let l = new URL(u(h)), b = new URL(u(f));\n        return l.href.split(\"#\")[0] === b.href.split(\"#\")[0];\n    }, \"isHashAnchor\"), F = a((h, f)=>{\n        let l = new URL(u(h)), b = new URL(u(f));\n        return l.hostname.replace(/^www\\./, \"\") === b.hostname.replace(/^www\\./, \"\");\n    }, \"isSameHostName\");\n    return v.useEffect({\n        \"O.useEffect\": ()=>{\n            s.configure({\n                showSpinner: i != null ? i : !0,\n                trickle: g != null ? g : !0,\n                trickleSpeed: c != null ? c : 200,\n                minimum: L != null ? L : .08,\n                easing: T != null ? T : \"ease\",\n                speed: E != null ? E : 200,\n                template: k != null ? k : '<div class=\"bar\" role=\"bar\"><div class=\"peg\"></div></div><div class=\"spinner\" role=\"spinner\"><div class=\"spinner-icon\"></div></div>'\n            });\n            function h(e, d) {\n                let n = new URL(e), p = new URL(d);\n                if (n.hostname === p.hostname && n.pathname === p.pathname && n.search === p.search) {\n                    let w = n.hash, P = p.hash;\n                    return w !== P && n.href.replace(w, \"\") === p.href.replace(P, \"\");\n                }\n                return !1;\n            }\n            a(h, \"isAnchorOfCurrentUrl\");\n            var f = document.querySelectorAll(\"html\");\n            let l = a({\n                \"O.useEffect.l\": ()=>f.forEach({\n                        \"O.useEffect.l\": (e)=>e.classList.remove(\"nprogress-busy\")\n                    }[\"O.useEffect.l\"])\n            }[\"O.useEffect.l\"], \"removeNProgressClass\");\n            function b(e) {\n                for(; e && e.tagName.toLowerCase() !== \"a\";)e = e.parentElement;\n                return e;\n            }\n            a(b, \"findClosestAnchor\");\n            function A(e) {\n                try {\n                    let d = e.target, n = b(d), p = n == null ? void 0 : n.href;\n                    if (p) {\n                        let w = window.location.href, P = n.target === \"_blank\", q = [\n                            \"tel:\",\n                            \"mailto:\",\n                            \"sms:\",\n                            \"blob:\",\n                            \"download:\"\n                        ].some({\n                            \"O.useEffect.A.q\": (I)=>p.startsWith(I)\n                        }[\"O.useEffect.A.q\"]);\n                        if (!F(window.location.href, n.href)) return;\n                        let $ = h(w, p) || B(window.location.href, n.href);\n                        if (!z && $) return;\n                        p === w || P || q || $ || e.ctrlKey || e.metaKey || e.shiftKey || e.altKey || !u(n.href).startsWith(\"http\") ? (s.start(), s.done(), l()) : s.start();\n                    }\n                } catch (d) {\n                    s.start(), s.done();\n                }\n            }\n            a(A, \"handleClick\"), ({\n                \"O.useEffect\": (e)=>{\n                    let d = e.pushState;\n                    e.pushState = ({\n                        \"O.useEffect\": (...n)=>(s.done(), l(), d.apply(e, n))\n                    })[\"O.useEffect\"];\n                }\n            })[\"O.useEffect\"](window.history), ({\n                \"O.useEffect\": (e)=>{\n                    let d = e.replaceState;\n                    e.replaceState = ({\n                        \"O.useEffect\": (...n)=>(s.done(), l(), d.apply(e, n))\n                    })[\"O.useEffect\"];\n                }\n            })[\"O.useEffect\"](window.history);\n            function R() {\n                s.done(), l();\n            }\n            a(R, \"handlePageHide\");\n            function U() {\n                s.done();\n            }\n            return a(U, \"handleBackAndForth\"), window.addEventListener(\"popstate\", U), document.addEventListener(\"click\", A), window.addEventListener(\"pagehide\", R), ({\n                \"O.useEffect\": ()=>{\n                    document.removeEventListener(\"click\", A), window.removeEventListener(\"pagehide\", R), window.removeEventListener(\"popstate\", U);\n                }\n            })[\"O.useEffect\"];\n        }\n    }[\"O.useEffect\"], []), j;\n}, \"NextTopLoader\"), Y = O;\nO.propTypes = {\n    color: t.string,\n    height: t.number,\n    showSpinner: t.bool,\n    crawl: t.bool,\n    crawlSpeed: t.number,\n    initialPosition: t.number,\n    easing: t.string,\n    speed: t.number,\n    template: t.string,\n    shadow: t.oneOfType([\n        t.string,\n        t.bool\n    ]),\n    zIndex: t.number,\n    showAtBottom: t.bool\n}; /**\n *\n * NextTopLoader\n * @license MIT\n * @param {NextTopLoaderProps} props The properties to configure NextTopLoader\n * @returns {React.JSX.Element}\n *\n */  //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nextjs-toploader/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/nextjs-toploader/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/nextjs-toploader/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js")

module.exports = createProxy("C:\\Users\\<USER>\\OneDrive\\Desktop\\Book my Service new\\nextjs-admin-dashboard-main\\node_modules\\nextjs-toploader\\dist\\index.js")


/***/ })

};
;