import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { comparePassword, signToken } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();

    const { email, password } = await request.json();
    console.log('Business Owner Login attempt:', { email, password: '***' });

    // Find business owner by email and role
    const businessOwner = await User.findOne({ email, role: 'business_owner' });
    console.log('Business Owner found:', businessOwner ? 'Yes' : 'No');
    if (!businessOwner) {
      return NextResponse.json({
        success: false,
        message: 'Invalid email or password'
      }, { status: 401 });
    }

    // Check password
    console.log('Comparing passwords...');
    const isPasswordValid = await comparePassword(password, businessOwner.password);
    console.log('Password valid:', isPasswordValid);
    if (!isPasswordValid) {
      return NextResponse.json({
        success: false,
        message: 'Invalid email or password'
      }, { status: 401 });
    }

    // Generate JWT token
    const payload = {
      userId: businessOwner._id,
      email: businessOwner.email,
      role: businessOwner.role,
    };
    const token = signToken(payload);

    return NextResponse.json({
      success: true,
      message: 'Business Owner logged in successfully',
      data: {
        user: {
          id: businessOwner._id,
          firstName: businessOwner.firstName,
          lastName: businessOwner.lastName,
          email: businessOwner.email,
          businessName: businessOwner.businessName,
          role: businessOwner.role,
        },
        token
      }
    });

  } catch (error) {
    console.error('Business Owner Login Error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to login'
    }, { status: 500 });
  }
}
