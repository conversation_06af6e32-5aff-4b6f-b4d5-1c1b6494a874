import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import BusinessOwner from '@/models/BusinessOwner';
import { comparePassword, signToken } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();

    const { email, password } = await request.json();
    console.log('Business Owner Login attempt:', { email, password: '***' });

    // Find business owner by email
    const businessOwner = await BusinessOwner.findOne({ email });
    console.log('Business Owner found:', businessOwner ? 'Yes' : 'No');
    if (!businessOwner) {
      return NextResponse.json({
        success: false,
        message: 'Invalid email or password'
      }, { status: 401 });
    }

    // Check password
    console.log('Comparing passwords...');
    const isPasswordValid = await comparePassword(password, businessOwner.password);
    console.log('Password valid:', isPasswordValid);
    if (!isPasswordValid) {
      return NextResponse.json({
        success: false,
        message: 'Invalid email or password'
      }, { status: 401 });
    }

    // Generate JWT token
    const payload = {
      businessOwner: {
        id: businessOwner._id,
        role: businessOwner.role,
        ownerFirstName: businessOwner.ownerFirstName,
        ownerLastName: businessOwner.ownerLastName,
        email: businessOwner.email,
      },
    };
    const token = signToken(payload);

    return NextResponse.json({
      success: true,
      message: 'Business Owner logged in successfully',
      data: { businessOwner, token }
    });

  } catch (error) {
    console.error('Business Owner Login Error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to login'
    }, { status: 500 });
  }
}
