// import { NextResponse } from 'next/server';
// import connectDB from '@/lib/mongodb';
// import User from '@/models/User';
// import { generateOtp, sendOtpMail } from '@/lib/auth-utils';

// export async function POST(request) {
//   try {
//     await connectDB();
    
//     const {
//       ownerFirstName,
//       ownerLastName,
//       email,
//       password,
//       phoneNumber,
//       businessName,
//       businessCategory,
//       businessDescription,
//       businessAddress,
//       city,
//       state,
//       zipCode,
//       country,
//     } = await request.json();

//     // Check if user already exists
//     const existingUser = await User.findOne({ email });
//     if (existingUser) {
//       return NextResponse.json({
//         success: false,
//         message: 'Email already exists'
//       }, { status: 400 });
//     }

//     // Generate OTP
//     const otp = generateOtp();
    
//     // Send OTP email
//     await sendOtpMail(email, ownerFirstName, '', otp);

//     // Store business owner data and OTP in cookies (temporary storage)
//     const response = NextResponse.json({
//       success: true,
//       message: 'OTP sent successfully'
//     });

//     // Set cookies with business owner data and OTP
//     response.cookies.set('business_owner_data', JSON.stringify({
//       personalInfo: {
//         ownerFirstName,
//         ownerLastName,
//         email,
//         password,
//         phoneNumber,
//       },
//       businessInfo: {
//         businessName,
//         businessCategory,
//         businessDescription,
//         businessAddress,
//         city,
//         state,
//         zipCode,
//         country,
//       },
//     }), {
//       httpOnly: true,
//       secure: process.env.NODE_ENV === 'production',
//       maxAge: 5 * 60 * 1000, // 5 minutes
//       sameSite: 'strict'
//     });

//     response.cookies.set('otp', otp, {
//       httpOnly: true,
//       secure: process.env.NODE_ENV === 'production',
//       maxAge: 5 * 60 * 1000, // 5 minutes
//       sameSite: 'strict'
//     });

//     return response;

//   } catch (error) {
//     console.error('Register Business Owner Error:', error);
//     return NextResponse.json({
//       success: false,
//       message: 'Failed to register business owner'
//     }, { status: 500 });
//   }
// }
import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import BusinessOwner from '@/models/BusinessOwner'; // Use BusinessOwner model
import { generateOtp, sendOtpMail } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    await connectDB();
    
    const {
      ownerFirstName,
      ownerLastName,
      email,
      password,
      phoneNumber,
      businessName,
      businessCategory,
      businessDescription,
      businessAddress,
      city,
      state,
      zipCode,
      country,
    } = await request.json();

    const existingBusinessOwner = await BusinessOwner.findOne({ email });
    if (existingBusinessOwner) {
      return NextResponse.json({
        success: false,
        message: 'Email already exists'
      }, { status: 400 });
    }

    const otp = generateOtp();
    await sendOtpMail(email, ownerFirstName, '', otp);

    const response = NextResponse.json({
      success: true,
      message: 'OTP sent successfully'
    });

    response.cookies.set('business_owner_data', JSON.stringify({
      personalInfo: {
        ownerFirstName,
        ownerLastName,
        email,
        password,
        phoneNumber,
      },
      businessInfo: {
        businessName,
        businessCategory,
        businessDescription,
        businessAddress,
        city,
        state,
        zipCode,
        country,
      },
    }), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 5 * 60 * 1000, // 5 minutes
      sameSite: 'strict'
    });

    response.cookies.set('otp', otp, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 5 * 60 * 1000, // 5 minutes
      sameSite: 'strict'
    });

    return response;
  } catch (error) {
    console.error('Register Business Owner Error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to register business owner'
    }, { status: 500 });
  }
}